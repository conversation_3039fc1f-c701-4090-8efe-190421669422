# fx_analysis.py
# -*- coding: utf-8 -*-
import pandas as pd
from datetime import datetime
from typing import List
from czsc.objects import RawBar, NewBar, FX, Mark
from czsc.analyze import remove_include  # 假设已经将analyze.py放在可导入路径
from czsc.utils.echarts_plot import kline_pro

def csv_to_rawbars(file_path: str, symbol: str, freq: str) -> List[RawBar]:
    """将CSV数据转换为RawBar对象列表
    
    :param file_path: CSV文件路径
    :param symbol: 标的代码
    :param freq: K线周期
    :return: RawBar列表
    """
    df = pd.read_csv(file_path, parse_dates=['Date'])
    raw_bars = []
    for idx, row in df.iterrows():
        bar = RawBar(
            symbol=symbol,
            dt=row['Date'],
            id=idx,
            freq=freq,
            open=row['Open'],
            close=row['Close'],
            high=row['High'],
            low=row['Low'],
            vol=row['Volume'],
            amount=row['Amount']
        )
        raw_bars.append(bar)
    return raw_bars

def process_include_bars(raw_bars: List[RawBar]) -> List[NewBar]:
    """处理包含关系生成标准K线序列"""
    if len(raw_bars) < 3:
        return []
    
    # 初始化前两根K线（假设前两根无包含关系）
    k1 = NewBar(**raw_bars[0].__dict__, elements=[raw_bars[0]])
    k2 = NewBar(**raw_bars[1].__dict__, elements=[raw_bars[1]])
    new_bars = [k1, k2]
    
    # 从第三根K线开始处理
    for bar in raw_bars[2:]:
        has_include, new_k = remove_include(new_bars[-2], new_bars[-1], bar)
        if has_include:
            new_bars[-1] = new_k  # 替换最后一根K线
        else:
            new_bars.append(new_k)
    return new_bars

def find_fractals(new_bars: List[NewBar]) -> List[FX]:
    """识别顶底分型"""
    fractals = []
    for i in range(1, len(new_bars)-1):
        left = new_bars[i-1]
        mid = new_bars[i]
        right = new_bars[i+1]
        
        # 顶分型条件
        if mid.high > left.high and mid.high > right.high:
            fractals.append(FX(symbol=mid.symbol, dt=mid.dt, 
                              mark=Mark.DING, high=mid.high, low=mid.low))
        
        # 底分型条件
        elif mid.low < left.low and mid.low < right.low:
            fractals.append(FX(symbol=mid.symbol, dt=mid.dt,
                              mark=Mark.DI, high=mid.high, low=mid.low))
    return fractals

def plot_with_fractals(new_bars: List[NewBar], fractals: List[FX]):
    """绘制带分型的K线图"""
    kline_data = [{
        'dt': bar.dt.strftime("%Y-%m-%d"),
        'open': bar.open,
        'close': bar.close,
        'high': bar.high,
        'low': bar.low
    } for bar in new_bars]
    
    annotations = []
    for fx in fractals:
        annotations.append({
            'dt': fx.dt.strftime("%Y-%m-%d"),
            'value': fx.high if fx.mark == Mark.DING else fx.low,
            'mark': fx.mark.value,
            'marker': 'triangle' if fx.mark == Mark.DING else 'circle',
            'color': '#FF0000' if fx.mark == Mark.DING else '#00FF00'
        })
    
    kline_pro(kline_data, annotations=annotations, title="股票分型分析")

if __name__ == '__main__':
    # 配置参数
    csv_path = "D:/czsc-master/stock_a.csv"
    symbol = "000001.SH"
    freq = "日线"
    
    # 数据处理流程
    raw_bars = csv_to_rawbars(csv_path, symbol, freq)
    new_bars = process_include_bars(raw_bars)
    fractals = find_fractals(new_bars)
    
    # 可视化展示
    plot_with_fractals(new_bars, fractals)
