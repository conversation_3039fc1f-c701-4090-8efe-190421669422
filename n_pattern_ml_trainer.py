#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
N字形态机器学习训练器
用于训练识别N字形态的机器学习模型
"""

import os
import numpy as np
import pandas as pd
import cv2
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import json
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class NPatternFeatureExtractor:
    """N字形态特征提取器"""

    def __init__(self):
        self.scaler = StandardScaler()

    def extract_price_features(self, prices: np.array, window_size: int = 50) -> Dict:
        """提取价格序列特征"""
        if len(prices) < window_size:
            # 如果数据不够，进行插值
            from scipy.interpolate import interp1d
            x_old = np.linspace(0, 1, len(prices))
            x_new = np.linspace(0, 1, window_size)
            f = interp1d(x_old, prices, kind='linear')
            prices = f(x_new)
        elif len(prices) > window_size:
            # 如果数据太多，进行下采样
            indices = np.linspace(0, len(prices)-1, window_size, dtype=int)
            prices = prices[indices]

        # 归一化价格到[0,1]区间
        min_price = np.min(prices)
        max_price = np.max(prices)
        if max_price > min_price:
            normalized_prices = (prices - min_price) / (max_price - min_price)
        else:
            normalized_prices = np.zeros_like(prices)

        features = {}

        # 1. 基本统计特征
        features['mean'] = np.mean(normalized_prices)
        features['std'] = np.std(normalized_prices)
        features['skewness'] = self._calculate_skewness(normalized_prices)
        features['kurtosis'] = self._calculate_kurtosis(normalized_prices)

        # 2. 趋势特征
        features['overall_trend'] = (normalized_prices[-1] - normalized_prices[0])
        features['max_drawdown'] = self._calculate_max_drawdown(normalized_prices)
        features['volatility'] = np.std(np.diff(normalized_prices))

        # 3. 形态特征
        peaks, troughs = self._find_peaks_troughs(normalized_prices)
        features['peak_count'] = len(peaks)
        features['trough_count'] = len(troughs)

        # 4. N字形态特定特征
        n_features = self._extract_n_pattern_features(normalized_prices, peaks, troughs)
        features.update(n_features)

        # 5. 技术指标特征
        tech_features = self._calculate_technical_indicators(normalized_prices)
        features.update(tech_features)

        return features

    def extract_image_features(self, image_path: str) -> Dict:
        """从图像中提取特征"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)

            # 霍夫变换检测直线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=30, maxLineGap=10)

            features = {}

            if lines is not None:
                # 线段特征
                slopes = []
                lengths = []

                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    if x2 != x1:
                        slope = (y2 - y1) / (x2 - x1)
                        slopes.append(slope)
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    lengths.append(length)

                features['line_count'] = len(lines)
                features['avg_slope'] = np.mean(slopes) if slopes else 0
                features['slope_std'] = np.std(slopes) if slopes else 0
                features['avg_length'] = np.mean(lengths)
                features['length_std'] = np.std(lengths)

                # N字形态检测
                n_score = self._detect_n_pattern_in_lines(lines, gray.shape)
                features['n_pattern_score'] = n_score
            else:
                features.update({
                    'line_count': 0, 'avg_slope': 0, 'slope_std': 0,
                    'avg_length': 0, 'length_std': 0, 'n_pattern_score': 0
                })

            # 图像纹理特征
            texture_features = self._extract_texture_features(gray)
            features.update(texture_features)

            return features

        except Exception as e:
            print(f"提取图像特征时出错: {e}")
            return {}

    def _calculate_skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)

    def _calculate_kurtosis(self, data):
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3

    def _calculate_max_drawdown(self, prices):
        """计算最大回撤"""
        peak = prices[0]
        max_dd = 0
        for price in prices:
            if price > peak:
                peak = price
            dd = (peak - price) / peak if peak > 0 else 0
            if dd > max_dd:
                max_dd = dd
        return max_dd

    def _find_peaks_troughs(self, prices, min_distance=5):
        """寻找峰值和谷值"""
        peaks = []
        troughs = []

        for i in range(min_distance, len(prices) - min_distance):
            # 检查是否是峰值
            is_peak = True
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and prices[j] >= prices[i]:
                    is_peak = False
                    break
            if is_peak:
                peaks.append(i)

            # 检查是否是谷值
            is_trough = True
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and prices[j] <= prices[i]:
                    is_trough = False
                    break
            if is_trough:
                troughs.append(i)

        return peaks, troughs

    def _extract_n_pattern_features(self, prices, peaks, troughs):
        """提取N字形态特定特征"""
        features = {}

        # 合并并排序所有极值点
        all_extremes = [(i, 'peak') for i in peaks] + [(i, 'trough') for i in troughs]
        all_extremes.sort(key=lambda x: x[0])

        if len(all_extremes) >= 4:
            # 寻找可能的N字形态模式
            n_patterns = 0
            for i in range(len(all_extremes) - 3):
                pattern = [all_extremes[j][1] for j in range(i, i + 4)]
                # 检查是否符合N字形态：高-低-高-低 或 低-高-低-高
                if (pattern == ['peak', 'trough', 'peak', 'trough'] or
                    pattern == ['trough', 'peak', 'trough', 'peak']):
                    n_patterns += 1

            features['n_pattern_count'] = n_patterns
            features['extreme_ratio'] = len(all_extremes) / len(prices)
        else:
            features['n_pattern_count'] = 0
            features['extreme_ratio'] = 0

        # 计算价格变化的方向性
        if len(all_extremes) >= 2:
            direction_changes = 0
            for i in range(1, len(all_extremes)):
                if all_extremes[i][1] != all_extremes[i-1][1]:
                    direction_changes += 1
            features['direction_changes'] = direction_changes
        else:
            features['direction_changes'] = 0

        return features

    def _calculate_technical_indicators(self, prices):
        """计算技术指标"""
        features = {}

        # 移动平均
        if len(prices) >= 5:
            ma5 = np.mean(prices[-5:])
            features['ma5_position'] = (prices[-1] - ma5) / ma5 if ma5 > 0 else 0
        else:
            features['ma5_position'] = 0

        if len(prices) >= 10:
            ma10 = np.mean(prices[-10:])
            features['ma10_position'] = (prices[-1] - ma10) / ma10 if ma10 > 0 else 0
        else:
            features['ma10_position'] = 0

        # RSI指标
        if len(prices) >= 14:
            rsi = self._calculate_rsi(prices, 14)
            features['rsi'] = rsi
        else:
            features['rsi'] = 50  # 中性值

        return features

    def _calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _detect_n_pattern_in_lines(self, lines, image_shape):
        """在线段中检测N字形态"""
        if len(lines) < 3:
            return 0

        height, width = image_shape

        # 将线段按x坐标排序
        sorted_lines = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            if x1 > x2:
                x1, x2 = x2, x1
                y1, y2 = y2, y1
            if x2 != x1:
                slope = (y2 - y1) / (x2 - x1)
                sorted_lines.append((x1, y1, x2, y2, slope))

        sorted_lines.sort(key=lambda x: x[0])

        n_score = 0
        # 寻找N字形态：上升-下降-上升 或 下降-上升-下降
        for i in range(len(sorted_lines) - 2):
            line1, line2, line3 = sorted_lines[i:i+3]

            # 检查连续性
            gap1 = abs(line1[2] - line2[0]) / width
            gap2 = abs(line2[2] - line3[0]) / width

            if gap1 < 0.1 and gap2 < 0.1:  # 线段相对连续
                # 检查斜率模式
                if ((line1[4] > 0.1 and line2[4] < -0.1 and line3[4] > 0.1) or
                    (line1[4] < -0.1 and line2[4] > 0.1 and line3[4] < -0.1)):
                    n_score += 1

        return n_score / max(1, len(sorted_lines) - 2)

    def _extract_texture_features(self, gray_image):
        """提取图像纹理特征"""
        features = {}

        # 计算图像的梯度
        grad_x = cv2.Sobel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)

        features['gradient_mean'] = np.mean(np.sqrt(grad_x**2 + grad_y**2))
        features['gradient_std'] = np.std(np.sqrt(grad_x**2 + grad_y**2))

        # 计算图像的对比度
        features['contrast'] = np.std(gray_image)

        return features

class NPatternMLTrainer:
    """N字形态机器学习训练器"""

    def __init__(self):
        self.feature_extractor = NPatternFeatureExtractor()
        self.models = {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'svm': SVC(kernel='rbf', random_state=42, probability=True)
        }
        self.best_model = None
        self.best_model_name = None
        self.feature_names = None

    def prepare_training_data(self, positive_samples: List[str], negative_samples: List[str]) -> Tuple[np.array, np.array]:
        """准备训练数据

        Args:
            positive_samples: 正样本文件路径列表（包含N字形态的图片或CSV文件）
            negative_samples: 负样本文件路径列表（不包含N字形态的图片或CSV文件）

        Returns:
            特征矩阵和标签数组
        """
        features_list = []
        labels = []

        print("处理正样本...")
        for i, sample_path in enumerate(positive_samples):
            print(f"处理正样本 {i+1}/{len(positive_samples)}: {os.path.basename(sample_path)}")
            features = self._extract_features_from_file(sample_path)
            if features:
                features_list.append(features)
                labels.append(1)  # 正样本标签为1

        print("处理负样本...")
        for i, sample_path in enumerate(negative_samples):
            print(f"处理负样本 {i+1}/{len(negative_samples)}: {os.path.basename(sample_path)}")
            features = self._extract_features_from_file(sample_path)
            if features:
                features_list.append(features)
                labels.append(0)  # 负样本标签为0

        if not features_list:
            raise ValueError("没有成功提取到任何特征")

        # 转换为DataFrame以便处理
        df = pd.DataFrame(features_list)
        self.feature_names = df.columns.tolist()

        # 处理缺失值
        df = df.fillna(0)

        # 标准化特征
        X = self.feature_extractor.scaler.fit_transform(df.values)
        y = np.array(labels)

        print(f"训练数据准备完成: {len(X)} 个样本, {X.shape[1]} 个特征")
        print(f"正样本: {sum(y)} 个, 负样本: {len(y) - sum(y)} 个")

        return X, y

    def _extract_features_from_file(self, file_path: str) -> Optional[Dict]:
        """从文件中提取特征"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                # 图像文件
                return self.feature_extractor.extract_image_features(file_path)
            elif file_ext in ['.csv', '.txt']:
                # CSV文件
                return self._extract_features_from_csv(file_path)
            else:
                print(f"不支持的文件格式: {file_ext}")
                return None

        except Exception as e:
            print(f"提取特征时出错 {file_path}: {e}")
            return None

    def _extract_features_from_csv(self, csv_path: str) -> Optional[Dict]:
        """从CSV文件中提取特征"""
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path)

            # 尝试不同的列名组合
            price_columns = ['close', 'Close', '收盘价', '收盘']
            price_col = None

            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break

            if price_col is None:
                # 如果没找到明确的价格列，使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    print(f"CSV文件中没有找到价格数据: {csv_path}")
                    return None

            prices = df[price_col].values

            # 移除NaN值
            prices = prices[~np.isnan(prices)]

            if len(prices) < 10:
                print(f"价格数据点太少: {len(prices)}")
                return None

            return self.feature_extractor.extract_price_features(prices)

        except Exception as e:
            print(f"读取CSV文件时出错 {csv_path}: {e}")
            return None

    def train_models(self, X: np.array, y: np.array) -> Dict:
        """训练所有模型并选择最佳模型"""
        if len(X) < 10:
            raise ValueError("训练数据太少，至少需要10个样本")

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        results = {}
        best_score = 0

        print("\n开始训练模型...")

        for model_name, model in self.models.items():
            print(f"\n训练 {model_name}...")

            # 训练模型
            model.fit(X_train, y_train)

            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5)

            # 测试集评估
            test_score = model.score(X_test, y_test)
            y_pred = model.predict(X_test)

            results[model_name] = {
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'test_score': test_score,
                'classification_report': classification_report(y_test, y_pred),
                'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
            }

            print(f"{model_name} - CV得分: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
            print(f"{model_name} - 测试得分: {test_score:.3f}")

            # 选择最佳模型
            if cv_scores.mean() > best_score:
                best_score = cv_scores.mean()
                self.best_model = model
                self.best_model_name = model_name

        print(f"\n最佳模型: {self.best_model_name} (CV得分: {best_score:.3f})")

        return results

    def save_model(self, model_path: str, metadata: Dict = None):
        """保存训练好的模型"""
        if self.best_model is None:
            raise ValueError("没有训练好的模型可以保存")

        model_data = {
            'model': self.best_model,
            'model_name': self.best_model_name,
            'scaler': self.feature_extractor.scaler,
            'feature_names': self.feature_names,
            'metadata': metadata or {},
            'created_at': datetime.now().isoformat()
        }

        joblib.dump(model_data, model_path)
        print(f"模型已保存到: {model_path}")

    def load_model(self, model_path: str):
        """加载训练好的模型"""
        try:
            model_data = joblib.load(model_path)

            self.best_model = model_data['model']
            self.best_model_name = model_data['model_name']
            self.feature_extractor.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']

            print(f"模型加载成功: {self.best_model_name}")
            print(f"创建时间: {model_data.get('created_at', '未知')}")

            return model_data.get('metadata', {})

        except Exception as e:
            print(f"加载模型时出错: {e}")
            return None

    def predict_csv(self, csv_path: str, threshold: float = 0.5) -> Dict:
        """预测CSV文件中是否包含N字形态"""
        if self.best_model is None:
            raise ValueError("请先训练或加载模型")

        # 提取特征
        features = self._extract_features_from_csv(csv_path)
        if features is None:
            return {'error': '无法提取特征'}

        # 转换为DataFrame并处理
        df = pd.DataFrame([features])

        # 确保特征顺序与训练时一致
        for feature_name in self.feature_names:
            if feature_name not in df.columns:
                df[feature_name] = 0

        df = df[self.feature_names]  # 重新排序
        df = df.fillna(0)

        # 标准化
        X = self.feature_extractor.scaler.transform(df.values)

        # 预测
        prediction = self.best_model.predict(X)[0]
        probability = self.best_model.predict_proba(X)[0]

        result = {
            'file_path': csv_path,
            'prediction': bool(prediction),
            'probability_negative': float(probability[0]),
            'probability_positive': float(probability[1]),
            'confidence': float(max(probability)),
            'is_n_pattern': probability[1] > threshold,
            'features': features
        }

        return result

    def batch_predict(self, csv_files: List[str], threshold: float = 0.5) -> List[Dict]:
        """批量预测多个CSV文件"""
        results = []

        print(f"开始批量预测 {len(csv_files)} 个文件...")

        for i, csv_file in enumerate(csv_files):
            print(f"预测 {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}")
            result = self.predict_csv(csv_file, threshold)
            results.append(result)

        # 统计结果
        n_patterns_found = sum(1 for r in results if r.get('is_n_pattern', False))
        print(f"\n预测完成! 找到 {n_patterns_found} 个可能的N字形态")

        return results


def create_sample_training_data():
    """创建示例训练数据（用于演示）"""
    print("创建示例训练数据...")

    # 创建示例目录
    os.makedirs("training_data/positive", exist_ok=True)
    os.makedirs("training_data/negative", exist_ok=True)

    # 生成示例N字形态数据
    np.random.seed(42)

    # 正样本：包含N字形态的价格序列
    for i in range(10):
        # 创建N字形态：上升-下降-上升-下降
        n_pattern = []

        # 第一段：上升
        start_price = 100 + np.random.normal(0, 5)
        for j in range(20):
            price = start_price + j * 0.5 + np.random.normal(0, 0.5)
            n_pattern.append(price)

        # 第二段：下降
        peak1 = n_pattern[-1]
        for j in range(15):
            price = peak1 - j * 0.8 + np.random.normal(0, 0.5)
            n_pattern.append(price)

        # 第三段：上升
        trough = n_pattern[-1]
        for j in range(18):
            price = trough + j * 0.9 + np.random.normal(0, 0.5)
            n_pattern.append(price)

        # 第四段：下降
        peak2 = n_pattern[-1]
        for j in range(12):
            price = peak2 - j * 1.2 + np.random.normal(0, 0.5)
            n_pattern.append(price)

        # 保存为CSV
        df = pd.DataFrame({'close': n_pattern})
        df.to_csv(f"training_data/positive/n_pattern_{i+1}.csv", index=False)

    # 负样本：随机价格序列或其他形态
    for i in range(10):
        # 创建随机走势
        prices = []
        current_price = 100 + np.random.normal(0, 10)

        for j in range(65):
            change = np.random.normal(0, 1)
            current_price += change
            prices.append(current_price)

        # 保存为CSV
        df = pd.DataFrame({'close': prices})
        df.to_csv(f"training_data/negative/random_{i+1}.csv", index=False)

    print("示例训练数据创建完成!")
    print("正样本目录: training_data/positive/")
    print("负样本目录: training_data/negative/")


def main():
    """主函数 - 演示如何使用N字形态机器学习训练器"""

    print("=== N字形态机器学习训练器 ===\n")

    # 创建训练器实例
    trainer = NPatternMLTrainer()

    # 选择操作模式
    print("请选择操作模式:")
    print("1. 训练新模型")
    print("2. 使用已有模型预测")
    print("3. 创建示例训练数据")

    choice = input("请输入选择 (1/2/3): ").strip()

    if choice == "1":
        # 训练模式
        print("\n=== 训练模式 ===")

        # 获取训练数据路径
        positive_dir = input("请输入正样本目录路径 (包含N字形态的图片或CSV文件): ").strip()
        negative_dir = input("请输入负样本目录路径 (不包含N字形态的图片或CSV文件): ").strip()

        if not os.path.exists(positive_dir) or not os.path.exists(negative_dir):
            print("目录不存在!")
            return

        # 收集文件
        positive_files = []
        negative_files = []

        for ext in ['*.csv', '*.txt', '*.jpg', '*.jpeg', '*.png', '*.bmp']:
            positive_files.extend(glob.glob(os.path.join(positive_dir, ext)))
            negative_files.extend(glob.glob(os.path.join(negative_dir, ext)))

        if len(positive_files) == 0 or len(negative_files) == 0:
            print("没有找到训练文件!")
            return

        print(f"找到正样本: {len(positive_files)} 个")
        print(f"找到负样本: {len(negative_files)} 个")

        # 准备训练数据
        X, y = trainer.prepare_training_data(positive_files, negative_files)

        # 训练模型
        results = trainer.train_models(X, y)

        # 保存模型
        model_path = input("请输入模型保存路径 (例如: n_pattern_model.pkl): ").strip()
        if not model_path:
            model_path = "n_pattern_model.pkl"

        metadata = {
            'positive_samples': len(positive_files),
            'negative_samples': len(negative_files),
            'training_results': results
        }

        trainer.save_model(model_path, metadata)

        print(f"\n训练完成! 模型已保存到: {model_path}")

    elif choice == "2":
        # 预测模式
        print("\n=== 预测模式 ===")

        # 加载模型
        model_path = input("请输入模型文件路径: ").strip()
        if not os.path.exists(model_path):
            print("模型文件不存在!")
            return

        trainer.load_model(model_path)

        # 选择预测方式
        print("\n请选择预测方式:")
        print("1. 预测单个CSV文件")
        print("2. 批量预测目录中的CSV文件")

        pred_choice = input("请输入选择 (1/2): ").strip()

        if pred_choice == "1":
            # 单个文件预测
            csv_path = input("请输入CSV文件路径: ").strip()
            if not os.path.exists(csv_path):
                print("文件不存在!")
                return

            threshold = float(input("请输入判断阈值 (0-1, 默认0.5): ").strip() or "0.5")

            result = trainer.predict_csv(csv_path, threshold)

            print(f"\n=== 预测结果 ===")
            print(f"文件: {result['file_path']}")
            print(f"是否包含N字形态: {'是' if result['is_n_pattern'] else '否'}")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"正样本概率: {result['probability_positive']:.3f}")
            print(f"负样本概率: {result['probability_negative']:.3f}")

        elif pred_choice == "2":
            # 批量预测
            csv_dir = input("请输入包含CSV文件的目录路径: ").strip()
            if not os.path.exists(csv_dir):
                print("目录不存在!")
                return

            threshold = float(input("请输入判断阈值 (0-1, 默认0.5): ").strip() or "0.5")

            # 收集CSV文件
            csv_files = []
            for ext in ['*.csv', '*.txt']:
                csv_files.extend(glob.glob(os.path.join(csv_dir, ext)))

            if len(csv_files) == 0:
                print("目录中没有找到CSV文件!")
                return

            results = trainer.batch_predict(csv_files, threshold)

            # 显示结果
            print(f"\n=== 批量预测结果 ===")
            n_patterns = [r for r in results if r.get('is_n_pattern', False)]

            if n_patterns:
                print(f"找到 {len(n_patterns)} 个可能的N字形态:")
                for result in n_patterns:
                    print(f"  {os.path.basename(result['file_path'])}: 置信度 {result['confidence']:.3f}")
            else:
                print("没有找到N字形态")

            # 保存结果
            save_results = input("\n是否保存预测结果到JSON文件? (y/n): ").strip().lower()
            if save_results == 'y':
                results_path = input("请输入结果文件路径 (默认: prediction_results.json): ").strip()
                if not results_path:
                    results_path = "prediction_results.json"

                with open(results_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)

                print(f"预测结果已保存到: {results_path}")

    elif choice == "3":
        # 创建示例数据
        create_sample_training_data()
        print("\n示例数据创建完成! 您可以使用这些数据来训练模型。")

    else:
        print("无效的选择!")


if __name__ == "__main__":
    import glob
    main()