# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
from czsc.analyze import CZSC
from czsc.objects import RawBar
from czsc.enum import Freq
from czsc.utils.echarts_plot import kline_pro
import webbrowser
import os
from datetime import datetime

# 创建示例数据
def create_sample_data(n=100):
    """创建示例K线数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2020-01-01', periods=n, freq='D')
    close = np.random.normal(loc=0.1, scale=0.5, size=n).cumsum() + 100
    open_prices = close - np.random.normal(loc=0, scale=0.2, size=n)
    high = np.maximum(close, open_prices) + np.random.normal(loc=0.2, scale=0.1, size=n)
    low = np.minimum(close, open_prices) - np.random.normal(loc=0.2, scale=0.1, size=n)
    
    bars = []
    for i in range(n):
        bar = RawBar(symbol="000001.SH", id=i, dt=dates[i], freq=Freq.D,
                    open=open_prices[i], close=close[i], high=high[i], low=low[i],
                    vol=np.random.randint(1000, 10000), amount=close[i] * 10000)
        bars.append(bar)
    return bars

# 创建K线数据
bars = create_sample_data(100)

# 创建CZSC对象
czsc_obj = CZSC(bars)

# 生成K线图
file_html = "kline_plot.html"
chart = kline_pro(czsc_obj, file_html=file_html, width="1400px", height="680px")

print(f"K线图已保存到 {file_html}")
print("尝试在浏览器中打开...")

# 尝试在浏览器中打开
try:
    webbrowser.open('file://' + os.path.abspath(file_html))
except:
    print("无法自动打开浏览器，请手动打开生成的HTML文件")
