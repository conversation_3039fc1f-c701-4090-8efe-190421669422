#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV N字形态预测器
使用训练好的模型分析CSV数据，找到相似的N字形态
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import joblib
from scipy import signal
from typing import List, Dict, Tuple
import glob

class CSVNPatternPredictor:
    """CSV N字形态预测器"""
    
    def __init__(self, model_path: str):
        """初始化预测器"""
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        self.feature_extractor = self.model_data['feature_extractor']
        
        print(f"模型加载成功: {self.model_data['model_name']}")
    
    def load_csv_data(self, csv_path: str) -> np.array:
        """加载CSV数据"""
        try:
            df = pd.read_csv(csv_path)
            
            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', '最高价', 
                           'low', 'Low', '最低价', 'price', 'Price', '价格']
            
            price_col = None
            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break
            
            if price_col is None:
                # 使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    raise ValueError("未找到价格数据列")
            
            prices = df[price_col].values
            prices = prices[~np.isnan(prices)]  # 移除NaN
            
            print(f"加载CSV数据: {len(prices)} 个价格点，使用列: {price_col}")
            return prices
            
        except Exception as e:
            print(f"加载CSV失败 {csv_path}: {e}")
            return None
    
    def extract_features_from_prices(self, prices: np.array) -> Dict:
        """从价格数据提取特征"""
        if len(prices) < 20:
            return None
        
        # 归一化价格到[0,1]区间
        min_price = np.min(prices)
        max_price = np.max(prices)
        if max_price > min_price:
            normalized_prices = (prices - min_price) / (max_price - min_price)
        else:
            normalized_prices = np.ones_like(prices) * 0.5
        
        # 使用与训练时相同的特征提取方法
        features = self.feature_extractor.extract_n_pattern_features(normalized_prices)
        
        return features
    
    def sliding_window_analysis(self, prices: np.array, window_size: int = 50, step_size: int = 5) -> List[Dict]:
        """滑动窗口分析"""
        results = []
        
        print(f"使用滑动窗口分析: 窗口大小={window_size}, 步长={step_size}")
        
        for i in range(0, len(prices) - window_size + 1, step_size):
            window_prices = prices[i:i + window_size]
            
            # 提取特征
            features = self.extract_features_from_prices(window_prices)
            
            if features is not None:
                # 转换为模型输入格式
                feature_vector = self._features_to_vector(features)
                
                if feature_vector is not None:
                    # 标准化
                    feature_vector_scaled = self.scaler.transform([feature_vector])
                    
                    # 预测
                    prediction = self.model.predict(feature_vector_scaled)[0]
                    probability = self.model.predict_proba(feature_vector_scaled)[0]
                    
                    result = {
                        'start_index': i,
                        'end_index': i + window_size - 1,
                        'window_prices': window_prices,
                        'features': features,
                        'prediction': prediction,
                        'probability_negative': probability[0],
                        'probability_positive': probability[1],
                        'confidence': max(probability),
                        'is_n_pattern': prediction == 1
                    }
                    
                    results.append(result)
        
        return results
    
    def _features_to_vector(self, features: Dict) -> np.array:
        """将特征字典转换为向量"""
        try:
            # 确保特征顺序与训练时一致
            feature_names = [
                'mean', 'std', 'range', 'overall_trend', 'peak_count', 'trough_count',
                'n_pattern_score', 'height_variation', 'avg_time_interval', 
                'time_interval_std', 'direction_changes', 'direction_change_rate',
                'volatility', 'max_single_move', 'sharpness'
            ]
            
            vector = []
            for name in feature_names:
                vector.append(features.get(name, 0))
            
            return np.array(vector)
            
        except Exception as e:
            print(f"特征转换失败: {e}")
            return None
    
    def find_n_patterns(self, csv_path: str, window_size: int = 50, step_size: int = 5, 
                       threshold: float = 0.7) -> List[Dict]:
        """在CSV数据中寻找N字形态"""
        print(f"\n分析文件: {os.path.basename(csv_path)}")
        
        # 加载数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return []
        
        # 滑动窗口分析
        results = self.sliding_window_analysis(prices, window_size, step_size)
        
        # 筛选N字形态
        n_patterns = []
        for result in results:
            if result['is_n_pattern'] and result['probability_positive'] >= threshold:
                n_patterns.append(result)
        
        print(f"找到 {len(n_patterns)} 个可能的N字形态 (阈值: {threshold})")
        
        return n_patterns
    
    def visualize_results(self, csv_path: str, n_patterns: List[Dict], save_plot: bool = False):
        """可视化结果"""
        if not n_patterns:
            print("没有找到N字形态可视化")
            return
        
        # 加载完整数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 上图：完整价格走势
        ax1.plot(prices, 'b-', linewidth=1, alpha=0.7, label='Price')
        ax1.set_title(f'Price Chart: {os.path.basename(csv_path)}')
        ax1.set_ylabel('Price')
        ax1.grid(True, alpha=0.3)
        
        # 标记N字形态
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        for i, pattern in enumerate(n_patterns[:5]):  # 最多显示5个
            start_idx = pattern['start_index']
            end_idx = pattern['end_index']
            color = colors[i % len(colors)]
            
            # 高亮显示N字形态区域
            ax1.axvspan(start_idx, end_idx, alpha=0.2, color=color)
            ax1.plot(range(start_idx, end_idx + 1), pattern['window_prices'], 
                    color=color, linewidth=3, alpha=0.8,
                    label=f'N-Pattern {i+1} (conf: {pattern["confidence"]:.2f})')
        
        ax1.legend()
        
        # 下图：置信度分布
        if len(n_patterns) > 0:
            confidences = [p['confidence'] for p in n_patterns]
            positions = [p['start_index'] for p in n_patterns]
            
            ax2.scatter(positions, confidences, c='red', s=50, alpha=0.7)
            ax2.set_xlabel('Position')
            ax2.set_ylabel('Confidence')
            ax2.set_title('N-Pattern Detection Confidence')
            ax2.grid(True, alpha=0.3)
            ax2.set_ylim(0, 1)
        
        plt.tight_layout()
        
        if save_plot:
            plot_name = f"{os.path.splitext(os.path.basename(csv_path))[0]}_n_patterns.png"
            plt.savefig(plot_name, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {plot_name}")
        
        plt.show()
    
    def batch_analyze_directory(self, csv_dir: str, window_size: int = 50, 
                               threshold: float = 0.7) -> Dict:
        """批量分析目录中的CSV文件"""
        print(f"\n批量分析目录: {csv_dir}")
        
        # 收集CSV文件
        csv_files = []
        for ext in ['*.csv', '*.txt']:
            csv_files.extend(glob.glob(os.path.join(csv_dir, ext)))
        
        if not csv_files:
            print("目录中没有找到CSV文件")
            return {}
        
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        all_results = {}
        total_patterns = 0
        
        for i, csv_file in enumerate(csv_files):
            print(f"\n处理 {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}")
            
            n_patterns = self.find_n_patterns(csv_file, window_size, threshold=threshold)
            
            if n_patterns:
                all_results[csv_file] = n_patterns
                total_patterns += len(n_patterns)
                
                # 显示最佳匹配
                best_pattern = max(n_patterns, key=lambda x: x['confidence'])
                print(f"  最佳匹配: 置信度 {best_pattern['confidence']:.3f}, "
                      f"位置 {best_pattern['start_index']}-{best_pattern['end_index']}")
        
        print(f"\n=== 批量分析完成 ===")
        print(f"总共找到 {total_patterns} 个N字形态")
        print(f"有N字形态的文件: {len(all_results)}/{len(csv_files)}")
        
        return all_results

def main():
    """主函数"""
    print("=== CSV N字形态预测器 ===\n")
    
    # 检查模型文件
    model_path = "n_pattern_feature_model.pkl"
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先运行 n_pattern_feature_trainer.py 训练模型")
        return
    
    # 创建预测器
    try:
        predictor = CSVNPatternPredictor(model_path)
    except Exception as e:
        print(f"加载模型失败: {e}")
        return
    
    print("\n请选择分析模式:")
    print("1. 分析单个CSV文件")
    print("2. 批量分析目录中的CSV文件")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 单个文件分析
        csv_path = input("请输入CSV文件路径: ").strip()
        if not os.path.exists(csv_path):
            print("文件不存在!")
            return
        
        window_size = int(input("请输入分析窗口大小 (默认50): ").strip() or "50")
        threshold = float(input("请输入置信度阈值 (0-1, 默认0.7): ").strip() or "0.7")
        
        # 分析文件
        n_patterns = predictor.find_n_patterns(csv_path, window_size, threshold=threshold)
        
        if n_patterns:
            print(f"\n找到 {len(n_patterns)} 个N字形态:")
            for i, pattern in enumerate(n_patterns):
                print(f"  N字形态 {i+1}: 位置 {pattern['start_index']}-{pattern['end_index']}, "
                      f"置信度 {pattern['confidence']:.3f}")
            
            # 可视化
            show_plot = input("\n是否显示图表? (y/n): ").strip().lower() == 'y'
            if show_plot:
                predictor.visualize_results(csv_path, n_patterns, save_plot=True)
        else:
            print("未找到N字形态")
    
    elif choice == "2":
        # 批量分析
        csv_dir = input("请输入CSV文件目录路径: ").strip()
        if not os.path.exists(csv_dir):
            print("目录不存在!")
            return
        
        window_size = int(input("请输入分析窗口大小 (默认50): ").strip() or "50")
        threshold = float(input("请输入置信度阈值 (0-1, 默认0.7): ").strip() or "0.7")
        
        # 批量分析
        results = predictor.batch_analyze_directory(csv_dir, window_size, threshold)
        
        if results:
            # 显示汇总结果
            print(f"\n=== 详细结果 ===")
            for csv_file, patterns in results.items():
                print(f"\n文件: {os.path.basename(csv_file)}")
                for i, pattern in enumerate(patterns):
                    print(f"  N字形态 {i+1}: 位置 {pattern['start_index']}-{pattern['end_index']}, "
                          f"置信度 {pattern['confidence']:.3f}")
            
            # 保存结果
            save_results = input("\n是否保存结果到JSON文件? (y/n): ").strip().lower() == 'y'
            if save_results:
                import json
                
                # 简化结果用于保存
                simplified_results = {}
                for csv_file, patterns in results.items():
                    simplified_results[os.path.basename(csv_file)] = [
                        {
                            'start_index': p['start_index'],
                            'end_index': p['end_index'],
                            'confidence': p['confidence'],
                            'probability_positive': p['probability_positive']
                        }
                        for p in patterns
                    ]
                
                results_path = "n_pattern_analysis_results.json"
                with open(results_path, 'w', encoding='utf-8') as f:
                    json.dump(simplified_results, f, ensure_ascii=False, indent=2)
                
                print(f"结果已保存到: {results_path}")
    
    else:
        print("无效选择!")

if __name__ == "__main__":
    main()
