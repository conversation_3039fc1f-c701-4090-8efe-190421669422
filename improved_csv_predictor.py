#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的CSV N字形态预测器
专门识别类似您标注的N字形态
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import joblib
from typing import List, Dict
import glob

class ImprovedCSVPredictor:
    """改进的CSV预测器"""
    
    def __init__(self, model_path: str):
        """初始化预测器"""
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        print(f"改进模型加载成功")
    
    def load_csv_data(self, csv_path: str) -> np.array:
        """加载CSV数据"""
        try:
            df = pd.read_csv(csv_path)
            
            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', '最高价', 
                           'low', 'Low', '最低价', 'price', 'Price', '价格']
            
            price_col = None
            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break
            
            if price_col is None:
                # 使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    raise ValueError("未找到价格数据列")
            
            prices = df[price_col].values
            prices = prices[~np.isnan(prices)]  # 移除NaN
            
            print(f"加载CSV数据: {len(prices)} 个价格点，使用列: {price_col}")
            return prices
            
        except Exception as e:
            print(f"加载CSV失败 {csv_path}: {e}")
            return None
    
    def find_turning_points(self, prices: np.array, min_change_pct: float = 0.03):
        """寻找价格转折点"""
        turning_points = []
        
        if len(prices) < 5:
            return turning_points
        
        # 使用更严格的转折点检测
        for i in range(2, len(prices) - 2):
            # 检查前后各2个点
            window = prices[i-2:i+3]
            center = window[2]  # 中心点
            
            # 检查是否是局部极值
            is_peak = all(center >= p for p in window) and center > window[0] and center > window[4]
            is_trough = all(center <= p for p in window) and center < window[0] and center < window[4]
            
            if is_peak or is_trough:
                # 检查变化幅度
                max_change = max(abs(center - window[0]), abs(center - window[4]))
                if max_change / center > min_change_pct:
                    turning_points.append(i)
        
        return turning_points
    
    def calculate_n_pattern_features(self, prices: np.array):
        """计算N字形态特征"""
        if len(prices) < 10:
            return None
        
        # 归一化价格
        min_price = np.min(prices)
        max_price = np.max(prices)
        if max_price > min_price:
            normalized_prices = (prices - min_price) / (max_price - min_price)
        else:
            normalized_prices = np.ones_like(prices) * 0.5
        
        features = {}
        
        # 寻找转折点
        turning_points = self.find_turning_points(normalized_prices)
        
        if len(turning_points) < 3:
            return None
        
        # 取前4个最重要的转折点
        key_points = turning_points[:4] if len(turning_points) >= 4 else turning_points
        
        # 计算特征
        heights = [normalized_prices[point] for point in key_points]
        features['height_range'] = max(heights) - min(heights)
        features['height_std'] = np.std(heights)
        
        # 时间特征
        if len(key_points) > 1:
            time_intervals = np.diff(key_points)
            features['avg_time_interval'] = np.mean(time_intervals)
            features['time_interval_std'] = np.std(time_intervals)
        else:
            features['avg_time_interval'] = 0
            features['time_interval_std'] = 0
        
        # 斜率特征
        slopes = []
        for i in range(len(key_points) - 1):
            start_idx, end_idx = key_points[i], key_points[i + 1]
            slope = (normalized_prices[end_idx] - normalized_prices[start_idx]) / (end_idx - start_idx)
            slopes.append(slope)
        
        if len(slopes) > 0:
            features['slope_changes'] = len([i for i in range(len(slopes)-1) 
                                           if slopes[i] * slopes[i+1] < 0])
            features['avg_slope_magnitude'] = np.mean(np.abs(slopes))
            features['slope_variation'] = np.std(slopes)
            
            # N字形态得分
            directions = [1 if slope > 0 else -1 for slope in slopes]
            n_pattern_score = 0
            
            # 检查N字模式
            for i in range(len(directions) - 2):
                pattern = directions[i:i+3]
                if pattern == [1, -1, 1] or pattern == [-1, 1, -1]:
                    n_pattern_score += 1
            
            features['n_pattern_score'] = n_pattern_score
        else:
            features['slope_changes'] = 0
            features['avg_slope_magnitude'] = 0
            features['slope_variation'] = 0
            features['n_pattern_score'] = 0
        
        # 整体特征
        features['overall_trend'] = normalized_prices[-1] - normalized_prices[0]
        features['volatility'] = np.std(np.diff(normalized_prices))
        features['price_range'] = np.max(normalized_prices) - np.min(normalized_prices)
        
        # 对称性
        mid_point = len(normalized_prices) // 2
        left_half = normalized_prices[:mid_point]
        right_half = normalized_prices[mid_point:]
        
        if len(left_half) > 0 and len(right_half) > 0:
            features['symmetry'] = 1 - abs(np.mean(left_half) - np.mean(right_half))
        else:
            features['symmetry'] = 0
        
        return features
    
    def sliding_window_analysis(self, prices: np.array, window_size: int = 30, step_size: int = 3):
        """滑动窗口分析，使用更小的窗口"""
        results = []
        
        print(f"使用滑动窗口分析: 窗口大小={window_size}, 步长={step_size}")
        
        for i in range(0, len(prices) - window_size + 1, step_size):
            window_prices = prices[i:i + window_size]
            
            # 提取特征
            features = self.calculate_n_pattern_features(window_prices)
            
            if features is not None:
                # 转换为模型输入格式
                feature_vector = self._features_to_vector(features)
                
                if feature_vector is not None:
                    # 标准化
                    feature_vector_scaled = self.scaler.transform([feature_vector])
                    
                    # 预测
                    prediction = self.model.predict(feature_vector_scaled)[0]
                    probability = self.model.predict_proba(feature_vector_scaled)[0]
                    
                    result = {
                        'start_index': i,
                        'end_index': i + window_size - 1,
                        'window_prices': window_prices,
                        'features': features,
                        'prediction': prediction,
                        'probability_negative': probability[0],
                        'probability_positive': probability[1],
                        'confidence': max(probability),
                        'is_n_pattern': prediction == 1,
                        'n_pattern_score': features.get('n_pattern_score', 0)
                    }
                    
                    results.append(result)
        
        return results
    
    def _features_to_vector(self, features: Dict) -> np.array:
        """将特征字典转换为向量"""
        try:
            feature_names = [
                'height_range', 'height_std', 'avg_time_interval', 'time_interval_std',
                'slope_changes', 'avg_slope_magnitude', 'slope_variation', 'n_pattern_score',
                'overall_trend', 'volatility', 'price_range', 'symmetry'
            ]
            
            vector = []
            for name in feature_names:
                vector.append(features.get(name, 0))
            
            return np.array(vector)
            
        except Exception as e:
            print(f"特征转换失败: {e}")
            return None
    
    def find_n_patterns(self, csv_path: str, window_size: int = 30, step_size: int = 3, 
                       threshold: float = 0.8, min_n_score: float = 1.0):
        """在CSV数据中寻找N字形态"""
        print(f"\n分析文件: {os.path.basename(csv_path)}")
        
        # 加载数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return []
        
        # 滑动窗口分析
        results = self.sliding_window_analysis(prices, window_size, step_size)
        
        # 筛选N字形态 - 更严格的条件
        n_patterns = []
        for result in results:
            if (result['is_n_pattern'] and 
                result['probability_positive'] >= threshold and
                result['n_pattern_score'] >= min_n_score):
                n_patterns.append(result)
        
        print(f"找到 {len(n_patterns)} 个可能的N字形态 (阈值: {threshold}, N字得分: {min_n_score})")
        
        return n_patterns
    
    def visualize_results(self, csv_path: str, n_patterns: List[Dict], save_plot: bool = False):
        """可视化结果"""
        if not n_patterns:
            print("没有找到N字形态可视化")
            return
        
        # 加载完整数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(15, 8))
        
        # 绘制完整价格走势
        ax.plot(prices, 'b-', linewidth=1, alpha=0.7, label='Price')
        ax.set_title(f'N-Pattern Detection: {os.path.basename(csv_path)}')
        ax.set_ylabel('Price')
        ax.set_xlabel('Time')
        ax.grid(True, alpha=0.3)
        
        # 标记N字形态
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        for i, pattern in enumerate(n_patterns[:5]):  # 最多显示5个
            start_idx = pattern['start_index']
            end_idx = pattern['end_index']
            color = colors[i % len(colors)]
            
            # 高亮显示N字形态区域
            ax.axvspan(start_idx, end_idx, alpha=0.3, color=color)
            ax.plot(range(start_idx, end_idx + 1), pattern['window_prices'], 
                    color=color, linewidth=3, alpha=0.9,
                    label=f'N-Pattern {i+1} (conf: {pattern["confidence"]:.2f}, score: {pattern["n_pattern_score"]:.1f})')
        
        ax.legend()
        plt.tight_layout()
        
        if save_plot:
            plot_name = f"{os.path.splitext(os.path.basename(csv_path))[0]}_improved_n_patterns.png"
            plt.savefig(plot_name, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {plot_name}")
        
        plt.show()

def main():
    """主函数"""
    print("=== 改进的CSV N字形态预测器 ===\n")
    
    # 检查模型文件
    model_path = "improved_n_pattern_model.pkl"
    if not os.path.exists(model_path):
        print(f"改进模型文件不存在: {model_path}")
        print("请先运行 improved_n_pattern_trainer.py 训练改进模型")
        return
    
    # 创建预测器
    try:
        predictor = ImprovedCSVPredictor(model_path)
    except Exception as e:
        print(f"加载模型失败: {e}")
        return
    
    # 单个文件分析
    csv_path = input("请输入CSV文件路径: ").strip()
    if not os.path.exists(csv_path):
        print("文件不存在!")
        return
    
    window_size = int(input("请输入分析窗口大小 (建议20-40): ").strip() or "30")
    threshold = float(input("请输入置信度阈值 (建议0.8-0.9): ").strip() or "0.8")
    min_n_score = float(input("请输入最小N字得分 (建议1.0): ").strip() or "1.0")
    
    # 分析文件
    n_patterns = predictor.find_n_patterns(csv_path, window_size, threshold=threshold, min_n_score=min_n_score)
    
    if n_patterns:
        print(f"\n找到 {len(n_patterns)} 个N字形态:")
        for i, pattern in enumerate(n_patterns):
            print(f"  N字形态 {i+1}: 位置 {pattern['start_index']}-{pattern['end_index']}, "
                  f"置信度 {pattern['confidence']:.3f}, N字得分 {pattern['n_pattern_score']:.1f}")
        
        # 可视化
        predictor.visualize_results(csv_path, n_patterns, save_plot=True)
    else:
        print("未找到符合条件的N字形态")
        print("建议:")
        print("1. 降低置信度阈值")
        print("2. 降低N字得分要求")
        print("3. 调整窗口大小")

if __name__ == "__main__":
    main()
