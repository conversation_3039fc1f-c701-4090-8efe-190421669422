#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用训练好的CNN模型预测CSV数据中的N字形态
将CSV数据转换为图像，然后使用CNN模型进行预测
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import cv2
from n_pattern_cnn_trainer import NPatternCNNTrainer
import tempfile
from typing import List, Dict

class CSVToImageConverter:
    """CSV数据转图像转换器"""
    
    def __init__(self, img_size=(224, 224)):
        self.img_size = img_size
    
    def csv_to_image(self, csv_path: str, save_path: str = None) -> str:
        """将CSV文件转换为K线图图像"""
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path)
            
            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', 'low', 'Low', 'open', 'Open']
            price_data = None
            
            for col in price_columns:
                if col in df.columns:
                    price_data = df[col].values
                    break
            
            if price_data is None:
                # 如果没找到明确的价格列，使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_data = df[numeric_cols[0]].values
                else:
                    raise ValueError("CSV文件中没有找到价格数据")
            
            # 移除NaN值
            price_data = price_data[~np.isnan(price_data)]
            
            if len(price_data) < 10:
                raise ValueError("价格数据点太少")
            
            # 创建图像
            img_path = self._create_price_chart_image(price_data, save_path)
            
            return img_path
            
        except Exception as e:
            print(f"转换CSV到图像时出错 {csv_path}: {e}")
            return None
    
    def _create_price_chart_image(self, prices: np.ndarray, save_path: str = None) -> str:
        """创建价格图表图像"""
        # 创建图表
        fig, ax = plt.subplots(figsize=(8, 6), dpi=100)
        
        # 绘制价格线
        x = np.arange(len(prices))
        ax.plot(x, prices, 'b-', linewidth=2)
        
        # 设置图表样式
        ax.set_facecolor('white')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('Time')
        ax.set_ylabel('Price')
        
        # 移除边框和刻度
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 保存图像
        if save_path is None:
            save_path = tempfile.mktemp(suffix='.png')
        
        plt.savefig(save_path, bbox_inches='tight', pad_inches=0, 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        return save_path

class CNNCSVPredictor:
    """使用CNN模型预测CSV数据的N字形态"""
    
    def __init__(self, model_path: str):
        self.trainer = NPatternCNNTrainer()
        self.converter = CSVToImageConverter()
        
        # 加载模型
        print(f"加载CNN模型: {model_path}")
        self.trainer.load_model(model_path)
    
    def predict_csv(self, csv_path: str, threshold: float = 0.5, save_image: bool = False) -> Dict:
        """预测CSV文件中的N字形态"""
        try:
            # 转换CSV为图像
            if save_image:
                img_name = os.path.splitext(os.path.basename(csv_path))[0] + '_chart.png'
                img_path = self.converter.csv_to_image(csv_path, img_name)
            else:
                img_path = self.converter.csv_to_image(csv_path)
            
            if img_path is None:
                return {'error': '无法转换CSV为图像'}
            
            # 使用CNN模型预测
            result = self.trainer.predict_image(img_path, threshold)
            
            # 添加CSV相关信息
            result['csv_path'] = csv_path
            result['image_path'] = img_path if save_image else None
            
            # 如果不保存图像，删除临时文件
            if not save_image and os.path.exists(img_path):
                os.remove(img_path)
            
            return result
            
        except Exception as e:
            return {'error': f'预测时出错: {str(e)}'}
    
    def batch_predict_csv(self, csv_dir: str, threshold: float = 0.5, 
                         save_images: bool = False) -> List[Dict]:
        """批量预测CSV文件目录"""
        # 收集CSV文件
        csv_files = []
        for ext in ['*.csv', '*.txt']:
            import glob
            csv_files.extend(glob.glob(os.path.join(csv_dir, ext)))
        
        if not csv_files:
            print("目录中没有找到CSV文件!")
            return []
        
        print(f"开始批量预测 {len(csv_files)} 个CSV文件...")
        
        results = []
        for i, csv_file in enumerate(csv_files):
            print(f"预测 {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}")
            result = self.predict_csv(csv_file, threshold, save_images)
            results.append(result)
        
        # 统计结果
        n_patterns_found = sum(1 for r in results if r.get('is_n_pattern', False))
        print(f"\n预测完成! 找到 {n_patterns_found} 个可能的N字形态")
        
        return results


def main():
    """主函数"""
    print("=== 使用CNN模型预测CSV中的N字形态 ===\n")
    
    # 检查模型文件
    model_path = "n_pattern_cnn_model.h5"
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先运行 n_pattern_cnn_trainer.py 训练模型")
        return
    
    # 创建预测器
    try:
        predictor = CNNCSVPredictor(model_path)
    except Exception as e:
        print(f"加载模型失败: {e}")
        return
    
    # 选择预测模式
    print("请选择预测模式:")
    print("1. 预测单个CSV文件")
    print("2. 批量预测目录中的CSV文件")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 单个文件预测
        csv_path = input("请输入CSV文件路径: ").strip()
        if not os.path.exists(csv_path):
            print("文件不存在!")
            return
        
        threshold = float(input("请输入判断阈值 (0-1, 默认0.5): ").strip() or "0.5")
        save_image = input("是否保存生成的图像? (y/n, 默认n): ").strip().lower() == 'y'
        
        result = predictor.predict_csv(csv_path, threshold, save_image)
        
        if 'error' in result:
            print(f"预测失败: {result['error']}")
        else:
            print(f"\n=== 预测结果 ===")
            print(f"CSV文件: {result['csv_path']}")
            print(f"预测结果: {result['prediction']}")
            print(f"是否包含N字形态: {'是' if result['is_n_pattern'] else '否'}")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"N字形态概率: {result['probability_positive']:.3f}")
            if result.get('image_path'):
                print(f"生成的图像: {result['image_path']}")
    
    elif choice == "2":
        # 批量预测
        csv_dir = input("请输入包含CSV文件的目录路径: ").strip()
        if not os.path.exists(csv_dir):
            print("目录不存在!")
            return
        
        threshold = float(input("请输入判断阈值 (0-1, 默认0.5): ").strip() or "0.5")
        save_images = input("是否保存生成的图像? (y/n, 默认n): ").strip().lower() == 'y'
        
        results = predictor.batch_predict_csv(csv_dir, threshold, save_images)
        
        # 显示结果
        print(f"\n=== 批量预测结果 ===")
        n_patterns = [r for r in results if r.get('is_n_pattern', False)]
        
        if n_patterns:
            print(f"找到 {len(n_patterns)} 个可能的N字形态:")
            for result in n_patterns:
                if 'error' not in result:
                    print(f"  {os.path.basename(result['csv_path'])}: 置信度 {result['confidence']:.3f}")
        else:
            print("没有找到N字形态")
        
        # 保存结果
        save_results = input("\n是否保存预测结果到JSON文件? (y/n): ").strip().lower()
        if save_results == 'y':
            import json
            results_path = input("请输入结果文件路径 (默认: csv_prediction_results.json): ").strip()
            if not results_path:
                results_path = "csv_prediction_results.json"
            
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"预测结果已保存到: {results_path}")
    
    else:
        print("无效的选择!")


if __name__ == "__main__":
    main()
