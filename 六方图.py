import numpy as np
import matplotlib.pyplot as plt
import math  # 导入 math 库
import sys
from PyQt6.QtWidgets import QMainWindow, QApplication
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文显示
plt.rcParams['axes.unicode_minus'] = False    # 负号显示

# 完整圈层配置
all_circles = [
    # 第1圈层 (1-6)
    {"range": (1, 6), "r": 3.0 * 2.0, "step": 60.0, "ref_num": 1, "ref_angle": 60.0},

    # 第2圈层 (7-18)
    {"range": (7, 18), "r": 6.0 * 2.0, "step": 30.0, "ref_num": 7, "ref_angle": 30.0},

    # 第3圈层 (19-36)
    {"range": (19, 36), "r": 9.0 * 2.0, "step": 20.0, "ref_num": 19, "ref_angle": 20.0},

    # 第4圈层 (37-60)
    {"range": (37, 60), "r": 12.0 * 2.0, "step": 15.0, "ref_num": 37, "ref_angle": 15.0},

    # 第5圈层 (61-90)
    {"range": (61, 90), "r": 15.0 * 2.0, "step": 12.0, "ref_num": 61, "ref_angle": 12.0},

    # 第6圈层 (91-126)
    {"range": (91, 126), "r": 18.0 * 2.0, "step": 10.0, "ref_num": 91, "ref_angle": 10.0},

    # 第7圈层 (127-168)
    {"range": (127, 168), "r": 21.0 * 2.0, "step": 8.571, "ref_num": 127, "ref_angle": 8.571},

    # 第8圈层 (169-216)
    {"range": (169, 216), "r": 24.0 * 2.0, "step": 7.5, "ref_num": 169, "ref_angle": 7.5},

    # 第9圈层 (217-270)
    {"range": (217, 270), "r": 27.0 * 2.0, "step": 6.667, "ref_num": 217, "ref_angle": 6.667},

    # 第10圈层 (271-330)
    {"range": (271, 330), "r": 30.0 * 2.0, "step": 6.0, "ref_num": 271, "ref_angle": 6.0},

    # 第11圈层 (331-396)
    {"range": (331, 396), "r": 33.0 * 2.0, "step": 5.455, "ref_num": 331, "ref_angle": 5.455},

    # 第12圈层 (397-468)
    {"range": (397, 468), "r": 36.0 * 2.0, "step": 5.0, "ref_num": 397, "ref_angle": 5.0},

    # 第13圈层 (469-546)
    {"range": (469, 546), "r": 39.0 * 2.0, "step": 4.615, "ref_num": 469, "ref_angle": 4.615},

    # 第14圈层 (547-630)
    {"range": (547, 630), "r": 42.0 * 2.0, "step": 4.286, "ref_num": 547, "ref_angle": 4.286},

    # 第15圈层 (631-720)
    {"range": (631, 720), "r": 45.0 * 2.0, "step": 4.0, "ref_num": 631, "ref_angle": 4.0},

    # 第16圈层 (721-816)
    {"range": (721, 816), "r": 48.0 * 2.0, "step": 3.75, "ref_num": 721, "ref_angle": 3.75},

    # 第17圈层 (817-918)
    {"range": (817, 918), "r": 51.0 * 2.0, "step": 3.529, "ref_num": 817, "ref_angle": 3.529},

    # 第18圈层 (919-1026)
    {"range": (919, 1026), "r": 54.0 * 2.0, "step": 3.333, "ref_num": 919, "ref_angle": 3.333}
]

# 定义需要高亮显示的数字
highlight_digits_red = {6, 18, 36, 60, 90, 126, 147, 168, 216, 270, 330, 507, 546, 867, 918, 945, 972, 999, 1026}

# 定义需要高亮显示的角度
highlight_angles_red = {0, 60, 90, 120, 180, 240, 270, 300, 360}
highlight_angles_green = {30, 150, 210, 330}

# 存储每个数字的位置信息
digits = []

# 生成数字位置信息
for circle in all_circles:
    start_digit, end_digit = circle["range"]
    radius = circle["r"]
    step_angle = circle["step"]
    ref_num = circle["ref_num"]
    ref_angle = circle["ref_angle"]

    for digit in range(start_digit, end_digit + 1):
        angle = (digit - ref_num) * step_angle + ref_angle
        # 规范化角度到0-360范围
        angle = angle % 360
        radian = math.radians(angle)
        x = radius * math.cos(radian)
        y = radius * math.sin(radian)
        digits.append((digit, angle, radius, x, y))

class HexagonChart:
    def __init__(self, parent=None):
        self.window = QMainWindow(parent)
        self.window.setWindowTitle('六方图分析')
        # 修改窗口大小
        self.window.setGeometry(200, 200, 1200, 1200)

        # 修改图形大小
        self.figure = Figure(figsize=(15, 15))
        self.canvas = FigureCanvas(self.figure)
        self.window.setCentralWidget(self.canvas)

        # 设置极坐标系
        self.ax = self.figure.add_subplot(111, polar=True)

        # 使用全局定义的圈层配置

    def plot_hexagon(self, manual_points=None):
        """绘制六方图"""
        # 清除现有图形
        self.ax.clear()

        # 设置30度间隔的刻度
        angles_30deg = np.arange(0, 360, 30)
        self.ax.set_xticks(np.deg2rad(angles_30deg))
        self.ax.set_xticklabels([f'{int(deg)}°' for deg in angles_30deg])

        # 处理手工添加的点
        manual_high_numbers = set()
        manual_low_numbers = set()
        if manual_points:
            manual_high_numbers = {int(point['price'] * 10) for point in manual_points['high']}
            manual_low_numbers = {int(point['price'] * 10) for point in manual_points['low']}

        # 使用全局定义的数字位置信息

        # 使用全局定义的高亮配置

        # 绘制每个数字的位置
        for d in digits:
            # 判断是否在红色高亮方向
            is_highlight_red = d[0] in highlight_digits_red or \
                               math.isclose(d[1] % 360, 0, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 60, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 90, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 120, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 180, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 240, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 270, abs_tol=1.0) or \
                               math.isclose(d[1] % 360, 300, abs_tol=1.0)

            # 判断是否在绿色高亮方向
            is_highlight_green = math.isclose(d[1] % 360, 30, abs_tol=1.0) or \
                                 math.isclose(d[1] % 360, 150, abs_tol=1.0) or \
                                 math.isclose(d[1] % 360, 210, abs_tol=1.0) or \
                                 math.isclose(d[1] % 360, 330, abs_tol=1.0)

            # 设置颜色和字体属性
            if is_highlight_red:
                text_color = 'red'
            elif is_highlight_green:
                text_color = 'green'
            else:
                text_color = 'black'

            font_weight = 'bold' if is_highlight_red or is_highlight_green else 'normal'

            self.ax.scatter(np.radians(d[1]), d[2], s=20, color='white')
            self.ax.annotate(f"{d[0]}",
                            xy=(np.radians(d[1]), d[2] + 0.5),
                            fontsize=8,
                            color=text_color,
                            weight=font_weight,  # 主要方向加粗
                            ha='center',
                            va='center')

        # 在绘制数字时添加手工点的标记
        for d in digits:
            num = d[0]
            angle = d[1]
            radius = d[2]

            if num in manual_high_numbers:
                self.ax.scatter(np.radians(angle), radius + 0.5, marker='s', s=100, color='orange', alpha=0.7)
            elif num in manual_low_numbers:
                self.ax.scatter(np.radians(angle), radius - 0.5, marker='s', s=100, color='blue', alpha=0.7)

        # 设置标题
        self.ax.set_title("主要方向数字高亮显示", pad=20)
        self.ax.set_yticklabels([])
        # 显示图形
        self.canvas.draw()

# 示例使用
if __name__ == "__main__":
    app = QApplication(sys.argv)
    chart = HexagonChart()
    chart.plot_hexagon()
    sys.exit(app.exec_())
