# N字形态识别工具使用说明

## 简介

本工具用于从K线数据中识别N字形态，支持日线和周线数据的分析。工具使用OpenCV进行图像处理和形态识别，通过PyQt5提供图形用户界面。

## 安装依赖

由于本工具依赖于PyQt5、matplotlib、mplfinance和OpenCV等库，请确保已安装以下依赖：

```bash
pip install PyQt5>=5.15.0 matplotlib>=3.5.0 mplfinance>=0.12.0 opencv-python>=4.5.0 pandas>=1.3.0 numpy>=1.20.0
```

或者直接使用项目中的requirements.txt安装所有依赖：

```bash
pip install -r requirements.txt
```

## 注意事项

原始程序使用PyQt6，但由于兼容性问题，已修改为使用更广泛支持的PyQt5。如果您的环境中已安装PyQt6，也可以尝试使用原始版本。

## 使用方法

1. 运行程序：

```bash
python n_shape_finder_gui.py
```

2. 在界面中点击"选择文件