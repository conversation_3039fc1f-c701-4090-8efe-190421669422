# fx_visualization.py
# -*- coding: utf-8 -*-
import pandas as pd
from datetime import datetime
from typing import List
from czsc.objects import RawBar, NewBar, FX, Mark
from czsc.enum import Direction
from czsc.utils.echarts_plot import kline_pro

def csv_to_raw_bars(file_path: str, symbol: str, freq: str) -> List[RawBar]:
    """将CSV转换为RawBar对象列表"""
    df = pd.read_csv(file_path, parse_dates=['Date'])
    raw_bars = []
    for idx, row in df.iterrows():
        bar = RawBar(
            symbol=symbol,
            dt=row['Date'],
            id=idx,
            freq=freq,
            open=row['Open'],
            close=row['Close'],
            high=row['High'],
            low=row['Low'],
            vol=row['Volume'],
            amount=row['Amount']
        )
        raw_bars.append(bar)
    return raw_bars

def process_include(raw_bars: List[RawBar]) -> List[NewBar]:
    """处理包含关系（直接调用analyze.py的remove_include）"""
    from czsc.analyze import remove_include
    
    if len(raw_bars) < 3:
        return []
    
    # 初始化前两根K线
    k1 = NewBar(**raw_bars[0].__dict__, elements=[raw_bars[0]])
    k2 = NewBar(**raw_bars[1].__dict__, elements=[raw_bars[1]])
    new_bars = [k1, k2]
    
    # 从第三根开始处理
    for bar in raw_bars[2:]:
        has_include, new_k = remove_include(new_bars[-2], new_bars[-1], bar)
        if has_include:
            new_bars[-1] = new_k
        else:
            new_bars.append(new_k)
    return new_bars

def find_fractals(new_bars: List[NewBar]) -> List[FX]:
    """识别分型"""
    fractals = []
    for i in range(1, len(new_bars)-1):
        prev, current, next_ = new_bars[i-1], new_bars[i], new_bars[i+1]
        
        # 顶分型条件
        if current.high > prev.high and current.high > next_.high:
            fractals.append(FX(
                symbol=current.symbol, 
                dt=current.dt,
                mark=Mark.DING,
                high=current.high,
                low=current.low
            ))
        
        # 底分型条件
        elif current.low < prev.low and current.low < next_.low:
            fractals.append(FX(
                symbol=current.symbol,
                dt=current.dt,
                mark=Mark.DI,
                high=current.high,
                low=current.low
            ))
    return fractals

def visualize_results(new_bars: List[NewBar], fractals: List[FX]):
    """可视化结果"""
    kline = [{
        'dt': bar.dt.strftime("%Y-%m-%d"),
        'open': bar.open,
        'close': bar.close,
        'high': bar.high,
        'low': bar.low
    } for bar in new_bars]
    
    annotations = [{
        'dt': fx.dt.strftime("%Y-%m-%d"),
        'value': fx.high if fx.mark == Mark.DING else fx.low,
        'mark': fx.mark.value,
        'marker': 'triangle' if fx.mark == Mark.DING else 'circle',
        'color': '#FF0000' if fx.mark == Mark.DING else '#00FF00'
    } for fx in fractals]
    
    kline_pro(kline, annotations=annotations, title="分型识别结果")

if __name__ == '__main__':
    # 配置参数
    csv_path = "D:/czsc-master/stock_a.csv"
    symbol = "000001.SH"
    freq = "日线"
    
    # 处理流程
    raw_bars = csv_to_raw_bars(csv_path, symbol, freq)
    new_bars = process_include(raw_bars)
    fractals = find_fractals(new_bars)
    
    # 结果展示
    visualize_results(new_bars, fractals)
