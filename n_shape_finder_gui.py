import sys
import os
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                             QPushButton, QFileDialog, QLabel, QComboBox, QHBoxLayout)
from PyQt5.QtCore import Qt

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import mplfinance as mpf

import cv2

# 导入数据处理和形态识别函数
from n_shape_finder_cv import read_kline_data, aggregate_to_daily, aggregate_to_weekly, find_n_shape_cv, Freq, RawBar

class NShapeFinderGUI(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle('N字形态识别工具')
        self.setGeometry(100, 100, 1200, 800)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # --- 控制面板 ----
        self.control_panel = QHBoxLayout()
        self.layout.addLayout(self.control_panel)

        # 文件选择
        self.file_label = QLabel("数据文件:")
        self.control_panel.addWidget(self.file_label)
        self.file_path_label = QLabel("未选择文件")
        self.control_panel.addWidget(self.file_path_label)
        self.select_file_button = QPushButton("选择文件")
        self.select_file_button.clicked.connect(self.select_file)
        self.control_panel.addWidget(self.select_file_button)

        # 频率选择
        self.freq_label = QLabel("频率:")
        self.control_panel.addWidget(self.freq_label)
        self.freq_combobox = QComboBox()
        self.freq_combobox.addItem("日线", Freq.D)
        self.freq_combobox.addItem("周线", Freq.W)
        self.control_panel.addWidget(self.freq_combobox)

        # 识别按钮
        self.find_button = QPushButton("识别N字形态")
        self.find_button.clicked.connect(self.find_n_shapes)
        self.control_panel.addWidget(self.find_button)

        # --- 图表区域 ----
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        self.layout.addWidget(self.canvas)

        # 初始化图表 - 使用单一坐标轴，mplfinance会自动处理子图
        self.ax = self.figure.add_subplot(111)
        self.ax.set_title('请加载数据文件')
        self.canvas.draw()

        self.raw_bars = []
        self.current_bars = []

    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择K线数据文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if file_path:
            self.file_path_label.setText(file_path)
            self.load_data(file_path)

    def load_data(self, file_path):
        self.raw_bars = read_kline_data(file_path)
        if self.raw_bars:
            self.update_chart()

    def update_chart(self):
        if not self.raw_bars:
            return

        selected_freq = self.freq_combobox.currentData()
        if selected_freq == Freq.D:
            self.current_bars = aggregate_to_daily(self.raw_bars)
        elif selected_freq == Freq.W:
            self.current_bars = aggregate_to_weekly(self.raw_bars)
        else:
            self.current_bars = self.raw_bars # Default to raw data if needed

        if not self.current_bars:
            print("聚合数据失败或数据为空")
            return

        # 清空旧图表
        self.figure.clear()

        # 绘制K线图
        df = pd.DataFrame([{
            'Date': bar.dt,
            'Open': bar.open,
            'High': bar.high,
            'Low': bar.low,
            'Close': bar.close,
            'Volume': bar.vol
        } for bar in self.current_bars])
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)

        # 创建子图
        ax1 = self.figure.add_subplot(2, 1, 1)  # 主图
        ax2 = self.figure.add_subplot(2, 1, 2)  # 成交量图
        
        # 使用mplfinance绘制K线图，不使用volume参数
        mpf.plot(df, type='candle', style='yahoo', 
                title=f'K线图 ({selected_freq.value})',
                ax=ax1, volume=ax2, figsize=(12, 8))
        
        self.canvas.draw()

    def find_n_shapes(self):
        if not self.current_bars:
            print("请先加载数据")
            return

        n_shapes = find_n_shape_cv(self.current_bars)
        print(f"识别到 {len(n_shapes)} 个N字形态")

        # 清除之前的图表
        self.figure.clear()
        
        # 重新绘制K线图
        df = pd.DataFrame([{
            'Date': bar.dt,
            'Open': bar.open,
            'High': bar.high,
            'Low': bar.low,
            'Close': bar.close,
            'Volume': bar.vol
        } for bar in self.current_bars])
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)

        selected_freq = self.freq_combobox.currentData()
        
        # 准备N字形态标记
        vlines = []
        for shape in n_shapes:
            start_idx = shape['start_idx']
            end_idx = shape['end_idx']
            start_dt = self.current_bars[start_idx].dt
            end_dt = self.current_bars[end_idx].dt
            # 添加垂直线标记N字形态的开始和结束
            vlines.append((start_dt, end_dt))
        
        # 创建子图
        ax1 = self.figure.add_subplot(2, 1, 1)  # 主图
        ax2 = self.figure.add_subplot(2, 1, 2)  # 成交量图
        
        # 使用mplfinance绘制K线图，并添加N字形态标记
        mpf.plot(df, type='candle', style='yahoo', 
                title=f'K线图 ({selected_freq.value}) - 识别到 {len(n_shapes)} 个N字形态',
                ax=ax1, volume=ax2, figsize=(12, 8),
                vspan=vlines, vspan_alpha=0.3, vspan_color='yellow')
        
        # 刷新图表
        self.canvas.draw()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = NShapeFinderGUI()
    window.show()  # 显示主窗口
    sys.exit(app.exec_())