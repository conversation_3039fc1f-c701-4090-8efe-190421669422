# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
from czsc.analyze import CZSC
from czsc.objects import RawBar, Signal
from czsc.enum import Freq
from czsc.signals.bar import bar_single_V230506
from datetime import datetime

# 创建示例数据
def create_sample_data(n=200):
    """创建示例K线数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2020-01-01', periods=n, freq='15min')
    close = np.random.normal(loc=0.1, scale=0.5, size=n).cumsum() + 100
    open_prices = close - np.random.normal(loc=0, scale=0.2, size=n)
    high = np.maximum(close, open_prices) + np.random.normal(loc=0.2, scale=0.1, size=n)
    low = np.minimum(close, open_prices) - np.random.normal(loc=0.2, scale=0.1, size=n)
    
    bars = []
    for i in range(n):
        bar = RawBar(symbol="000001.SH", id=i, dt=dates[i], freq=Freq.F15,
                    open=open_prices[i], close=close[i], high=high[i], low=low[i],
                    vol=np.random.randint(1000, 10000), amount=close[i] * 10000)
        bars.append(bar)
    return bars

# 创建K线数据
bars = create_sample_data(200)

# 创建CZSC对象
czsc_obj = CZSC(bars)

# 计算信号
signals = bar_single_V230506(czsc_obj, di=1, n=5)

# 打印信号
print("计算得到的信号:")
for k, v in signals.items():
    print(f"{k}: {v}")
