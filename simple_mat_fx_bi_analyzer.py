#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分型和笔的识别与可视化 - PyQt6版本

该脚本用于读取CSV或TXT格式的K线数据，识别顶底分型和笔，并使用PyQt6实现交互界面。
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
import math
import time
from datetime import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Any, Callable
from collections import OrderedDict
from enum import Enum
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import mplfinance as mpf
import cv2
from matplotlib.patches import Rectangle, Patch
from matplotlib.lines import Line2D
from matplotlib.figure import Figure
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar

# PyQt6相关导入
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QFileDialog, QLabel, QComboBox, QSpinBox, QDoubleSpinBox,
                             QStatusBar, QSplitter, QMessageBox, QGroupBox, QFormLayout,
                             QInputDialog, QDialog, QTextEdit, QToolBar, QSizePolicy,
                             QScrollArea, QRadioButton, QListWidget, QListWidgetItem,
                             QDialogButtonBox)
from PyQt6.QtGui import QIcon, QFont, QAction
from PyQt6.QtCore import Qt, QSize

# 设置中文字体 - 简单直接的方式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False


# 定义必要的枚举类型
class Mark(Enum):
    D = "底分型"
    G = "顶分型"


class Direction(Enum):
    Up = "向上"
    Down = "向下"


class Freq(Enum):
    F1 = "1分钟"
    F5 = "5分钟"
    F15 = "15分钟"
    F30 = "30分钟"
    F60 = "60分钟"
    D = "日线"
    W = "周线"
    M = "月线"
    S = "季线"
    Y = "年线"

    @classmethod
    def _missing_(cls, value):
        if isinstance(value, str):
            for freq in cls:
                if freq.value == value:
                    return freq
        return None


# 定义必要的数据类
@dataclass
class RawBar:
    """原始K线元素"""
    symbol: str
    id: int  # id 必须是升序
    dt: datetime
    freq: Freq
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float
    cache: dict = field(default_factory=dict)  # cache 用户缓存


@dataclass
class NewBar:
    """去除包含关系后的K线元素"""
    symbol: str
    id: int  # id 必须是升序
    dt: datetime
    freq: Freq
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float
    elements: List = field(default_factory=list)  # 存入具有包含关系的原始K线
    cache: dict = field(default_factory=dict)  # cache 用户缓存

    @property
    def raw_bars(self):
        return self.elements


@dataclass
class FX:
    """分型"""
    symbol: str
    dt: datetime
    mark: Mark
    high: float
    low: float
    fx: float
    elements: List = field(default_factory=list)
    cache: dict = field(default_factory=dict)  # cache 用户缓存


@dataclass
class BI:
    """笔"""
    symbol: str
    fx_a: FX    # 笔开始的分型
    fx_b: FX    # 笔结束的分型
    fxs: List   # 笔内部的分型列表
    direction: Direction
    bars: List[NewBar] = field(default_factory=list)
    cache: dict = field(default_factory=dict)  # cache 用户缓存

    def __post_init__(self):
        self.sdt = self.fx_a.dt
        self.edt = self.fx_b.dt

    @property
    def high(self):
        return max(self.fx_a.high, self.fx_b.high)

    @property
    def low(self):
        return min(self.fx_a.low, self.fx_b.low)


@dataclass
class ZS:
    """中枚对象，用于识别和显示中枚"""

    bis: List[BI]  # 构成中枚的笔列表
    cache: dict = field(default_factory=dict)  # cache 用户缓存

    def __post_init__(self):
        self.symbol = self.bis[0].symbol

    @property
    def sdt(self):
        """中枚开始时间"""
        return self.bis[0].sdt

    @property
    def edt(self):
        """中枚结束时间"""
        return self.bis[-1].edt

    @property
    def sdir(self):
        """中枚第一笔方向，sdir 是 start direction 的缩写"""
        return self.bis[0].direction

    @property
    def edir(self):
        """中枚倒一笔方向，edir 是 end direction 的缩写"""
        return self.bis[-1].direction

    @property
    def zz(self):
        """中枚中轴"""
        return self.zd + (self.zg - self.zd) / 2

    @property
    def gg(self):
        """中枚最高点"""
        return max([x.high for x in self.bis])

    @property
    def zg(self):
        """中枚上沿"""
        return min([x.high for x in self.bis[:3]])

    @property
    def dd(self):
        """中枚最低点"""
        return min([x.low for x in self.bis])

    @property
    def zd(self):
        """中枚下沿"""
        return max([x.low for x in self.bis[:3]])

    @property
    def is_valid(self):
        """中枚是否有效"""
        if self.zg < self.zd:
            return False

        for bi in self.bis:
            # 中枚内的笔必须与中枚的上下沿有交集
            if (
                self.zg >= bi.high >= self.zd
                or self.zg >= bi.low >= self.zd
                or bi.high >= self.zg > self.zd >= bi.low
            ):
                continue
            else:
                return False

        return True

    def __repr__(self):
        return (
            f"ZS(sdt={self.sdt}, sdir={self.sdir}, edt={self.edt}, edir={self.edir}, "
            f"len_bis={len(self.bis)}, zg={self.zg}, zd={self.zd}, "
            f"gg={self.gg}, dd={self.dd}, zz={self.zz})"
        )


# 核心函数
def remove_include(k1: NewBar, k2: NewBar, k3: RawBar):
    """去除包含关系"""
    if k1.high < k2.high:
        direction = Direction.Up
    elif k1.high > k2.high:
        direction = Direction.Down
    else:
        k4 = NewBar(symbol=k3.symbol, id=k3.id, freq=k3.freq, dt=k3.dt, open=k3.open,
                    close=k3.close, high=k3.high, low=k3.low, vol=k3.vol, amount=k3.amount, elements=[k3])
        return False, k4

    # 判断 k2 和 k3 之间是否存在包含关系，有则处理
    if (k2.high <= k3.high and k2.low >= k3.low) or (k2.high >= k3.high and k2.low <= k3.low):
        if direction == Direction.Up:
            high = max(k2.high, k3.high)
            low = max(k2.low, k3.low)
            dt = k2.dt if k2.high > k3.high else k3.dt
        elif direction == Direction.Down:
            high = min(k2.high, k3.high)
            low = min(k2.low, k3.low)
            dt = k2.dt if k2.low < k3.low else k3.dt
        else:
            raise ValueError

        open_, close = (high, low) if k3.open > k3.close else (low, high)
        vol = k2.vol + k3.vol
        amount = k2.amount + k3.amount

        elements = [x for x in k2.elements[:100] if x.dt != k3.dt] + [k3]
        k4 = NewBar(symbol=k3.symbol, id=k2.id, freq=k2.freq, dt=dt, open=open_,
                    close=close, high=high, low=low, vol=vol, amount=amount, elements=elements)
        return True, k4
    else:
        k4 = NewBar(symbol=k3.symbol, id=k3.id, freq=k3.freq, dt=k3.dt, open=k3.open,
                    close=k3.close, high=k3.high, low=k3.low, vol=k3.vol, amount=k3.amount, elements=[k3])
        return False, k4


def check_fx(k1: NewBar, k2: NewBar, k3: NewBar):
    """查找分型"""
    fx = None
    if k1.high < k2.high > k3.high and k1.low < k2.low > k3.low:
        fx = FX(symbol=k1.symbol, dt=k2.dt, mark=Mark.G, high=k2.high,
                low=k2.low, fx=k2.high, elements=[k1, k2, k3])
    elif k1.high > k2.high < k3.high and k1.low > k2.low < k3.low:
        fx = FX(symbol=k1.symbol, dt=k2.dt, mark=Mark.D, high=k2.high,
                low=k2.low, fx=k2.low, elements=[k1, k2, k3])
    return fx


def check_fxs(bars: List[NewBar]) -> List[FX]:
    """输入一串无包含关系K线，查找其中所有分型"""
    fxs = []
    for i in range(1, len(bars) - 1):
        fx = check_fx(bars[i - 1], bars[i], bars[i + 1])
        if isinstance(fx, FX):
            # 默认情况下，fxs本身是顶底交替的，但是对于一些特殊情况下不是这样; 临时强制要求fxs序列顶底交替
            if len(fxs) >= 2 and fx.mark == fxs[-1].mark:
                print(f"check_fxs错误: {bars[i].dt}，{fx.mark}，{fxs[-1].mark}")
            else:
                fxs.append(fx)
    return fxs


def check_bi(bars: List[NewBar], min_bi_len=3):
    """输入一串无包含关系K线，查找其中的一笔"""
    fxs = check_fxs(bars)
    if len(fxs) < 2:
        return None, bars

    fx_a = fxs[0]
    if fx_a.mark == Mark.D:
        direction = Direction.Up
        fxs_b = (x for x in fxs if x.mark == Mark.G and x.dt > fx_a.dt and x.fx > fx_a.fx)
        fx_b = max(fxs_b, key=lambda fx: fx.high, default=None)
    elif fx_a.mark == Mark.G:
        direction = Direction.Down
        fxs_b = (x for x in fxs if x.mark == Mark.D and x.dt > fx_a.dt and x.fx < fx_a.fx)
        fx_b = min(fxs_b, key=lambda fx: fx.low, default=None)
    else:
        raise ValueError

    if fx_b is None:
        return None, bars

    bars_a = [x for x in bars if fx_a.elements[0].dt <= x.dt <= fx_b.elements[2].dt]
    bars_b = [x for x in bars if x.dt >= fx_b.elements[0].dt]

    # 判断fx_a和fx_b价格区间是否存在包含关系
    ab_include = (fx_a.high > fx_b.high and fx_a.low < fx_b.low) or (fx_a.high < fx_b.high and fx_a.low > fx_b.low)

    # 成笔的条件：1）顶底分型之间没有包含关系；2）笔长度大于等于min_bi_len
    if (not ab_include) and (len(bars_a) >= min_bi_len):
        fxs_ = [x for x in fxs if fx_a.elements[0].dt <= x.dt <= fx_b.elements[2].dt]
        bi = BI(symbol=fx_a.symbol, fx_a=fx_a, fx_b=fx_b, fxs=fxs_, direction=direction, bars=bars_a)
        return bi, bars_b
    else:
        return None, bars


class CZSC:
    # Placeholder for M-pattern model path
    m_pattern_model_path: str = None
    m_pattern_model: Any = None  # Placeholder for the loaded model
    identified_n_patterns = []  # 存储识别出的N字形态
    def __init__(self, bars: List[RawBar], max_bi_num=50, min_bi_len=3):
        """
        :param bars: K线数据
        :param max_bi_num: 最大允许保留的笔数量
        :param min_bi_len: 最小笔长度
        """
        self.max_bi_num = max_bi_num
        self.min_bi_len = min_bi_len
        self.bars_raw = []  # 原始K线序列
        self.bars_ubi = []  # 未完成笔的无包含K线序列
        self.bi_list = []
        self.symbol = bars[0].symbol
        self.freq = bars[0].freq
        self.cache = OrderedDict()
        self.m_pattern_model_path = None
        self.m_pattern_model = None
        self.identified_m_patterns = [] # To store identified M-patterns
        self.identified_n_patterns = [] # To store identified N-patterns

        for bar in bars:
            self.update(bar)

    def __repr__(self):
        return f"<CZSC~{self.symbol}~{self.freq.value}>"

    def __update_bi(self):
        bars_ubi = self.bars_ubi
        if len(bars_ubi) < 3:
            return

        # 查找笔
        if not self.bi_list:
            # 第一笔的查找
            fxs = check_fxs(bars_ubi)
            if not fxs:
                return

            fx_a = fxs[0]
            fxs_a = [x for x in fxs if x.mark == fx_a.mark]
            for fx in fxs_a:
                if (fx_a.mark == Mark.D and fx.low <= fx_a.low) \
                        or (fx_a.mark == Mark.G and fx.high >= fx_a.high):
                    fx_a = fx
            bars_ubi = [x for x in bars_ubi if x.dt >= fx_a.elements[0].dt]

            bi, bars_ubi_ = check_bi(bars_ubi, min_bi_len=self.min_bi_len)
            if isinstance(bi, BI):
                self.bi_list.append(bi)
            self.bars_ubi = bars_ubi_
            return

        bi, bars_ubi_ = check_bi(bars_ubi, min_bi_len=self.min_bi_len)
        self.bars_ubi = bars_ubi_
        if isinstance(bi, BI):
            self.bi_list.append(bi)

        # 后处理：如果当前笔被破坏，将当前笔的bars与bars_ubi进行合并，并丢弃
        last_bi = self.bi_list[-1]
        bars_ubi = self.bars_ubi
        if (last_bi.direction == Direction.Up and bars_ubi[-1].high > last_bi.high) \
                or (last_bi.direction == Direction.Down and bars_ubi[-1].low < last_bi.low):
            # 当前笔被破坏，将当前笔的bars与bars_ubi进行合并，并丢弃，这里容易出错，多一根K线就可能导致错误
            # 必须是 -2，因为最后一根无包含K线有可能是未完成的
            self.bars_ubi = last_bi.bars[:-2] + [x for x in bars_ubi if x.dt >= last_bi.bars[-2].dt]
            self.bi_list.pop(-1)

    def load_m_pattern_model(self, model_path: str):
        """加载预训练的M形态识别模型"""
        # 此处为加载模型的示例代码，需要用户根据实际模型库进行修改
        # 例如，如果使用 TensorFlow/Keras:
        # from tensorflow.keras.models import load_model
        # self.m_pattern_model = load_model(model_path)
        self.m_pattern_model_path = model_path
        print(f"M形态识别模型已加载: {model_path}")
        # 实际加载逻辑待用户实现
        pass

    def generate_m_pattern_image(self, kline_segment: List[RawBar]):
        """将K线片段转化为标准化的图像，用于M形态识别"""
        # 此处为生成图像的示例代码，需要用户根据实际需求实现
        # 1. 选择K线片段
        # 2. 将K线数据绘制成图像 (例如使用 matplotlib)
        # 3. 对图像进行预处理 (例如，调整大小、归一化)
        # 返回预处理后的图像数据
        print(f"为M形态识别生成图像，K线片段长度: {len(kline_segment)}")
        # 实际图像生成逻辑待用户实现
        return None # Placeholder

    def identify_extended_m_pattern(self, kline_data: List[RawBar], window_size: int = 60, step: int = 1):
        """识别左边延长的M形态

        :param kline_data: 原始K线数据列表
        :param window_size: 用于生成图像的K线窗口大小
        :param step: 窗口滑动的步长
        :return: 识别到的M形态位置列表 (例如，[{"start_dt": datetime, "end_dt": datetime}, ...])
        """
        if not self.m_pattern_model:
            print("M形态识别模型未加载，请先调用 load_m_pattern_model 方法加载模型。")
            return []

        identified_patterns = []
        if len(kline_data) < window_size:
            return []

        for i in range(0, len(kline_data) - window_size + 1, step):
            segment = kline_data[i : i + window_size]
            image_data = self.generate_m_pattern_image(segment)

            if image_data is not None:
                # 使用加载的模型进行预测
                # prediction = self.m_pattern_model.predict(image_data)
                # 此处为调用模型进行预测的示例代码，需要用户根据实际模型进行修改
                # 假设模型输出一个概率值，大于某个阈值则认为是M形态
                # is_m_pattern = prediction > 0.8 # 示例阈值
                is_m_pattern = False # Placeholder for actual prediction logic

                if is_m_pattern:
                    pattern_info = {
                        "start_dt": segment[0].dt,
                        "end_dt": segment[-1].dt,
                        "start_idx": segment[0].id, # Assuming RawBar has an id
                        "end_idx": segment[-1].id
                    }
                    identified_patterns.append(pattern_info)
                    print(f"识别到M形态: {pattern_info}")

        self.identified_m_patterns = identified_patterns
        return identified_patterns

    def identify_n_pattern(self, window_size: int = 30, min_trend_length: int = 5, slope_threshold: float = 0.2,
                          price_change_threshold: float = 0.05, amplitude_threshold: float = 0.1):
        """识别N字形态

        N字形态的特征：左边一段上涨后，然后形成N字形的形态（先跌后涨再跌）

        :param window_size: 滑动窗口大小，用于检测局部趋势
        :param min_trend_length: 最小趋势长度，用于过滤短期波动
        :param slope_threshold: 斜率阈值，用于判断趋势的强度
        :param price_change_threshold: 价格变化阈值，用于判断价格变化的显著性（相对于整体价格的百分比）
        :param amplitude_threshold: 振幅阈值，用于判断N字形态的振幅是否足够大（相对于整体价格的百分比）
        :return: 识别到的N字形态位置列表
        """
        if not self.bars_raw or len(self.bars_raw) < window_size * 2:
            print("数据不足，无法识别N字形态")
            return []

        # 提取价格序列
        closes = np.array([bar.close for bar in self.bars_raw])
        highs = np.array([bar.high for bar in self.bars_raw])
        lows = np.array([bar.low for bar in self.bars_raw])
        dates = [bar.dt for bar in self.bars_raw]

        # 计算整体价格范围，用于归一化
        price_range = np.max(highs) - np.min(lows)
        if price_range == 0:  # 避免除以零
            price_range = 1

        identified_patterns = []

        # 使用峰值检测方法查找可能的N字形态
        for i in range(window_size, len(closes) - window_size):
            # 在当前窗口内查找局部极值点
            window_start = max(0, i - window_size)
            window_end = min(len(closes), i + window_size)

            window_highs = highs[window_start:window_end]
            window_lows = lows[window_start:window_end]
            window_closes = closes[window_start:window_end]

            # 查找局部高点（使用收盘价和最高价）
            peak_indices = []
            for j in range(1, len(window_closes) - 1):
                if (window_closes[j] > window_closes[j-1] and
                    window_closes[j] > window_closes[j+1] and
                    window_highs[j] >= window_highs[j-1] and
                    window_highs[j] >= window_highs[j+1]):
                    peak_indices.append(j + window_start)

            # 查找局部低点（使用收盘价和最低价）
            trough_indices = []
            for j in range(1, len(window_closes) - 1):
                if (window_closes[j] < window_closes[j-1] and
                    window_closes[j] < window_closes[j+1] and
                    window_lows[j] <= window_lows[j-1] and
                    window_lows[j] <= window_lows[j+1]):
                    trough_indices.append(j + window_start)

            # 如果找到至少2个高点和2个低点，检查是否形成N字形态
            if len(peak_indices) >= 2 and len(trough_indices) >= 2:
                # 按时间顺序排序
                peak_indices.sort()
                trough_indices.sort()

                # 查找符合N字形态的点序列：高点-低点-高点-低点
                for p1_idx, peak1 in enumerate(peak_indices[:-1]):
                    for p2_idx, peak2 in enumerate(peak_indices[p1_idx+1:], p1_idx+1):
                        # 查找两个高点之间的低点
                        middle_troughs = [t for t in trough_indices if peak1 < t < peak2]

                        if not middle_troughs:
                            continue

                        middle_trough = middle_troughs[0]  # 取第一个低点

                        # 查找第二个高点之后的低点
                        end_troughs = [t for t in trough_indices if t > peak2]

                        if not end_troughs:
                            continue

                        end_trough = end_troughs[0]  # 取第一个低点

                        # 计算各点的价格
                        peak1_price = highs[peak1]
                        trough1_price = lows[middle_trough]
                        peak2_price = highs[peak2]
                        trough2_price = lows[end_trough]

                        # 计算价格变化幅度
                        drop1 = (peak1_price - trough1_price) / price_range
                        rise = (peak2_price - trough1_price) / price_range
                        drop2 = (peak2_price - trough2_price) / price_range

                        # 计算整体振幅
                        amplitude = max(peak1_price, peak2_price) - min(trough1_price, trough2_price)
                        relative_amplitude = amplitude / price_range

                        # 判断是否符合N字形态的条件
                        if (drop1 > price_change_threshold and  # 第一次下跌幅度足够大
                            rise > price_change_threshold and   # 上涨幅度足够大
                            drop2 > price_change_threshold and  # 第二次下跌幅度足够大
                            relative_amplitude > amplitude_threshold):  # 整体振幅足够大

                            # 找到形态的起始和结束位置
                            start_idx = max(0, peak1 - min_trend_length)  # 向前延伸一些
                            end_idx = min(len(closes) - 1, end_trough + min_trend_length)  # 向后延伸一些

                            # 记录N字形态信息
                            pattern_info = {
                                "start_dt": dates[start_idx],
                                "end_dt": dates[end_idx],
                                "start_idx": start_idx,
                                "end_idx": end_idx,
                                "pattern_type": "N形态",
                                "amplitude": relative_amplitude,
                                "key_points": [
                                    {"idx": peak1, "price": peak1_price, "type": "第一高点"},
                                    {"idx": middle_trough, "price": trough1_price, "type": "第一低点"},
                                    {"idx": peak2, "price": peak2_price, "type": "第二高点"},
                                    {"idx": end_trough, "price": trough2_price, "type": "第二低点"}
                                ]
                            }

                            identified_patterns.append(pattern_info)
                            print(f"识别到N字形态: 起始={dates[start_idx]}, 结束={dates[end_idx]}, 振幅={relative_amplitude:.2f}")

        # 合并重叠的形态，只保留最显著的一个
        if identified_patterns:
            filtered_patterns = [identified_patterns[0]]
            for pattern in identified_patterns[1:]:
                overlap = False
                for filtered in filtered_patterns:
                    # 如果两个形态的时间范围有50%以上的重叠，则认为是重叠的
                    if (pattern["start_idx"] < filtered["end_idx"] and
                        pattern["end_idx"] > filtered["start_idx"]):
                        overlap = True
                        # 保留形态更明显的一个（这里简单地用时间跨度来判断）
                        if (pattern["end_idx"] - pattern["start_idx"]) > (filtered["end_idx"] - filtered["start_idx"]):
                            filtered_patterns.remove(filtered)
                            filtered_patterns.append(pattern)
                        break
                if not overlap:
                    filtered_patterns.append(pattern)

            identified_patterns = filtered_patterns

        self.identified_n_patterns = identified_patterns
        return identified_patterns

    def identify_n_pattern_with_cv(self, kline_data=None, window_size: int = 60, step: int = 5,
                                  min_amplitude: float = 0.15, slope_threshold: float = 0.3):
        """使用OpenCV识别N字形态

        :param kline_data: 原始K线数据列表，如果为None则使用self.bars_raw
        :param window_size: 用于生成图像的K线窗口大小
        :param step: 窗口滑动的步长
        :param min_amplitude: 最小振幅阈值，用于过滤小幅波动
        :param slope_threshold: 斜率阈值，用于判断线段的倾斜程度
        :return: 识别到的N字形态位置列表
        """
        if kline_data is None:
            kline_data = self.bars_raw

        if not kline_data or len(kline_data) < window_size:
            print("数据不足，无法识别N字形态")
            return []

        identified_patterns = []
        total_segments = (len(kline_data) - window_size) // step + 1
        print(f"OpenCV N字形态检测开始: 总共{total_segments}个窗口, 窗口大小={window_size}, 步长={step}")
        print(f"最小振幅阈值={min_amplitude}, 斜率阈值={slope_threshold}")

        # 生成K线图像并使用OpenCV进行形态识别
        processed_count = 0
        for i in range(0, len(kline_data) - window_size + 1, step):
            segment = kline_data[i:i+window_size]
            processed_count += 1

            # 提取价格数据
            highs = np.array([x.high for x in segment])
            lows = np.array([x.low for x in segment])
            closes = np.array([x.close for x in segment])

            # 计算价格范围，用于振幅检测
            price_range = np.max(highs) - np.min(lows)
            if price_range == 0:
                print(f"窗口{processed_count}: 价格范围为0，跳过")
                continue

            # 检查振幅是否足够大
            relative_amplitude = price_range / np.mean(closes)
            print(f"窗口{processed_count}: 价格范围={price_range:.2f}, 相对振幅={relative_amplitude:.3f}")

            if relative_amplitude < min_amplitude:
                print(f"窗口{processed_count}: 振幅{relative_amplitude:.3f} < {min_amplitude}，跳过")
                continue  # 振幅太小，跳过

            # 创建高分辨率图像用于更精确的线段检测
            fig = plt.figure(figsize=(12, 8), dpi=150)
            ax = fig.add_subplot(111)

            # 绘制收盘价连线，使用更粗的线条
            x_coords = np.arange(len(closes))
            ax.plot(x_coords, closes, 'w-', linewidth=4, antialiased=True)

            # 设置黑色背景，白色线条，便于边缘检测
            ax.set_facecolor('black')
            fig.patch.set_facecolor('black')

            # 关闭坐标轴和边框
            ax.axis('off')
            ax.set_xlim(0, len(closes)-1)
            ax.set_ylim(np.min(closes)*0.98, np.max(closes)*1.02)

            # 将图表转换为图像
            fig.canvas.draw()
            # 兼容新旧版本的Matplotlib
            if hasattr(fig.canvas, 'tostring_rgb'):
                img_data = fig.canvas.tostring_rgb()
            else:
                img_data = fig.canvas.buffer_rgba()
            img = np.frombuffer(img_data, dtype=np.uint8)

            # 获取图像尺寸
            width, height = fig.canvas.get_width_height()

            # 重新整形图像
            if hasattr(fig.canvas, 'tostring_rgb'):
                img = img.reshape((height, width, 3))
            else:
                img = img.reshape((height, width, 4))
                # 如果是RGBA格式，转换为RGB
                img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
            plt.close(fig)

            # 转换为灰度图像
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)

            # 使用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # 使用更严格的Canny边缘检测参数
            edges = cv2.Canny(blurred, 100, 200, apertureSize=3)

            # 使用形态学操作连接断开的边缘
            kernel = np.ones((3,3), np.uint8)
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # 使用霍夫变换检测直线，降低阈值以检测更多线段
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=30, minLineLength=20, maxLineGap=20)

            if lines is None:
                print(f"窗口{processed_count}: 未检测到任何线段")
                continue
            elif len(lines) < 3:
                print(f"窗口{processed_count}: 只检测到{len(lines)}条线段，需要至少3条")
                continue  # 没有检测到足够的线段
            else:
                print(f"窗口{processed_count}: 检测到{len(lines)}条线段")

            # 分析检测到的线段，寻找N字形态
            valid_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]

                # 计算线段长度
                length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                if length < 15:  # 降低长度要求
                    continue

                # 确保x1 < x2
                if x1 > x2:
                    x1, x2 = x2, x1
                    y1, y2 = y2, y1

                # 计算线段斜率
                if x2 != x1:
                    slope = (y2 - y1) / (x2 - x1)
                    # 只考虑有明显斜率的线段
                    if abs(slope) > slope_threshold:
                        valid_lines.append((x1, y1, x2, y2, slope, length))

            if len(valid_lines) < 3:
                print(f"窗口{processed_count}: 有效线段只有{len(valid_lines)}条，需要至少3条")
                continue
            else:
                print(f"窗口{processed_count}: 有效线段{len(valid_lines)}条")

            # 按x坐标排序
            valid_lines.sort(key=lambda x: x[0])

            # 寻找N字形态：上升-下降-上升-下降 或 下降-上升-下降的模式
            found_n_pattern = False

            # 方法1：寻找经典的N字形态（上升-下降-上升-下降）
            for j in range(len(valid_lines) - 3):
                line1, line2, line3, line4 = valid_lines[j:j+4]

                # 检查是否形成N字形态：上升-下降-上升-下降
                if (line1[4] > slope_threshold and    # 第一段上升
                    line2[4] < -slope_threshold and   # 第二段下降
                    line3[4] > slope_threshold and    # 第三段上升
                    line4[4] < -slope_threshold):     # 第四段下降

                    # 检查线段的连续性（允许一定的间隔）
                    gap1 = abs(line1[2] - line2[0])
                    gap2 = abs(line2[2] - line3[0])
                    gap3 = abs(line3[2] - line4[0])

                    if gap1 < 30 and gap2 < 30 and gap3 < 30:
                        # 计算形态的价格振幅
                        y_coords = [line1[1], line1[3], line2[1], line2[3],
                                   line3[1], line3[3], line4[1], line4[3]]
                        pattern_amplitude = (max(y_coords) - min(y_coords)) / height

                        if pattern_amplitude > 0.1:  # 形态振幅足够大
                            found_n_pattern = True
                            break

            # 方法2：寻找简化的N字形态（下降-上升-下降）
            if not found_n_pattern:
                for j in range(len(valid_lines) - 2):
                    line1, line2, line3 = valid_lines[j:j+3]

                    # 检查是否形成简化N字形态：下降-上升-下降
                    if (line1[4] < -slope_threshold and   # 第一段下降
                        line2[4] > slope_threshold and    # 第二段上升
                        line3[4] < -slope_threshold):     # 第三段下降

                        # 检查线段的连续性
                        gap1 = abs(line1[2] - line2[0])
                        gap2 = abs(line2[2] - line3[0])

                        if gap1 < 30 and gap2 < 30:
                            # 计算形态的价格振幅
                            y_coords = [line1[1], line1[3], line2[1], line2[3], line3[1], line3[3]]
                            pattern_amplitude = (max(y_coords) - min(y_coords)) / height

                            if pattern_amplitude > 0.15:  # 简化形态需要更大的振幅
                                found_n_pattern = True
                                break

            if found_n_pattern:
                # 找到了N字形态，记录详细信息
                pattern_info = {
                    "start_dt": segment[0].dt,
                    "end_dt": segment[-1].dt,
                    "start_idx": i,
                    "end_idx": i + window_size - 1,
                    "pattern_type": "N形态(OpenCV增强)",
                    "amplitude": relative_amplitude,
                    "confidence": pattern_amplitude,
                    "price_range": price_range,
                    "key_points": [
                        {"idx": i, "price": closes[0], "type": "起始点"},
                        {"idx": i + window_size - 1, "price": closes[-1], "type": "结束点"}
                    ]
                }
                identified_patterns.append(pattern_info)
                print(f"使用OpenCV增强方法识别到N字形态: 起始={segment[0].dt}, 结束={segment[-1].dt}, 振幅={relative_amplitude:.3f}")

        self.identified_n_patterns = identified_patterns
        return identified_patterns

    def update(self, bar: RawBar):
        """更新分析结果"""
        # 更新K线序列
        if not self.bars_raw or bar.dt != self.bars_raw[-1].dt:
            self.bars_raw.append(bar)
            last_bars = [bar]
        else:
            # 当前 bar 是上一根 bar 的时间延伸
            self.bars_raw[-1] = bar
            last_bars = self.bars_ubi.pop(-1).raw_bars
            assert bar.dt == last_bars[-1].dt, f"{bar.dt} != {last_bars[-1].dt}，时间错位"
            last_bars[-1] = bar

        # 去除包含关系
        bars_ubi = self.bars_ubi
        for bar in last_bars:
            if len(bars_ubi) < 2:
                bars_ubi.append(NewBar(symbol=bar.symbol, id=bar.id, freq=bar.freq, dt=bar.dt,
                                       open=bar.open, close=bar.close, amount=bar.amount,
                                       high=bar.high, low=bar.low, vol=bar.vol, elements=[bar]))
            else:
                k1, k2 = bars_ubi[-2:]
                has_include, k3 = remove_include(k1, k2, bar)
                if has_include:
                    bars_ubi[-1] = k3
                else:
                    bars_ubi.append(k3)
        self.bars_ubi = bars_ubi

        # 更新笔
        self.__update_bi()

        # 根据最大笔数量限制完成 bi_list, bars_raw 序列的数量控制
        self.bi_list = self.bi_list[-self.max_bi_num:]
        if self.bi_list:
            sdt = self.bi_list[0].fx_a.elements[0].dt
            s_index = 0
            for i, bar in enumerate(self.bars_raw):
                if bar.dt >= sdt:
                    s_index = i
                    break
            self.bars_raw = self.bars_raw[s_index:]

    @property
    def fx_list(self) -> List[FX]:
        """分型列表，包括 bars_ubi 中的分型"""
        fxs = []
        for bi_ in self.bi_list:
            fxs.extend(bi_.fxs[1:])
        ubi = check_fxs(self.bars_ubi)
        for x in ubi:
            if not fxs or x.dt > fxs[-1].dt:
                fxs.append(x)
        return fxs

    def find_zs(self, bis: List[BI], min_bi_num=3, max_bi_num=9) -> List[ZS]:
        """从笔列表中查找中枚

        :param bis: 笔列表
        :param min_bi_num: 最小笔数量
        :param max_bi_num: 最大笔数量
        :return: 中枚列表
        """
        if len(bis) < min_bi_num:
            return []

        zs_list = []
        for i in range(len(bis) - min_bi_num + 1):
            for j in range(min_bi_num, min(max_bi_num + 1, len(bis) - i + 1)):
                zs = ZS(bis=bis[i:i+j])
                if zs.is_valid:
                    zs_list.append(zs)
        return zs_list

    @property
    def zs_list(self) -> List[ZS]:
        """中枚列表"""
        return self.find_zs(self.bi_list)

    def to_matplotlib(self, output_file=None, figsize=(20, 12)):
        """使用matplotlib绘制K线分析图"""
        # 准备数据
        dates = [x.dt for x in self.bars_raw]
        opens = [x.open for x in self.bars_raw]
        highs = [x.high for x in self.bars_raw]
        lows = [x.low for x in self.bars_raw]
        closes = [x.close for x in self.bars_raw]
        volumes = [x.vol for x in self.bars_raw]

        # 创建图表
        fig = plt.figure(figsize=figsize)

        # 设置K线图和成交量图的比例
        gs = fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = fig.add_subplot(gs[0])
        ax2 = fig.add_subplot(gs[1], sharex=ax1)

        # 绘制K线图
        width = 0.6  # K线宽度
        width2 = 0.05  # 影线宽度

        # 上涨和下跌的颜色
        up_color = 'red'
        down_color = 'green'

        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

        # 绘制成交量图
        for i in range(len(dates)):
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
            else:  # 下跌
                color = down_color
            ax2.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 标记分型
        for fx in self.fx_list:
            # 找到对应的索引
            idx = dates.index(fx.dt) if fx.dt in dates else -1
            if idx >= 0:
                if fx.mark == Mark.G:
                    ax1.scatter(idx, fx.fx, marker='^', color='blue', s=100, zorder=5)
                else:
                    ax1.scatter(idx, fx.fx, marker='v', color='purple', s=100, zorder=5)

        # 绘制笔
        if len(self.bi_list) > 0:
            bi_points = []
            for bi in self.bi_list:
                # 找到对应的索引
                idx_a = dates.index(bi.fx_a.dt) if bi.fx_a.dt in dates else -1
                if idx_a >= 0:
                    bi_points.append((idx_a, bi.fx_a.fx))

            # 添加最后一笔的结束点
            idx_b = dates.index(self.bi_list[-1].fx_b.dt) if self.bi_list[-1].fx_b.dt in dates else -1
            if idx_b >= 0:
                bi_points.append((idx_b, self.bi_list[-1].fx_b.fx))

            # 绘制笔的连线
            indices = [x[0] for x in bi_points]
            values = [x[1] for x in bi_points]
            ax1.plot(indices, values, 'y-', linewidth=2, zorder=4)

        # 绘制中枚
        zs_list = self.zs_list
        if zs_list:
            for zs in zs_list:
                # 找到中枚开始和结束的索引
                zs_start_dt = zs.bis[0].fx_a.dt
                zs_end_dt = zs.bis[-1].fx_b.dt
                zs_start_idx = dates.index(zs_start_dt) if zs_start_dt in dates else -1
                zs_end_idx = dates.index(zs_end_dt) if zs_end_dt in dates else -1

                if zs_start_idx >= 0 and zs_end_idx >= 0:
                    # 绘制中枚区域
                    ax1.axhspan(zs.zd, zs.zg, xmin=zs_start_idx/len(dates), xmax=zs_end_idx/len(dates),
                                alpha=0.2, color='gray', zorder=1)

                    # 绘制中枚中轴
                    ax1.hlines(zs.zz, zs_start_idx, zs_end_idx, colors='blue', linestyles='dashed',
                              linewidth=1, alpha=0.8, zorder=1)

        # 设置x轴刻度为日期
        ax1.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置图表标题和标签
        ax1.set_title(f"{self.symbol} - {self.freq.value} 分析结果", fontsize=16)
        ax1.set_ylabel('价格', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=12)

        # 设置y轴范围
        ax1.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1.grid(True, linestyle='--', alpha=0.3)

        # 添加图例
        from matplotlib.lines import Line2D
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
            Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
            Line2D([0], [0], color='yellow', lw=2, label='笔'),
            Line2D([0], [0], marker='^', color='blue', markersize=10, label='顶分型', linestyle='None'),
            Line2D([0], [0], marker='v', color='purple', markersize=10, label='底分型', linestyle='None'),
            Patch(facecolor='gray', alpha=0.2, label='中枚区域'),
            Line2D([0], [0], color='blue', linestyle='dashed', lw=1, label='中枚中轴'),
            Patch(facecolor='orange', alpha=0.3, label='M形态'),
            Patch(facecolor='cyan', alpha=0.3, label='N形态')
        ]
        ax1.legend(handles=legend_elements, loc='best')

        # 调整布局
        plt.tight_layout()

        # 标记识别到的M形态
        if hasattr(self, 'identified_m_patterns') and self.identified_m_patterns:
            for pattern in self.identified_m_patterns:
                try:
                    start_idx = dates.index(pattern['start_dt']) if pattern['start_dt'] in dates else -1
                    end_idx = dates.index(pattern['end_dt']) if pattern['end_dt'] in dates else -1
                    if start_idx != -1 and end_idx != -1:
                        # 在M形态的顶部画一个矩形框或者特殊标记
                        # 这里简单地在K线图上方面板的对应区域画一个半透明的矩形
                        rect_height = (max(highs[start_idx:end_idx+1]) - min(lows[start_idx:end_idx+1])) * 0.1
                        rect_bottom = max(highs[start_idx:end_idx+1]) + rect_height * 0.2
                        m_pattern_rect = Rectangle((start_idx - 0.5, rect_bottom),
                                                   end_idx - start_idx + 1,
                                                   rect_height,
                                                   color='orange', alpha=0.3, zorder=6)
                        ax1.add_patch(m_pattern_rect)
                        ax1.text((start_idx + end_idx) / 2, rect_bottom + rect_height / 2, 'M',
                                 horizontalalignment='center', verticalalignment='center',
                                 fontsize=12, color='black', fontweight='bold', zorder=7)
                except ValueError:
                    # 日期不在列表中，跳过标记
                    pass

        # 标记识别到的N字形态
        if hasattr(self, 'identified_n_patterns') and self.identified_n_patterns:
            for pattern in self.identified_n_patterns:
                try:
                    start_idx = dates.index(pattern['start_dt']) if pattern['start_dt'] in dates else -1
                    end_idx = dates.index(pattern['end_dt']) if pattern['end_dt'] in dates else -1
                    if start_idx != -1 and end_idx != -1:
                        # 在N形态的区域画一个矩形框
                        rect_height = (max(highs[start_idx:end_idx+1]) - min(lows[start_idx:end_idx+1])) * 0.1
                        rect_bottom = max(highs[start_idx:end_idx+1]) + rect_height * 0.2
                        n_pattern_rect = Rectangle((start_idx - 0.5, rect_bottom),
                                                  end_idx - start_idx + 1,
                                                  rect_height,
                                                  color='cyan', alpha=0.3, zorder=6)
                        ax1.add_patch(n_pattern_rect)
                        ax1.text((start_idx + end_idx) / 2, rect_bottom + rect_height / 2, 'N',
                                horizontalalignment='center', verticalalignment='center',
                                fontsize=12, color='black', fontweight='bold', zorder=7)

                        # 如果有关键点信息，标记关键点
                        if 'key_points' in pattern:
                            for point in pattern['key_points']:
                                if point['idx'] >= 0 and point['idx'] < len(dates):
                                    ax1.scatter(point['idx'], point['price'], marker='o', color='red', s=50, zorder=8)
                                    ax1.annotate(point['type'], (point['idx'], point['price']),
                                                xytext=(0, 10), textcoords='offset points',
                                                ha='center', va='bottom', fontsize=8,
                                                bbox=dict(facecolor='white', alpha=0.7, edgecolor='red', pad=1))
                except ValueError:
                    # 日期不在列表中，跳过标记
                    pass

        # 绘制双底形态
        if hasattr(self, 'identified_double_bottoms') and self.identified_double_bottoms:
            colors = ['cyan', 'magenta', 'yellow', 'blue', 'green', 'red'] # Define a list of colors
            for i, pattern in enumerate(self.identified_double_bottoms):
                try:
                    color = colors[pattern['id'] % len(colors)] # Select color based on ID

                    # 绘制第一个底
                    fb_idx = dates.index(pattern['first_bottom']['date']) if pattern['first_bottom']['date'] in dates else -1
                    if fb_idx != -1:
                        pattern_label_id = pattern['id'] + 1
                        ax1.scatter(fb_idx, pattern['first_bottom']['price'], marker='o', color=color, s=150, zorder=8, label=f'双底 {pattern_label_id}')

                    # 绘制第二个底
                    sb_idx = dates.index(pattern['second_bottom']['date']) if pattern['second_bottom']['date'] in dates else -1
                    if sb_idx != -1:
                        ax1.scatter(sb_idx, pattern['second_bottom']['price'], marker='o', color=color, s=150, zorder=8)

                    # 绘制颈线
                    neckline_start_idx = min(fb_idx, sb_idx) if fb_idx != -1 and sb_idx != -1 else (fb_idx if fb_idx != -1 else sb_idx)
                    neckline_end_idx = pattern['breakout']['index'] if 'breakout' in pattern and 'index' in pattern['breakout'] else -1

                    if neckline_start_idx != -1 and neckline_end_idx != -1:
                         ax1.hlines(pattern['neckline']['price'], neckline_start_idx, neckline_end_idx, colors=color, linestyles='dashed', linewidth=1.5, alpha=0.8, zorder=7)

                except ValueError:
                    # 日期不在列表中，跳过标记
                    pass
                except Exception as e:
                    print(f"绘制双底形态时出错: {e}")
                    pass

        # 保存图表
        if output_file:
            plt.savefig(output_file, dpi=100)
            print(f"分析结果已保存至: {output_file}")

        return fig


def read_kline_from_file(file_path, symbol=None, freq='日线'):
    """从CSV或TXT文件读取K线数据

    :param file_path: CSV或TXT文件路径
    :param symbol: 标的代码，如果为None，则使用文件名作为标的代码
    :param freq: K线周期，默认为日线
    :return: RawBar对象列表
    """
    # 如果未指定symbol，则从文件名中提取
    if symbol is None:
        symbol = os.path.basename(file_path).split('.')[0]

    # 读取文件
    if file_path.endswith('.csv') or file_path.endswith('.txt'):
        df = pd.read_csv(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_path}")

    # 检查列名并进行必要的转换
    # 如果是300046moudle.txt的格式，列名应该是: date,open,high,low,close,volume,amount
    if 'date' in df.columns and 'open' in df.columns and 'volume' in df.columns:
        # 转换日期列
        df['date'] = pd.to_datetime(df['date'])

        # 确保所有列都是数值类型
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 如果没有amount列，则使用close * volume作为amount
        if 'amount' not in df.columns:
            df['amount'] = df['close'] * df['volume']
        else:
            df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
    else:
        raise ValueError(f"文件格式不正确，必须包含 date, open, high, low, close, volume 列")

    # 转换为RawBar对象列表
    freq_obj = Freq(freq)
    bars = []
    for i, row in df.iterrows():
        try:
            bar = RawBar(
                symbol=symbol,
                id=i,
                freq=freq_obj,
                dt=row['date'],
                open=float(row['open']),
                close=float(row['close']),
                high=float(row['high']),
                low=float(row['low']),
                vol=float(row['volume']),
                amount=float(row['amount']) if 'amount' in df.columns else float(row['volume']) * float(row['close'])
            )
            bars.append(bar)
        except Exception as e:
            print(f"跳过无效行: {row}, 错误: {e}")

    print(f"共读取了 {len(bars)} 条有效K线数据")
    return bars


def identify_third_bs(c):
    """识别三买点和三卖点

    参考czsc.signals.cxt模块中的定义，三买点和三卖点的基本特征是：
    1. 三买：1）123构成中枚，4离开，5回落不回中枚；2）均线新高
    2. 三卖：1）123构成中枚，4离开，5回升不回中枚；2）均线新低

    :param c: CZSC对象
    :return: 三买点和三卖点列表
    """
    third_buy_points = []
    third_sell_points = []

    # 至少需要5笔才能形成三买或三卖
    if len(c.bi_list) < 5:
        return third_buy_points, third_sell_points

    # 遍历笔序列，查找可能的三买和三卖点
    for i in range(2, len(c.bi_list) - 2):
        # 获取5笔序列
        b1 = c.bi_list[i-2]
        b2 = c.bi_list[i-1]
        b3 = c.bi_list[i]
        b4 = c.bi_list[i+1]
        b5 = c.bi_list[i+2]

        # 检查是否构成中枚
        if b1.direction == b3.direction and b2.direction == b4.direction and b1.direction != b2.direction:
            # 计算中枚区域
            zg = min(b1.high, b3.high) if b1.direction == Direction.Down else min(b2.high, b4.high)
            zd = max(b1.low, b3.low) if b1.direction == Direction.Down else max(b2.low, b4.low)

            # 检查是否有效中枚
            if zg > zd:
                # 检查三买点
                if b5.direction == Direction.Down and b5.low > zg:
                    third_buy_points.append((b5.fx_b.dt, b5.low))

                # 检查三卖点
                if b5.direction == Direction.Up and b5.high < zd:
                    third_sell_points.append((b5.fx_b.dt, b5.high))

    return third_buy_points, third_sell_points


def identify_double_bottom(bars, min_distance=20, max_distance=60, price_tolerance=0.03, volume_increase_threshold=1.2):
    """识别双底形态

    双底形态的核心特征：
    1. 结构规则：
       - 第一个底（First Bottom）：某一段时间内的最低点。
       - 反弹高点（Neckline）：第一个底后的局部高点。
       - 第二个底（Second Bottom）：回落后与第一个底价格相近（误差±3%）。
       - 突破信号（Breakout）：价格站稳颈线，确认趋势反转。
    2. 时间规则：
       - 两个底之间的间隔通常为 20~60个交易日或周。
       - 突破颈线时需伴随成交量放大。

    :param bars: K线数据列表
    :param min_distance: 两个底之间的最小间隔，默认20个交易日
    :param max_distance: 两个底之间的最大间隔，默认60个交易日
    :param price_tolerance: 价格差异容差，默认3%
    :param volume_increase_threshold: 成交量增加阈值，默认1.2倍
    :return: 双底形态列表
    """
    try:
        print(f"identify_double_bottom: 开始处理 {len(bars)} 条数据")

        if len(bars) < max_distance + 10:  # 确保有足够的数据
            print("identify_double_bottom: 数据不足，无法识别双底形态")
            return []

        double_bottoms = []

        # 定义局部最低点和最高点的窗口大小
        # 使用更灵活的窗口大小，或者考虑更高级的峰谷识别算法
        # 当前仍使用固定窗口，但可以根据需要调整
        bottom_window_size = 5
        neckline_window_size = 5

        # 遍历所有可能的第一个底
        # 确保有足够的空间容纳第一个底、颈线、第二个底和突破
        min_required_bars = bottom_window_size + 1 + neckline_window_size + 1 + min_distance + 1 + 1 # 1 for breakout
        if len(bars) < min_required_bars:
             print("identify_double_bottom: 数据不足，无法识别双底形态")
             return []

        for i in range(bottom_window_size, len(bars) - max_distance - bottom_window_size - neckline_window_size - 2):
            try:
                # 检查当前点是否是局部最低点
                is_local_min = True
                # 确保索引在有效范围内
                start_check = max(0, i - bottom_window_size)
                end_check = min(len(bars), i + bottom_window_size + 1)
                for j in range(start_check, end_check):
                    if j != i:
                        # 检查bars[j]是否有low属性
                        if hasattr(bars[j], 'low') and hasattr(bars[i], 'low') and bars[j].low < bars[i].low:
                            is_local_min = False
                            break

                if not is_local_min:
                    continue

                # 检查bars[i]是否有必要的属性
                if not hasattr(bars[i], 'low') or not hasattr(bars[i], 'dt'):
                    continue

                first_bottom_price = bars[i].low
                first_bottom_date = bars[i].dt

                # 找到第一个底后的反弹高点（颈线）
                # 在第一个底之后，第二个底之前寻找局部最高点作为颈线
                neckline_idx = -1
                neckline_price = first_bottom_price

                # 寻找颈线的范围：从第一个底之后到可能的第二个底的最小距离之前
                neckline_search_end = min(i + min_distance, len(bars))
                for j in range(i + 1, neckline_search_end):
                     # 检查当前点是否是局部最高点
                    is_local_max = True
                    # 确保索引在有效范围内
                    start_check_neck = max(i + 1, j - neckline_window_size)
                    end_check_neck = min(neckline_search_end, j + neckline_window_size + 1)
                    for k in range(start_check_neck, end_check_neck):
                        if k != j:
                            if hasattr(bars[k], 'high') and hasattr(bars[j], 'high') and bars[k].high > bars[j].high:
                                is_local_max = False
                                break
                    if is_local_max:
                        # 找到一个局部最高点，更新颈线信息
                        if hasattr(bars[j], 'high') and bars[j].high > neckline_price:
                            neckline_price = bars[j].high
                            neckline_idx = j

                if neckline_idx == -1 or neckline_idx <= i:  # 没有找到有效的颈线
                    continue

                # 检查bars[neckline_idx]是否有dt属性
                if not hasattr(bars[neckline_idx], 'dt'):
                    continue

                neckline_date = bars[neckline_idx].dt

                # 在有效范围内寻找第二个底
                # 寻找第二个底的范围：从颈线之后到最大距离范围内
                second_bottom_search_start = neckline_idx + 1
                second_bottom_search_end = min(i + max_distance + 1, len(bars))

                for j in range(second_bottom_search_start, second_bottom_search_end):
                    try:
                        # 检查当前点是否是局部最低点
                        is_local_min = True
                        # 确保索引在有效范围内
                        start_check = max(second_bottom_search_start, j - bottom_window_size)
                        end_check = min(second_bottom_search_end, j + bottom_window_size + 1)
                        for k in range(start_check, end_check):
                            if k != j:
                                # 检查bars[k]是否有low属性
                                if hasattr(bars[k], 'low') and hasattr(bars[j], 'low') and bars[k].low < bars[j].low:
                                    is_local_min = False
                                    break

                        if not is_local_min: # 或者 bars[j].low > first_bottom_price * (1 + price_tolerance): # 简单过滤高于第一个底太多的点
                            continue

                        # 检查bars[j]是否有必要的属性
                        if not hasattr(bars[j], 'low') or not hasattr(bars[j], 'dt'):
                            continue

                        second_bottom_price = bars[j].low
                        second_bottom_date = bars[j].dt

                        # 检查两个底的价格是否相近（在容差范围内）
                        # 确保分母不为零
                        if first_bottom_price == 0:
                            price_diff_ratio = float('inf') # Avoid division by zero
                        else:
                            price_diff_ratio = abs(second_bottom_price - first_bottom_price) / first_bottom_price

                        if price_diff_ratio > price_tolerance:
                            continue

                        # 检查第二个底后是否突破颈线
                        breakout_idx = -1
                        for k in range(j + 1, min(j + 20, len(bars))):
                            # 检查bars[k]是否有close属性
                            if hasattr(bars[k], 'close') and bars[k].close > neckline_price:
                                breakout_idx = k
                                break

                        if breakout_idx == -1:  # 没有突破颈线
                            continue

                        # 检查bars[breakout_idx]是否有必要的属性
                        if not hasattr(bars[breakout_idx], 'dt') or not hasattr(bars[breakout_idx], 'close'):
                            continue

                        breakout_date = bars[breakout_idx].dt

                        # 检查突破时的成交量是否放大
                        # 首先检查是否有vol属性
                        volume_check_passed = True  # 默认通过成交量检查

                        try:
                            if all(hasattr(bars[k], 'vol') for k in range(breakout_idx - 5, breakout_idx + 1)):
                                # 计算突破前5个交易日的平均成交量
                                avg_volume_before = sum(bars[k].vol for k in range(breakout_idx - 5, breakout_idx)) / 5
                                # 突破时的成交量
                                breakout_volume = bars[breakout_idx].vol

                                if breakout_volume < avg_volume_before * volume_increase_threshold:
                                    volume_check_passed = False
                        except Exception as e:
                            print(f"检查成交量时出错: {str(e)}")
                            # 如果检查成交量时出错，则跳过成交量检查
                            volume_check_passed = True

                        if not volume_check_passed:
                            continue

                        # 收集双底形态信息
                        double_bottom = {
                            'first_bottom': {
                                'date': first_bottom_date,
                                'price': first_bottom_price,
                                'index': i
                            },
                            'neckline': {
                                'date': neckline_date,
                                'price': neckline_price,
                                'index': neckline_idx
                            },
                            'second_bottom': {
                                'date': second_bottom_date,
                                'price': second_bottom_price,
                                'index': j
                            },
                            'breakout': {
                                'date': breakout_date,
                                'price': bars[breakout_idx].close,
                                'index': breakout_idx,
                                'volume': getattr(bars[breakout_idx], 'vol', 0),
                                'avg_volume_before': getattr(bars[breakout_idx-1], 'vol', 0) / 1.2  # 默认值，确保比例正确
                            },
                            'id': len(double_bottoms) # Add a unique ID for each pattern
                        }

                        double_bottoms.append(double_bottom)
                    except Exception as e:
                        print(f"处理第二个底时出错: {str(e)}")
                        continue
            except Exception as e:
                print(f"处理第一个底时出错: {str(e)}")
                continue

        print(f"identify_double_bottom: 找到 {len(double_bottoms)} 个双底形态")
        return double_bottoms

    except Exception as e:
        print(f"识别双底形态时出错: {str(e)}")
        return []  # 出错时返回空列表


def resample_to_weekly(bars):
    """将日线K线数据重采样为周线数据

    :param bars: 日线RawBar对象列表
    :return: 周线RawBar对象列表
    """
    if not bars:
        return []

    # 创建DataFrame
    df = pd.DataFrame({
        'dt': [bar.dt for bar in bars],
        'open': [bar.open for bar in bars],
        'high': [bar.high for bar in bars],
        'low': [bar.low for bar in bars],
        'close': [bar.close for bar in bars],
        'vol': [bar.vol for bar in bars],
        'amount': [bar.amount for bar in bars]
    })

    # 设置日期索引
    df.set_index('dt', inplace=True)

    # 添加年和周数列
    df['week_num'] = df.index.isocalendar().week
    df['year'] = df.index.isocalendar().year

    # 按年和周数分组
    weekly_data = []
    for (year, week), week_group in df.groupby(['year', 'week_num']):
        if not week_group.empty:
            # 只处理工作日数据（周一到周五）
            week_group = week_group[week_group.index.dayofweek < 5]
            if len(week_group) > 0:
                first_day = week_group.iloc[0]
                last_day = week_group.iloc[-1]

                # 创建周线RawBar对象
                weekly_bar = RawBar(
                    symbol=bars[0].symbol,
                    id=len(weekly_data),
                    freq=Freq('周线'),
                    dt=week_group.index[0],  # 使用该周第一个交易日作为日期
                    open=first_day['open'],
                    high=week_group['high'].max(),
                    low=week_group['low'].min(),
                    close=last_day['close'],
                    vol=week_group['vol'].sum(),
                    amount=week_group['amount'].sum()
                )
                weekly_data.append(weekly_bar)

    # 按日期排序
    weekly_data.sort(key=lambda x: x.dt)

    print(f"将 {len(bars)} 条日线K线数据重采样为 {len(weekly_data)} 条周线K线数据")
    return weekly_data


def analyze_and_visualize(bars, output_file=None, figsize=(20, 12)):
    """分析K线并可视化分型和笔

    :param bars: RawBar对象列表
    :param output_file: 输出图片文件路径，如果为None，则使用标的代码和当前时间生成文件名
    :param figsize: 图表尺寸
    :return: 输出文件路径
    """
    # 创建CZSC对象进行分析
    c = CZSC(bars)

    # 如果未指定输出文件，则生成一个文件名
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = f"{c.symbol}_{c.freq.value}_{timestamp}.png"

    # 生成图表
    c.to_matplotlib(output_file, figsize=figsize)

    print(f"共识别出 {len(c.fx_list)} 个分型")
    print(f"共识别出 {len(c.bi_list)} 笔")

    return output_file


# 创建Matplotlib画布类
class MplCanvas(FigureCanvas):
    def __init__(self, width=10, height=8, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super(MplCanvas, self).__init__(self.fig)
        self.setMinimumSize(800, 600)


# 放大图表窗口类
class ZoomedChartWindow(QMainWindow):
    def __init__(self, chart_type, data, symbol, freq, parent=None):
        super(ZoomedChartWindow, self).__init__(parent)

        # 保存数据以便在其他方法中使用
        self.data = data
        self.chart_type = chart_type
        self.symbol = symbol
        self.freq = freq

        # 设置窗口标题和大小
        if chart_type == "raw":
            self.setWindowTitle(f"{symbol} - {freq} 原始K线放大图")
        else:
            self.setWindowTitle(f"{symbol} - {freq} 分析结果放大图")

        self.setMinimumSize(1200, 800)

        # 创建中心部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # 创建Matplotlib画布
        self.canvas = MplCanvas(width=12, height=10, dpi=100)
        self.toolbar = NavigationToolbar(self.canvas, self)

        # 添加缩放按钮到工具栏
        self.zoom_all_action = QAction("显示全部K线", self)
        self.zoom_all_action.triggered.connect(self.show_all_klines)
        self.toolbar.addAction(self.zoom_all_action)

        # 添加查找双底按钮到工具栏
        self.find_double_bottom_action = QAction("查找双底形态", self)
        self.find_double_bottom_action.triggered.connect(self.find_double_bottom)
        self.toolbar.addAction(self.find_double_bottom_action)

        # 添加查找N字形态按钮到工具栏
        self.find_n_pattern_action = QAction("查找N字形态", self)
        self.find_n_pattern_action.triggered.connect(self.find_n_pattern)
        self.toolbar.addAction(self.find_n_pattern_action)

        # 添加价格分布密度按钮到工具栏
        self.price_density_action = QAction("价格分布密度", self)
        self.price_density_action.triggered.connect(self.show_price_density)
        self.toolbar.addAction(self.price_density_action)

        # 添加查找角度关系的三点按钮到工具栏
        self.find_angle_points_action = QAction("查找角度关系三点", self)
        self.find_angle_points_action.triggered.connect(self.find_angle_points)
        self.toolbar.addAction(self.find_angle_points_action)

        # 添加计算高低价格零度倍数按钮到工具栏
        self.find_zero_degree_prices_action = QAction("计算零度价格", self)
        self.find_zero_degree_prices_action.triggered.connect(self.find_zero_degree_prices)
        self.toolbar.addAction(self.find_zero_degree_prices_action)

        # 添加计算阶段高低点的峰值按钮到工具栏
        self.find_peak_points_action = QAction("计算阶段高低点", self)
        self.find_peak_points_action.triggered.connect(self.find_peak_points)
        self.toolbar.addAction(self.find_peak_points_action)


        # 添加到布局
        self.layout.addWidget(self.toolbar)
        self.layout.addWidget(self.canvas)

        # 绘制图表
        self.plot_chart(chart_type, data)

        # 自动显示全部K线
        self.show_all_klines()

    def plot_chart(self, chart_type, data):
        """绘制放大图表"""
        # 清除当前图表
        self.canvas.fig.clear()

        # 解析数据
        dates = data['dates']
        opens = data['opens']
        highs = data['highs']
        lows = data['lows']
        closes = data['closes']
        volumes = data['volumes']

        # 设置K线图和成交量图的比例
        gs = self.canvas.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = self.canvas.fig.add_subplot(gs[0])
        ax2 = self.canvas.fig.add_subplot(gs[1], sharex=ax1)

        # 绘制K线图
        width = 0.6  # K线宽度
        width2 = 0.3  # 影线宽度 - 增加影线宽度使其更清晰可见

        # 上涨和下跌的颜色
        up_color = 'red'
        down_color = 'green'

        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

            # 绘制成交量图
            ax2.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 如果是分析后的图表，添加分型、笔和中枚
        if chart_type == "analyzed" and 'czsc' in data:
            czsc = data['czsc']

            # 标记分型
            for fx in czsc.fx_list:
                # 找到对应的索引
                idx = dates.index(fx.dt) if fx.dt in dates else -1
                if idx >= 0:
                    if fx.mark == Mark.G:
                        ax1.scatter(idx, fx.fx, marker='^', color='blue', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                        # 标注价格
                        ax1.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, 5),
                                     textcoords='offset points', ha='center', va='bottom', fontsize=8)
                    else:
                        ax1.scatter(idx, fx.fx, marker='v', color='purple', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                        # 标注价格
                        ax1.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, -5),
                                     textcoords='offset points', ha='center', va='top', fontsize=8)

            # 绘制笔
            if len(czsc.bi_list) > 0:
                bi_points = []
                for bi in czsc.bi_list:
                    # 找到对应的索引
                    idx_a = dates.index(bi.fx_a.dt) if bi.fx_a.dt in dates else -1
                    if idx_a >= 0:
                        bi_points.append((idx_a, bi.fx_a.fx))

                # 添加最后一笔的结束点
                idx_b = dates.index(czsc.bi_list[-1].fx_b.dt) if czsc.bi_list[-1].fx_b.dt in dates else -1
                if idx_b >= 0:
                    bi_points.append((idx_b, czsc.bi_list[-1].fx_b.fx))

                # 绘制笔的连线
                indices = [x[0] for x in bi_points]
                values = [x[1] for x in bi_points]
                ax1.plot(indices, values, 'y-', linewidth=0.5, zorder=4)  # 进一步减小笔连线的粗细

            # 绘制中枚
            zs_list = czsc.zs_list
            if zs_list:
                # 中枚颜色列表，使用淡色系
                zs_colors = ['#FFA07A', '#FFD700', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA', '#FFC0CB']

                for i, zs in enumerate(zs_list):
                    # 选择颜色，循环使用颜色列表
                    color_idx = i % len(zs_colors)
                    zs_color = zs_colors[color_idx]

                    # 找到中枚开始和结束的索引
                    zs_start_dt = zs.bis[0].fx_a.dt
                    zs_end_dt = zs.bis[-1].fx_b.dt
                    zs_start_idx = dates.index(zs_start_dt) if zs_start_dt in dates else -1
                    zs_end_idx = dates.index(zs_end_dt) if zs_end_dt in dates else -1

                    if zs_start_idx >= 0 and zs_end_idx >= 0:
                        # 绘制中枚区域，增加透明度，降低zorder使其不遮挡K线
                        ax1.axhspan(zs.zd, zs.zg, xmin=zs_start_idx/len(dates), xmax=zs_end_idx/len(dates),
                                    alpha=0.08, color=zs_color, zorder=0)

                        # 绘制中枚中轴
                        ax1.hlines(zs.zz, zs_start_idx, zs_end_idx, colors=zs_color, linestyles='dashed',
                                  linewidth=1, alpha=0.8, zorder=1)

                        # 标注中枚信息
                        ax1.text((zs_start_idx + zs_end_idx) / 2, zs.zz, f"ZS{i+1}",
                                 ha='center', va='center', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.6, edgecolor=zs_color, pad=1))

            # 标注三买点和三卖点
            third_buy_points, third_sell_points = identify_third_bs(czsc)

            # 标注三买点
            for dt, price in third_buy_points:
                idx = dates.index(dt) if dt in dates else -1
                if idx >= 0:
                    # 使用绿色上三角标记三买点
                    ax1.scatter(idx, price, marker='^', color='lime', s=80, zorder=6, edgecolors='black')
                    # 标注三买文字
                    ax1.annotate('三买', (idx, price), xytext=(0, -15),
                                 textcoords='offset points', ha='center', va='top', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='lime', pad=1))

            # 标注三卖点
            for dt, price in third_sell_points:
                idx = dates.index(dt) if dt in dates else -1
                if idx >= 0:
                    # 使用红色下三角标记三卖点
                    ax1.scatter(idx, price, marker='v', color='red', s=80, zorder=6, edgecolors='black')
                    # 标注三卖文字
                    ax1.annotate('三卖', (idx, price), xytext=(0, 15),
                                 textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))

        # 设置x轴刻度为日期
        ax1.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置图表标题和标签
        if chart_type == "raw":
            ax1.set_title(f"{data['symbol']} - {data['freq']} 原始K线", fontsize=16)
        else:
            ax1.set_title(f"{data['symbol']} - {data['freq']} 分析结果", fontsize=16)

        ax1.set_ylabel('价格', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=12)

        # 设置y轴范围
        ax1.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1.grid(True, linestyle='--', alpha=0.3)

        # 添加图例
        from matplotlib.lines import Line2D
        from matplotlib.patches import Patch

        if chart_type == "raw":
            # 在原始K线图上标注高低点
            if 'czsc' in data:
                czsc_obj = data['czsc']

                # 准备高点和低点数据
                high_points = []
                low_points = []

                # 获取笔的顶底分型
                # 如果有parent对象并且有num_points_spinbox，则使用其值
                num_points = 30  # 默认值
                if hasattr(self.parent(), 'num_points_spinbox'):
                    num_points = self.parent().num_points_spinbox.value()

                bi_list = czsc_obj.bi_list[-num_points:] if len(czsc_obj.bi_list) > num_points else czsc_obj.bi_list

                for bi in bi_list:
                    # 添加笔的起始分型
                    if bi.fx_a.mark == Mark.G:  # 顶分型
                        high_points.append({
                            'price': bi.fx_a.fx,
                            'date': bi.fx_a.dt
                        })
                    else:  # 底分型
                        low_points.append({
                            'price': bi.fx_a.fx,
                            'date': bi.fx_a.dt
                        })

                    # 添加笔的结束分型
                    if bi.fx_b.mark == Mark.G:  # 顶分型
                        high_points.append({
                            'price': bi.fx_b.fx,
                            'date': bi.fx_b.dt
                        })
                    else:  # 底分型
                        low_points.append({
                            'price': bi.fx_b.fx,
                            'date': bi.fx_b.dt
                        })

                # 在原始K线图上标注高点
                for point in high_points:
                    # 找到对应的索引
                    idx = dates.index(point['date']) if point['date'] in dates else -1
                    if idx >= 0:
                        # 使用橙色正方形标记高点 - 调整透明度和位置
                        # 将标记放在高点上方，不遗挡K线
                        ax1.scatter(idx, point['price'] * 1.002, marker='s', color='orange', s=40, zorder=5, alpha=0.5)
                        # 标注价格 - 调整位置和透明度
                        ax1.annotate(f"{point['price']:.2f}", (idx, point['price'] * 1.004), xytext=(0, 2),
                                     textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                     bbox=dict(facecolor='white', alpha=0.6, edgecolor='orange', pad=1))

                # 在原始K线图上标注低点
                for point in low_points:
                    # 找到对应的索引
                    idx = dates.index(point['date']) if point['date'] in dates else -1
                    if idx >= 0:
                        # 使用蓝色正方形标记低点 - 调整透明度和位置
                        # 将标记放在低点下方，不遗挡K线
                        ax1.scatter(idx, point['price'] * 0.998, marker='s', color='blue', s=40, zorder=5, alpha=0.5)
                        # 标注价格 - 调整位置和透明度
                        ax1.annotate(f"{point['price']:.2f}", (idx, point['price'] * 0.996), xytext=(0, -2),
                                     textcoords='offset points', ha='center', va='top', fontsize=8,
                                     bbox=dict(facecolor='white', alpha=0.6, edgecolor='blue', pad=1))

            # 添加图例
            legend_elements = [
                Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
                Patch(facecolor=down_color, edgecolor=down_color, label='下跌')
            ]

            # 如果有czsc对象，添加高低点图例
            if 'czsc' in data:
                legend_elements.extend([
                    Line2D([0], [0], marker='s', color='w', markerfacecolor='orange', markersize=8, label='高点'),
                    Line2D([0], [0], marker='s', color='w', markerfacecolor='blue', markersize=8, label='低点')
                ])
        else:
            legend_elements = [
                Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
                Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
                Line2D([0], [0], color='yellow', lw=0.5, label='笔'),  # 进一步减小笔连线的粗细
                Line2D([0], [0], marker='^', color='blue', markersize=3, label='顶分型', linestyle='None'),  # 进一步缩小箭头
                Line2D([0], [0], marker='v', color='purple', markersize=3, label='底分型', linestyle='None'),  # 进一步缩小箭头
                Line2D([0], [0], marker='^', color='lime', markersize=8, label='三买点', linestyle='None', markeredgecolor='black'),  # 添加三买点
                Line2D([0], [0], marker='v', color='red', markersize=8, label='三卖点', linestyle='None', markeredgecolor='black'),  # 添加三卖点
            ]

            # 添加中枚图例
            if 'czsc' in data and data['czsc'].zs_list:
                # 中枚颜色列表，使用淡色系
                zs_colors = ['#FFA07A', '#FFD700', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA', '#FFC0CB']
                zs_list = data['czsc'].zs_list

                # 为每个中枚添加一个图例项
                for i in range(min(len(zs_list), 5)):  # 最多显示5个中枚图例
                    color_idx = i % len(zs_colors)
                    zs_color = zs_colors[color_idx]
                    legend_elements.append(Patch(facecolor=zs_color, alpha=0.08, label=f'ZS{i+1}'))

        ax1.legend(handles=legend_elements, loc='best')

        # 如果有双底形态信息，则显示双底形态
        if 'has_double_bottoms' in data and data['has_double_bottoms']:
            try:
                # 直接使用双底形态数据绘制颈线水平线
                if 'double_bottoms' in data and data['double_bottoms']:
                    for db in data['double_bottoms']:
                        # 绘制颈线水平线
                        first_idx = db['first_bottom']['index']
                        breakout_idx = db['breakout']['index']
                        neckline_price = db['neckline']['price']

                        # 计算相对位置
                        xmin = first_idx / len(dates)
                        xmax = breakout_idx / len(dates)

                        # 确保范围在[0,1]内
                        xmin = max(0, min(1, xmin))
                        xmax = max(0, min(1, xmax))

                        # 绘制颈线水平线
                        ax1.axhline(y=neckline_price, xmin=xmin, xmax=xmax,
                                   color='purple', linestyle='--', alpha=0.5, label='double_bottom')

                # 获取原始K线图的轴
                parent_window = self.parent()
                if parent_window:
                    # 获取原始K线图的轴
                    parent_ax = parent_window.raw_canvas.fig.axes[0]

                    # 复制双底形态标记
                    for artist in parent_ax.get_children():
                        if hasattr(artist, 'get_label') and artist.get_label() == 'double_bottom':
                            # 如果是散点图，复制散点
                            if hasattr(artist, 'get_offsets') and callable(artist.get_offsets):
                                try:
                                    offsets = artist.get_offsets()
                                    # 使用默认标记和大小，而不是尝试获取原始标记
                                    # 根据散点的颜色决定使用什么标记
                                    marker = 'o'  # 默认使用圆形标记
                                    size = 100    # 默认大小

                                    # 使用简单的字符串颜色名称，而不是尝试获取原始颜色
                                    # 根据散点的类型决定颜色和标记
                                    # 检查散点的位置来确定它的类型

                                    # 默认值
                                    color = 'blue'  # 默认颜色

                                    # 根据散点的位置确定它的类型
                                    # 如果是第一个底或第二个底，使用蓝色圆形
                                    # 如果是颈线，使用紫色三角形
                                    # 如果是突破点，使用红色星形

                                    # 检查散点的位置
                                    y_value = offsets[0, 1]  # 获取第一个点的y值

                                    # 尝试根据散点的位置确定它的类型
                                    for db in data.get('double_bottoms', []):
                                        if abs(y_value - db.get('neckline', {}).get('price', 0)) < 0.001:
                                            # 这是颈线
                                            color = 'purple'
                                            marker = '^'
                                            break
                                        elif abs(y_value - db.get('breakout', {}).get('price', 0)) < 0.001:
                                            # 这是突破点
                                            color = 'red'
                                            marker = '*'
                                            break
                                        elif abs(y_value - db.get('first_bottom', {}).get('price', 0)) < 0.001 or \
                                             abs(y_value - db.get('second_bottom', {}).get('price', 0)) < 0.001:
                                            # 这是底点
                                            color = 'blue'
                                            marker = 'o'
                                            break

                                    # 根据散点的位置决定大小
                                    if marker == '*':  # 突破点用大一点的标记
                                        size = 150
                                    elif marker == '^':  # 颈线用中等大小的标记
                                        size = 100
                                    else:  # 其他用正常大小的标记
                                        size = 100

                                    ax1.scatter(offsets[:, 0], offsets[:, 1],
                                               marker=marker,
                                               color=color,
                                               s=size,
                                               zorder=10, alpha=0.7, label='double_bottom')
                                except Exception as e:
                                    print(f"复制散点时出错: {str(e)}")
                            # 如果是线条，复制线条
                            elif hasattr(artist, 'get_xdata') and callable(artist.get_xdata):
                                try:
                                    xdata = artist.get_xdata()
                                    ydata = artist.get_ydata()
                                    ax1.plot(xdata, ydata,
                                            linestyle=artist.get_linestyle(),
                                            color=artist.get_color(),
                                            linewidth=artist.get_linewidth(),
                                            alpha=0.7, label='double_bottom')
                                except Exception as e:
                                    print(f"复制线条时出错: {str(e)}")
                            # 如果是水平线，复制水平线
                            elif hasattr(artist, 'get_ydata') and callable(artist.get_ydata()):
                                try:
                                    # 尝试获取水平线的y值
                                    if len(artist.get_ydata()) > 0:
                                        y = artist.get_ydata()[0]

                                        # 如果有双底形态数据，则使用数据中的颈线价格
                                        for db in data.get('double_bottoms', []):
                                            if abs(y - db.get('neckline', {}).get('price', 0)) < 0.001:
                                                # 这是颈线的水平线
                                                # 使用双底形态数据中的索引计算xmin和xmax
                                                first_idx = db.get('first_bottom', {}).get('index', 0)
                                                breakout_idx = db.get('breakout', {}).get('index', len(dates)-1)

                                                # 计算相对位置
                                                xmin = first_idx / len(dates)
                                                xmax = breakout_idx / len(dates)

                                                # 确保范围在[0,1]内
                                                xmin = max(0, min(1, xmin))
                                                xmax = max(0, min(1, xmax))

                                                # 绘制颈线的水平线
                                                ax1.axhline(y=y, xmin=xmin, xmax=xmax,
                                                           color='purple',  # 颈线用紫色
                                                           linestyle='--',
                                                           alpha=0.5, label='double_bottom')
                                                break
                                        else:
                                            # 如果不是颈线，则使用原始的xmin和xmax
                                            if hasattr(artist, 'get_xdata') and callable(artist.get_xdata()) and len(artist.get_xdata()) >= 2:
                                                xmin = artist.get_xdata()[0]
                                                xmax = artist.get_xdata()[-1]
                                                ax1.axhline(y=y, xmin=xmin/len(dates), xmax=xmax/len(dates),
                                                           color=artist.get_color(),
                                                           linestyle=artist.get_linestyle(),
                                                           alpha=0.5, label='double_bottom')
                                except Exception as e:
                                    print(f"复制水平线时出错: {str(e)}")

                    # 复制文本注释
                    for artist in parent_ax.texts:
                        try:
                            if hasattr(artist, '_x') and hasattr(artist, '_y'):
                                # 复制文本注释的内容和位置
                                ax1.annotate(artist.get_text(),
                                             xy=(artist._x, artist._y),
                                             xytext=artist.get_position(),
                                             textcoords='offset points',
                                             ha='center',
                                             va='center',
                                             fontsize=artist.get_fontsize(),
                                             bbox=dict(facecolor='white', alpha=0.8, edgecolor='blue', pad=1))
                        except Exception as e:
                            print(f"复制文本注释时出错: {str(e)}")

                    # 添加双底图例
                    legend_elements.append(Line2D([0], [0], color='g', linestyle='--', label='双底形态'))
            except Exception as e:
                print(f"复制双底形态标记时出错: {str(e)}")

        # 调整布局
        self.canvas.fig.tight_layout()

        # 刷新画布
        self.canvas.draw()

    def find_double_bottom(self):
        """查找双底形态"""
        try:
            # 导入必要的类
            from czsc.objects import RawBar
            from czsc.enum import Freq
            # 弹出对话框让用户选择参数
            dialog = QDialog(self)
            dialog.setWindowTitle("双底形态参数设置")
            dialog.setMinimumWidth(300)

            layout = QVBoxLayout(dialog)

            # 添加参数设置
            form_layout = QFormLayout()

            # 两个底之间的最小间隔
            min_distance_spin = QSpinBox()
            min_distance_spin.setRange(5, 100)
            min_distance_spin.setValue(20)
            min_distance_spin.setSingleStep(1)
            form_layout.addRow("最小间隔(交易日):", min_distance_spin)

            # 两个底之间的最大间隔
            max_distance_spin = QSpinBox()
            max_distance_spin.setRange(20, 200)
            max_distance_spin.setValue(60)
            max_distance_spin.setSingleStep(5)
            form_layout.addRow("最大间隔(交易日):", max_distance_spin)

            # 价格差异容差
            price_tolerance_spin = QDoubleSpinBox()
            price_tolerance_spin.setRange(0.01, 0.1)
            price_tolerance_spin.setValue(0.03)
            price_tolerance_spin.setSingleStep(0.01)
            price_tolerance_spin.setDecimals(2)
            form_layout.addRow("价格容差(百分比):", price_tolerance_spin)

            # 成交量增加阈值
            volume_threshold_spin = QDoubleSpinBox()
            volume_threshold_spin.setRange(1.0, 3.0)
            volume_threshold_spin.setValue(1.2)
            volume_threshold_spin.setSingleStep(0.1)
            volume_threshold_spin.setDecimals(1)
            form_layout.addRow("成交量增加阈值(倍):", volume_threshold_spin)

            layout.addLayout(form_layout)

            # 添加确定和取消按钮
            button_box = QHBoxLayout()
            ok_button = QPushButton("确定")
            cancel_button = QPushButton("取消")
            button_box.addWidget(ok_button)
            button_box.addWidget(cancel_button)
            layout.addLayout(button_box)

            # 连接信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)

            # 显示对话框
            if dialog.exec() != QDialog.DialogCode.Accepted:
                return

            # 获取参数
            min_distance = min_distance_spin.value()
            max_distance = max_distance_spin.value()
            price_tolerance = price_tolerance_spin.value()
            volume_threshold = volume_threshold_spin.value()

            # 检查是否有czsc对象
            if 'czsc' not in self.data:
                QMessageBox.warning(self, "警告", "没有找到分析数据！")
                return

            # 更新状态
            self.setWindowTitle(f"{self.windowTitle()} - 正在查找双底形态...")
            QApplication.processEvents()  # 刷新UI

            # 查找双底形态
            # 使用完整的数据
            bars = []
            if 'dates' in self.data and len(self.data['dates']) > 0:
                # 从数据中构造bars对象
                for i in range(len(self.data['dates'])):
                    bar = RawBar(
                        symbol=self.symbol,
                        id=i,  # 使用索引作为id
                        dt=self.data['dates'][i],
                        freq=Freq.D,  # 使用日线频率
                        open=self.data['opens'][i],
                        close=self.data['closes'][i],
                        high=self.data['highs'][i],
                        low=self.data['lows'][i],
                        vol=self.data['volumes'][i],
                        amount=self.data['volumes'][i] * self.data['closes'][i]  # 使用成交量 * 收盘价作为成交额
                    )
                    bars.append(bar)

            if not bars:
                QMessageBox.warning(self, "警告", "没有找到有效的K线数据！")
                return

            double_bottoms = identify_double_bottom(bars, min_distance, max_distance, price_tolerance, volume_threshold)

            if not double_bottoms:
                QMessageBox.information(self, "提示", "未找到符合条件的双底形态")
                self.setWindowTitle(self.windowTitle().replace(" - 正在查找双底形态...", ""))
                return

            # 在图表上标记双底形态
            self.plot_double_bottoms(double_bottoms)

            # 更新状态
            self.setWindowTitle(f"{self.windowTitle().replace(' - 正在查找双底形态...', '')} - 找到 {len(double_bottoms)} 个双底形态")
        except Exception as e:
            print(f"查找双底形态时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"查找双底形态时出错: {str(e)}")

    def plot_double_bottoms(self, double_bottoms):
        """在图表上标记双底形态

        :param double_bottoms: 双底形态列表
        """
        try:
            print("plot_double_bottoms: 开始绘制双底形态")

            # 保存双底形态数据，以便在其他方法中使用
            self.double_bottoms_data = double_bottoms

            # 首先显示一个消息框，这样即使后面的代码出错，用户也能看到结果
            QMessageBox.information(self, "提示", f"找到 {len(double_bottoms)} 个双底形态")

            if not double_bottoms:  # 如果没有双底形态，直接返回
                return

            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 清除原有的双底标记
            for artist in ax1.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'double_bottom':
                    artist.remove()

            # 标记双底形态
            for i, db in enumerate(double_bottoms):
                try:
                    # 第一个底
                    ax1.scatter(db['first_bottom']['index'], db['first_bottom']['price'],
                               marker='o', color='blue', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax1.annotate(f"B1-{i+1}", (db['first_bottom']['index'], db['first_bottom']['price']),
                                xytext=(0, -15), textcoords='offset points', ha='center', va='top', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='blue', pad=1))

                    # 颈线
                    ax1.scatter(db['neckline']['index'], db['neckline']['price'],
                               marker='^', color='purple', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax1.annotate(f"N-{i+1}", (db['neckline']['index'], db['neckline']['price']),
                                xytext=(0, 10), textcoords='offset points', ha='center', va='bottom', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='purple', pad=1))

                    # 第二个底
                    ax1.scatter(db['second_bottom']['index'], db['second_bottom']['price'],
                               marker='o', color='blue', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax1.annotate(f"B2-{i+1}", (db['second_bottom']['index'], db['second_bottom']['price']),
                                xytext=(0, -15), textcoords='offset points', ha='center', va='top', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='blue', pad=1))

                    # 突破点
                    ax1.scatter(db['breakout']['index'], db['breakout']['price'],
                               marker='*', color='red', s=150, zorder=10, alpha=0.7, label='double_bottom')
                    ax1.annotate(f"BR-{i+1}", (db['breakout']['index'], db['breakout']['price']),
                                xytext=(0, 10), textcoords='offset points', ha='center', va='bottom', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))

                    # 连接线
                    ax1.plot([db['first_bottom']['index'], db['neckline']['index'], db['second_bottom']['index'], db['breakout']['index']],
                            [db['first_bottom']['price'], db['neckline']['price'], db['second_bottom']['price'], db['breakout']['price']],
                            'g--', linewidth=1.5, alpha=0.7, label='double_bottom')

                    # 标记颈线 - 使用安全的方式计算xmin和xmax
                    total_bars = len(self.data['dates'])
                    xmin = max(0, min(1, db['first_bottom']['index'] / total_bars))
                    xmax = max(0, min(1, db['breakout']['index'] / total_bars))
                    ax1.axhline(y=db['neckline']['price'], xmin=xmin, xmax=xmax,
                               color='purple', linestyle='--', alpha=0.5, label='double_bottom')

                    # 添加成交量信息 - 安全地计算成交量增加倍数
                    if 'avg_volume_before' in db['breakout'] and db['breakout']['avg_volume_before'] > 0:
                        volume_increase = db['breakout']['volume'] / db['breakout']['avg_volume_before']
                        ax1.annotate(f"Vol: {volume_increase:.1f}x", (db['breakout']['index'], db['breakout']['price']),
                                    xytext=(10, 0), textcoords='offset points', ha='left', va='center', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))
                except Exception as e:
                    print(f"绘制双底形态 {i+1} 时出错: {str(e)}")
                    continue

            try:
                # 添加图例
                from matplotlib.lines import Line2D
                handles, labels = ax1.get_legend_handles_labels()
                unique_labels = []
                unique_handles = []
                for handle, label in zip(handles, labels):
                    if label not in unique_labels or label != 'double_bottom':
                        unique_labels.append(label)
                        unique_handles.append(handle)

                # 添加双底图例
                unique_handles.append(Line2D([0], [0], color='g', linestyle='--', label='双底形态'))
                unique_labels.append('双底形态')

                ax1.legend(unique_handles, unique_labels)
            except Exception as e:
                print(f"添加图例时出错: {str(e)}")

            # 刷新画布
            self.canvas.draw()
            print("plot_double_bottoms: 绘制完成")

        except Exception as e:
            print(f"绘制双底形态时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"绘制双底形态时出错: {str(e)}")

    def calculate_angle(self, n):
        """计算数字n在六方图上对应的角度"""
        # 如果n小于1，返回0度
        if n < 1:
            return 0

        # 使用六方图中的六圈层配置
        all_circles = [
            # 第1圈层 (1-6)
            {"range": (1, 6), "r": 3.0 * 2.0, "step": 60.0, "ref_num": 1, "ref_angle": 60.0},

            # 第2圈层 (7-18)
            {"range": (7, 18), "r": 6.0 * 2.0, "step": 30.0, "ref_num": 7, "ref_angle": 30.0},

            # 第3圈层 (19-36)
            {"range": (19, 36), "r": 9.0 * 2.0, "step": 20.0, "ref_num": 19, "ref_angle": 20.0},

            # 第4圈层 (37-60)
            {"range": (37, 60), "r": 12.0 * 2.0, "step": 15.0, "ref_num": 37, "ref_angle": 15.0},

            # 第5圈层 (61-90)
            {"range": (61, 90), "r": 15.0 * 2.0, "step": 12.0, "ref_num": 61, "ref_angle": 12.0},

            # 第6圈层 (91-126)
            {"range": (91, 126), "r": 18.0 * 2.0, "step": 10.0, "ref_num": 91, "ref_angle": 10.0},

            # 第7圈层 (127-168)
            {"range": (127, 168), "r": 21.0 * 2.0, "step": 8.571, "ref_num": 127, "ref_angle": 8.571},

            # 第8圈层 (169-216)
            {"range": (169, 216), "r": 24.0 * 2.0, "step": 7.5, "ref_num": 169, "ref_angle": 7.5},

            # 第9圈层 (217-270)
            {"range": (217, 270), "r": 27.0 * 2.0, "step": 6.667, "ref_num": 217, "ref_angle": 6.667},

            # 第10圈层 (271-330)
            {"range": (271, 330), "r": 30.0 * 2.0, "step": 6.0, "ref_num": 271, "ref_angle": 6.0},

            # 第11圈层 (331-396)
            {"range": (331, 396), "r": 33.0 * 2.0, "step": 5.455, "ref_num": 331, "ref_angle": 5.455},

            # 第12圈层 (397-468)
            {"range": (397, 468), "r": 36.0 * 2.0, "step": 5.0, "ref_num": 397, "ref_angle": 5.0},

            # 第13圈层 (469-546)
            {"range": (469, 546), "r": 39.0 * 2.0, "step": 4.615, "ref_num": 469, "ref_angle": 4.615},

            # 第14圈层 (547-630)
            {"range": (547, 630), "r": 42.0 * 2.0, "step": 4.286, "ref_num": 547, "ref_angle": 4.286},

            # 第15圈层 (631-720)
            {"range": (631, 720), "r": 45.0 * 2.0, "step": 4.0, "ref_num": 631, "ref_angle": 4.0},

            # 第16圈层 (721-816)
            {"range": (721, 816), "r": 48.0 * 2.0, "step": 3.75, "ref_num": 721, "ref_angle": 3.75},

            # 第17圈层 (817-918)
            {"range": (817, 918), "r": 51.0 * 2.0, "step": 3.529, "ref_num": 817, "ref_angle": 3.529},

            # 第18圈层 (919-1026)
            {"range": (919, 1026), "r": 54.0 * 2.0, "step": 3.333, "ref_num": 919, "ref_angle": 3.333}
        ]

        # 遍历圈层配置，找到n所在的圈层
        for circle in all_circles:
            start_digit, end_digit = circle["range"]
            if start_digit <= n <= end_digit:
                step_angle = circle["step"]
                ref_num = circle["ref_num"]
                ref_angle = circle["ref_angle"]

                # 计算角度
                angle = (n - ref_num) * step_angle + ref_angle
                # 规范化角度到0-360范围
                angle = angle % 360
                return angle

        # 如果n超出了圈层范围，使用六方图中的计算公式
        # 使用公式直接计算圈层，避免无限循环
        # 解方程：3*ring^2 - 3*ring + 1 <= n <= 3*ring^2 + 3*ring
        # 近似解：ring ≈ sqrt(n/3)
        ring_estimate = max(1, int(math.sqrt(n / 3)))

        # 从估计值开始检查几个圈层
        for ring in range(max(1, ring_estimate - 1), ring_estimate + 2):
            start = 3 * ring * ring - 3 * ring + 1
            end = 3 * ring * ring + 3 * ring
            if start <= n <= end:
                # 计算在当前圈层中的位置和角度
                position = n - start + 1
                angle_step = 60 / ring  # 每个数字之间的角度
                angle = position * angle_step
                return angle % 360  # 规范化到 [0°, 360°)

        # 如果所有圈层都不匹配，返回0度
        return 0

    def find_angle_points(self):
        """查找两个低点和一个高点，其中两个低点之间的时间值与最高点的价格成九十度的倍数"""
        try:
            # 重置变量，确保每次计算都是从头开始的
            self.angle_points_data = []  # 清除之前的角度关系三点数据

            # 记录开始时间
            start_time = time.time()
            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 检查是否有数据
            if 'dates' not in self.data or len(self.data['dates']) < 3:
                QMessageBox.warning(self, "警告", "数据不足，无法查找角度关系三点")
                return

            # 创建参数设置对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("角度关系三点参数设置")
            dialog.setMinimumWidth(300)

            layout = QVBoxLayout(dialog)

            # 添加角度设置
            angle_layout = QHBoxLayout()
            angle_label = QLabel("目标角度（度）:")
            angle_spin = QDoubleSpinBox()
            angle_spin.setRange(0, 360)
            angle_spin.setValue(90)
            angle_spin.setSingleStep(1)
            angle_spin.setDecimals(1)
            angle_layout.addWidget(angle_label)
            angle_layout.addWidget(angle_spin)
            layout.addLayout(angle_layout)

            # 添加时间范围设置
            time_range_group = QGroupBox("时间范围设置")
            time_range_layout = QVBoxLayout(time_range_group)

            # 最小时间差
            min_time_layout = QHBoxLayout()
            min_time_label = QLabel("最小时间差:")
            min_time_spin = QSpinBox()
            min_time_spin.setRange(1, 1000)
            min_time_spin.setValue(150)
            min_time_spin.setSingleStep(10)
            min_time_layout.addWidget(min_time_label)
            min_time_layout.addWidget(min_time_spin)
            time_range_layout.addLayout(min_time_layout)

            # 最大时间差
            max_time_layout = QHBoxLayout()
            max_time_label = QLabel("最大时间差:")
            max_time_spin = QSpinBox()
            max_time_spin.setRange(1, 1000)
            max_time_spin.setValue(200)
            max_time_spin.setSingleStep(10)
            max_time_layout.addWidget(max_time_label)
            max_time_layout.addWidget(max_time_spin)
            time_range_layout.addLayout(max_time_layout)

            layout.addWidget(time_range_group)

            # 添加确定和取消按钮
            button_box = QHBoxLayout()
            ok_button = QPushButton("确定")
            cancel_button = QPushButton("取消")
            button_box.addWidget(ok_button)
            button_box.addWidget(cancel_button)
            layout.addLayout(button_box)

            # 连接信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)

            # 显示对话框
            if dialog.exec() != QDialog.DialogCode.Accepted:
                return

            # 获取参数
            angle = angle_spin.value()
            min_time_diff = min_time_spin.value()
            max_time_diff = max_time_spin.value()

            # 清除原有的所有标记，包括角度关系三点、未来点和连接线
            labels_to_remove = ['angle_points', 'future_point', 'future_line']
            for artist in ax1.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() in labels_to_remove:
                    artist.remove()

            # 清除所有标注
            for artist in ax1.texts:
                # 尝试判断是否是我们添加的标注
                if hasattr(artist, 'get_bbox') and artist.get_bbox() is not None:
                    artist.remove()

            # 第一步：识别所有的高低点
            print("\n开始查找角度关系三点...")
            print(f"目标角度: {angle}度")
            print("第一步: 识别所有的高低点...")

            # 显示进度对话框
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("识别高低点")
            progress_dialog.setFixedSize(300, 100)
            layout = QVBoxLayout(progress_dialog)
            label = QLabel("正在识别高低点...")
            layout.addWidget(label)
            progress_dialog.show()
            QApplication.processEvents()  # 刷新UI

            # 获取所有低点和高点
            lows = []
            highs = []
            for i in range(1, len(self.data['dates']) - 1):
                # 判断是否是低点（左右两个点的低点都高于当前点）
                if self.data['lows'][i] < self.data['lows'][i-1] and self.data['lows'][i] < self.data['lows'][i+1]:
                    lows.append((i, self.data['lows'][i], self.data['dates'][i]))
                # 判断是否是高点（左右两个点的高点都低于当前点）
                if self.data['highs'][i] > self.data['highs'][i-1] and self.data['highs'][i] > self.data['highs'][i+1]:
                    highs.append((i, self.data['highs'][i], self.data['dates'][i]))

            # 更新进度对话框
            if 'freq' in self.data and self.data['freq'] == '周线':
                label.setText(f"已识别 {len(lows)} 个低点和 {len(highs)} 个高点\n正在计算角度关系...\n时间限制: 150-200周")
            else:
                label.setText(f"已识别 {len(lows)} 个低点和 {len(highs)} 个高点\n正在计算角度关系...\n时间限制: 150-200天")
            QApplication.processEvents()  # 刷新UI

            # 输出日志
            print(f"已识别 {len(lows)} 个低点和 {len(highs)} 个高点")
            print("低点示例: ", lows[:3] if len(lows) > 3 else lows)
            print("高点示例: ", highs[:3] if len(highs) > 3 else highs)

            # 第二步：预处理高点数据，按时间排序
            print("第二步: 预处理高点数据，按时间排序...")
            highs_by_date = {}
            for high_idx, high_price, high_date in highs:
                if high_date not in highs_by_date:
                    highs_by_date[high_date] = []
                highs_by_date[high_date].append((high_idx, high_price, high_date))

            print(f"预处理完成，共有 {len(highs_by_date)} 个不同的日期有高点")

            # 第三步：查找满足条件的三点
            print("第三步: 查找满足条件的三点...")
            if 'freq' in self.data and self.data['freq'] == '周线':
                print(f"时间限制: 两个低点之间的交易周差在{min_time_diff}到{max_time_diff}周之间")
            else:
                print(f"时间限制: 两个低点之间的时间差在{min_time_diff}到{max_time_diff}天之间")
            angle_points = []
            total_combinations = len(lows) * (len(lows) - 1) // 2
            print(f"需要处理 {total_combinations} 对低点组合")
            processed = 0
            found_count = 0  # 记录找到的角度关系三点数量

            for i in range(len(lows)):
                for j in range(i+1, len(lows)):
                    processed += 1
                    if processed % 100 == 0 or processed == total_combinations:  # 每100次或最后一次更新进度
                        if 'freq' in self.data and self.data['freq'] == '周线':
                            label.setText(f"已识别 {len(lows)} 个低点和 {len(highs)} 个高点\n正在计算角度关系... ({processed}/{total_combinations})\n时间限制: {min_time_diff}-{max_time_diff}周, 已找到: {found_count} 组")
                            print(f"进度: {processed}/{total_combinations} ({processed/total_combinations*100:.2f}%), 时间限制: {min_time_diff}-{max_time_diff}周, 已找到: {found_count} 组")
                        else:
                            label.setText(f"已识别 {len(lows)} 个低点和 {len(highs)} 个高点\n正在计算角度关系... ({processed}/{total_combinations})\n时间限制: {min_time_diff}-{max_time_diff}天, 已找到: {found_count} 组")
                            print(f"进度: {processed}/{total_combinations} ({processed/total_combinations*100:.2f}%), 时间限制: {min_time_diff}-{max_time_diff}天, 已找到: {found_count} 组")
                        QApplication.processEvents()  # 刷新UI

                    low1_idx, low1_price, low1_date = lows[i]
                    low2_idx, low2_price, low2_date = lows[j]

                    # 计算两个低点之间的时间差
                    if 'freq' in self.data and self.data['freq'] == '周线':
                        # 对于周线数据，使用交易周之间的差值
                        time_diff = low2_idx - low1_idx
                        print(f"  周线时间差: {time_diff} 周")
                    else:
                        # 对于日线数据，使用日历天数
                        time_diff = (low2_date - low1_date).total_seconds() / (24 * 3600)
                        print(f"  日线时间差: {time_diff:.2f} 天")

                    # 限制时间差在用户设置的范围内
                    if time_diff < min_time_diff or time_diff > max_time_diff:
                        continue

                    # 只考虑两个低点之间的最高点
                    max_high_price = 0
                    max_high_idx = None
                    max_high_date = None

                    # 找出两个低点之间的最高点
                    for high_date in highs_by_date:
                        if low1_date < high_date < low2_date:
                            for high_idx, high_price, high_date in highs_by_date[high_date]:
                                if high_price > max_high_price:
                                    max_high_price = high_price
                                    max_high_idx = high_idx
                                    max_high_date = high_date

                    # 如果找到了最高点，计算角度
                    if max_high_idx is not None:
                        high_idx, high_price, high_date = max_high_idx, max_high_price, max_high_date
                        # 将时间和价格转换为六方图上的角度
                        # 使用六方图中的calculate_angle函数
                        time_angle = self.calculate_angle(time_diff)
                        price_angle = self.calculate_angle(int(high_price * 10))

                        # 计算角度差
                        angle_diff = abs(time_angle - price_angle)
                        if angle_diff > 180:
                            angle_diff = 360 - angle_diff

                        # 在日志中输出每对低点之间的时间和最高点的度数信息
                        if processed % 1000 == 0:  # 每处理1000对输出一次，避免日志过多
                            if 'freq' in self.data and self.data['freq'] == '周线':
                                print(f"\n比较: 两个低点之间的时间差={time_diff}周 -> 度数={time_angle:.2f}度")
                                print(f"      两个低点之间的最高价={high_price:.2f}*10={high_price*10:.2f} -> 度数={price_angle:.2f}度")
                                print(f"      角度差={angle_diff:.2f}度 (目标角度: {angle} 度)")
                            else:
                                print(f"\n比较: 两个低点之间的时间差={time_diff:.2f}天 -> 度数={time_angle:.2f}度")
                                print(f"      两个低点之间的最高价={high_price:.2f}*10={high_price*10:.2f} -> 度数={price_angle:.2f}度")
                                print(f"      角度差={angle_diff:.2f}度 (目标角度: {angle} 度)")

                        # 计算两个低点的价格在六方图上的角度
                        low1_price_angle = self.calculate_angle(int(low1_price * 10))
                        low2_price_angle = self.calculate_angle(int(low2_price * 10))

                        # 计算两个低点价格的角度差
                        low_price_angle_diff = abs(low1_price_angle - low2_price_angle)
                        if low_price_angle_diff > 180:
                            low_price_angle_diff = 360 - low_price_angle_diff

                        # 在日志中输出两个低点价格的角度差
                        if processed % 1000 == 0:  # 每处理1000对输出一次，避免日志过多
                            print(f"      低点1价格={low1_price:.2f}*10={low1_price*10:.2f} -> 度数={low1_price_angle:.2f}度")
                            print(f"      低点2价格={low2_price:.2f}*10={low2_price*10:.2f} -> 度数={low2_price_angle:.2f}度")
                            print(f"      两个低点价格的角度差={low_price_angle_diff:.2f}度")

                        # 检查是否同时满足两个条件：
                        # 1. 两个低点之间的时间与最高点的价格成角度关系
                        # 2. 两个低点的价格成角度关系
                        target_angle = angle  # 用户输入的角度
                        time_price_match = abs(angle_diff - target_angle) <= 5 or abs(angle_diff - (180 - target_angle)) <= 5
                        low_prices_match = abs(low_price_angle_diff - target_angle) <= 5 or abs(low_price_angle_diff - (180 - target_angle)) <= 5

                        if time_price_match and low_prices_match:
                            angle_points.append((low1_idx, low1_price, low2_idx, low2_price, high_idx, high_price))
                            found_count += 1

                            # 每找到一组就输出日志
                            if found_count <= 10:  # 只输出前10组，避免日志过多
                                print(f"\n找到第 {found_count} 组角度关系三点:")
                                print(f"  低点1: 索引={low1_idx}, 价格={low1_price:.2f}, 日期={low1_date}")
                                print(f"  两个低点之间的最高点: 索引={high_idx}, 价格={high_price:.2f}, 日期={high_date}")
                                print(f"  低点2: 索引={low2_idx}, 价格={low2_price:.2f}, 日期={low2_date}")
                                # 计算六方图上的角度
                                time_angle = self.calculate_angle(time_diff)
                                price_angle = self.calculate_angle(int(high_price * 10))

                                # 计算两个低点的价格在六方图上的角度
                                low1_price_angle = self.calculate_angle(int(low1_price * 10))
                                low2_price_angle = self.calculate_angle(int(low2_price * 10))
                                low_price_angle_diff = abs(low1_price_angle - low2_price_angle)
                                if low_price_angle_diff > 180:
                                    low_price_angle_diff = 360 - low_price_angle_diff

                                if 'freq' in self.data and self.data['freq'] == '周线':
                                    print(f"  两个低点之间的时间差: {time_diff} 周 -> 六方图度数: {time_angle:.2f} 度")
                                    print(f"  两个低点之间的最高价: {high_price:.2f} -> 价格*10={high_price*10:.2f} -> 六方图度数: {price_angle:.2f} 度")
                                    print(f"  时间与价格的角度差: {angle_diff:.2f} 度 (目标角度: {angle} 度, 误差: {abs(angle_diff - angle):.2f} 度)")
                                    print(f"  低点1价格: {low1_price:.2f} -> 价格*10={low1_price*10:.2f} -> 六方图度数: {low1_price_angle:.2f} 度")
                                    print(f"  低点2价格: {low2_price:.2f} -> 价格*10={low2_price*10:.2f} -> 六方图度数: {low2_price_angle:.2f} 度")
                                    print(f"  两个低点价格的角度差: {low_price_angle_diff:.2f} 度 (目标角度: {angle} 度, 误差: {abs(low_price_angle_diff - angle):.2f} 度)")
                                else:
                                    print(f"  两个低点之间的时间差: {time_diff:.2f} 天 -> 六方图度数: {time_angle:.2f} 度")
                                    print(f"  两个低点之间的最高价: {high_price:.2f} -> 价格*10={high_price*10:.2f} -> 六方图度数: {price_angle:.2f} 度")
                                    print(f"  时间与价格的角度差: {angle_diff:.2f} 度 (目标角度: {angle} 度, 误差: {abs(angle_diff - angle):.2f} 度)")
                                    print(f"  低点1价格: {low1_price:.2f} -> 价格*10={low1_price*10:.2f} -> 六方图度数: {low1_price_angle:.2f} 度")
                                    print(f"  低点2价格: {low2_price:.2f} -> 价格*10={low2_price*10:.2f} -> 六方图度数: {low2_price_angle:.2f} 度")
                                    print(f"  两个低点价格的角度差: {low_price_angle_diff:.2f} 度 (目标角度: {angle} 度, 误差: {abs(low_price_angle_diff - angle):.2f} 度)")

            # 关闭进度对话框
            progress_dialog.close()

            # 输出查找结果的总结
            print(f"\n查找完成，共找到 {len(angle_points)} 组满足条件的角度关系三点")
            if 'freq' in self.data and self.data['freq'] == '周线':
                print(f"时间限制: {min_time_diff}-{max_time_diff}周, 目标角度: {angle}度")
            else:
                print(f"时间限制: {min_time_diff}-{max_time_diff}天, 目标角度: {angle}度")
            print(f"处理了 {processed} 对低点组合，总计算时间: {time.time() - start_time:.2f} 秒")

            if not angle_points:
                QMessageBox.information(self, "提示", "未找到满足条件的角度关系三点")
                return

            # 绘制满足条件的三点和连接线
            print("\n开始绘制角度关系三点...")

            # 定义不同组的颜色
            group_colors = [
                {'color': 'red', 'dark_color': 'darkred', 'edge_color': '#8B0000'},
                {'color': 'blue', 'dark_color': 'darkblue', 'edge_color': '#00008B'},
                {'color': 'green', 'dark_color': 'darkgreen', 'edge_color': '#006400'},
                {'color': 'purple', 'dark_color': 'darkviolet', 'edge_color': '#9400D3'},
                {'color': 'orange', 'dark_color': 'darkorange', 'edge_color': '#FF8C00'},
                {'color': 'brown', 'dark_color': 'saddlebrown', 'edge_color': '#8B4513'},
                {'color': 'magenta', 'dark_color': 'darkmagenta', 'edge_color': '#8B008B'},
                {'color': 'teal', 'dark_color': 'darkcyan', 'edge_color': '#008B8B'}
            ]

            for idx, (low1_idx, low1_price, low2_idx, low2_price, high_idx, high_price) in enumerate(angle_points):
                # 选择当前组的颜色
                color_idx = idx % len(group_colors)
                group_color = group_colors[color_idx]['color']
                group_dark_color = group_colors[color_idx]['dark_color']
                group_edge_color = group_colors[color_idx]['edge_color']
                print(f"绘制第 {idx+1} 组角度关系三点")

                # 计算未来点的信息
                future_idx = high_idx + (low2_idx - low1_idx)  # 从高点开始，平移两个低点之间的时间差
                if future_idx < len(self.data['dates']):
                    future_date = self.data['dates'][future_idx].strftime('%Y-%m-%d')
                    future_price = self.data['closes'][future_idx]
                    print(f"  未来点: 索引={future_idx}, 价格={future_price:.2f}, 日期={future_date}")
                    print(f"  从高点平移的时间差: {low2_idx - low1_idx} (与两个低点之间的时间差相同)")
                else:
                    print(f"  未来点索引 {future_idx} 超出数据范围，无法绘制")

                # 获取日期字符串
                low1_date_str = self.data['dates'][low1_idx].strftime('%Y-%m-%d')
                high_date_str = self.data['dates'][high_idx].strftime('%Y-%m-%d')
                low2_date_str = self.data['dates'][low2_idx].strftime('%Y-%m-%d')

                # 计算时间差和角度
                if 'freq' in self.data and self.data['freq'] == '周线':
                    time_diff = low2_idx - low1_idx
                    time_unit = '周'
                else:
                    time_diff = (self.data['dates'][low2_idx] - self.data['dates'][low1_idx]).total_seconds() / (24 * 3600)
                    time_unit = '天'

                # 计算六方图上的角度
                time_angle = self.calculate_angle(time_diff)
                price_angle = self.calculate_angle(int(high_price * 10))
                low1_price_angle = self.calculate_angle(int(low1_price * 10))
                low2_price_angle = self.calculate_angle(int(low2_price * 10))

                # 计算角度差
                angle_diff = abs(time_angle - price_angle)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                low_price_angle_diff = abs(low1_price_angle - low2_price_angle)
                if low_price_angle_diff > 180:
                    low_price_angle_diff = 360 - low_price_angle_diff

                # 绘制三个点 - 使用更小的圆点并增加透明度
                ax1.scatter([low1_idx, high_idx, low2_idx], [low1_price, high_price, low2_price],
                           color=group_color, s=50, alpha=0.5, zorder=10, label='angle_points',
                           edgecolors=group_dark_color, linewidths=1.5)  # 添加边框以增强可见性

                # 连接三个点
                ax1.plot([low1_idx, high_idx, low2_idx], [low1_price, high_price, low2_price],
                        color=group_color, linestyle='-', linewidth=2, alpha=0.7, label='angle_points')

                # 计算从高点开始平移相同时间差的未来点
                future_idx = high_idx + (low2_idx - low1_idx)  # 从高点开始，平移两个低点之间的时间差

                # 检查未来点是否超出数据范围
                if future_idx < len(self.data['dates']):
                    # 绘制未来点 - 使用与该组相同的颜色，但使用不同的标记形状
                    ax1.scatter([future_idx], [self.data['closes'][future_idx]],
                               color=group_color, s=100, alpha=0.7, zorder=10, label='future_point',
                               marker='*', edgecolors=group_dark_color, linewidths=1.5)

                    # 添加从高点到未来点的虚线
                    ax1.plot([high_idx, future_idx], [high_price, self.data['closes'][future_idx]],
                            color=group_color, linestyle='--', linewidth=1.5, alpha=0.5, label='future_line')

                    # 添加未来点的标注
                    ax1.annotate(f"F-{idx+1}\n{self.data['closes'][future_idx]:.2f}",
                                (future_idx, self.data['closes'][future_idx]), xytext=(0, 20),
                                textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor=group_edge_color, pad=1),
                                color=group_dark_color)

                # 添加标注 - 低点1
                ax1.annotate(f"L1-{idx+1}\n{low1_price:.2f}\n度数:{low1_price_angle:.1f}°",
                            (low1_idx, low1_price), xytext=(0, -20),
                            textcoords='offset points', ha='center', va='top', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.8, edgecolor=group_edge_color, pad=1),
                            color=group_dark_color)

                # 添加标注 - 高点
                ax1.annotate(f"H-{idx+1}\n{high_price:.2f}\n度数:{price_angle:.1f}°",
                            (high_idx, high_price), xytext=(0, 20),
                            textcoords='offset points', ha='center', va='bottom', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.8, edgecolor=group_edge_color, pad=1),
                            color=group_dark_color)

                # 添加标注 - 低点2
                ax1.annotate(f"L2-{idx+1}\n{low2_price:.2f}\n度数:{low2_price_angle:.1f}°",
                            (low2_idx, low2_price), xytext=(0, -20),
                            textcoords='offset points', ha='center', va='top', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.8, edgecolor=group_edge_color, pad=1),
                            color=group_dark_color)

                # 添加时间差和角度差的标注
                # 计算标注的位置 - 在两个低点之间的中点上方
                mid_x = (low1_idx + low2_idx) / 2
                mid_y = (low1_price + low2_price) / 2  # 使用两个低点的平均高度

                # 根据组的索引调整标注的垂直偏移，避免重叠
                y_offset = -30 - (idx % 3) * 40  # 每三组循环一次偏移量

                # 为每个组创建不同的背景色
                light_color = f"light{group_color}" if group_color in ['blue', 'green', 'cyan', 'yellow'] else f"#{group_color}15"

                # 添加标注，使用更明显的背景色和边框
                ax1.annotate(f"组 {idx+1} - 时间差: {time_diff:.0f}{time_unit} (度数:{time_angle:.1f}°)\n"
                            f"时间-价格角度差: {angle_diff:.1f}° | "
                            f"低点价格角度差: {low_price_angle_diff:.1f}°",
                            (mid_x, mid_y), xytext=(0, y_offset),
                            textcoords='offset points', ha='center', va='center', fontsize=8, fontweight='bold',
                            bbox=dict(facecolor='white', alpha=0.9, edgecolor=group_edge_color, linewidth=2, pad=3),
                            color=group_dark_color,
                            arrowprops=dict(arrowstyle='->', color=group_dark_color, lw=1.5))

                # 旧的标注已被替换为更详细的标注

            # 更新标题
            ax1.set_title(f"{ax1.get_title()} - 角度关系三点 ({angle}度)")

            # 刷新画布
            self.canvas.draw()

            # 显示消息框
            time_unit = '周' if 'freq' in self.data and self.data['freq'] == '周线' else '天'
            QMessageBox.information(self, "提示", f"找到 {len(angle_points)} 组满足以下两个条件的角度关系三点:\n\n"
                                   f"1. 两个低点之间的时间与最高点的价格成{angle}度关系\n"
                                   f"2. 两个低点的价格成{angle}度关系\n\n"
                                   f"时间范围: {min_time_diff}-{max_time_diff}{time_unit}\n\n"
                                   f"注意: 已从每组的高点开始，平移相同的时间差，并标记未来点(F)")

            # 保存找到的角度关系三点数据，以便其他方法可以使用
            self.angle_points_data = angle_points

            # 输出完成日志
            print(f"\n绘制完成，总计算时间: {time.time() - start_time:.2f} 秒")

        except Exception as e:
            print(f"查找角度关系三点时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"查找角度关系三点时出错: {str(e)}")

    def find_zero_degree_prices(self):
        """计算高低价格为零度的倍数"""
        try:
            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 检查是否有数据
            if 'dates' not in self.data or len(self.data['dates']) < 3:
                QMessageBox.warning(self, "警告", "数据不足，无法计算零度价格")
                return

            # 弹出对话框让用户选择参数
            dialog = QDialog(self)
            dialog.setWindowTitle("零度价格参数设置")
            dialog.setMinimumWidth(300)

            layout = QVBoxLayout(dialog)

            # 添加角度设置
            angle_layout = QHBoxLayout()
            angle_label = QLabel("目标角度（度）:")
            angle_spin = QDoubleSpinBox()
            angle_spin.setRange(0, 360)
            angle_spin.setValue(0)  # 默认为0度
            angle_spin.setSingleStep(90)  # 每次增加90度
            angle_spin.setDecimals(1)
            angle_layout.addWidget(angle_label)
            angle_layout.addWidget(angle_spin)
            layout.addLayout(angle_layout)

            # 添加误差范围设置
            error_layout = QHBoxLayout()
            error_label = QLabel("误差范围（度）:")
            error_spin = QDoubleSpinBox()
            error_spin.setRange(0.1, 10)
            error_spin.setValue(5)  # 默认为5度
            error_spin.setSingleStep(0.1)
            error_spin.setDecimals(1)
            error_layout.addWidget(error_label)
            error_layout.addWidget(error_spin)
            layout.addLayout(error_layout)

            # 添加确定和取消按钮
            button_box = QHBoxLayout()
            ok_button = QPushButton("确定")
            cancel_button = QPushButton("取消")
            button_box.addWidget(ok_button)
            button_box.addWidget(cancel_button)
            layout.addLayout(button_box)

            # 连接信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)

            # 显示对话框
            if dialog.exec() != QDialog.DialogCode.Accepted:
                return

            # 获取参数
            target_angle = angle_spin.value()
            error_range = error_spin.value()

            # 清除原有的标记
            for artist in ax1.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'zero_degree_price':
                    artist.remove()

            # 清除标注
            for artist in ax1.texts:
                if hasattr(artist, 'get_bbox') and artist.get_bbox() is not None:
                    artist.remove()

            # 计算所有价格的角度
            zero_degree_prices = []
            for i in range(len(self.data['dates'])):
                high_price = self.data['highs'][i]
                low_price = self.data['lows'][i]

                # 计算高价和低价的角度
                high_angle = self.calculate_angle(int(high_price * 10))
                low_angle = self.calculate_angle(int(low_price * 10))

                # 检查是否接近目标角度
                high_diff = min(abs(high_angle - target_angle), abs(high_angle - (target_angle + 360)))
                low_diff = min(abs(low_angle - target_angle), abs(low_angle - (target_angle + 360)))

                if high_diff <= error_range:
                    zero_degree_prices.append((i, high_price, 'high', high_angle))

                if low_diff <= error_range:
                    zero_degree_prices.append((i, low_price, 'low', low_angle))

            if not zero_degree_prices:
                QMessageBox.information(self, "提示", f"未找到角度接近 {target_angle}° 的价格（误差范围: ±{error_range}°）")
                return

            # 绘制满足条件的价格点
            print(f"\n找到 {len(zero_degree_prices)} 个角度接近 {target_angle}° 的价格点")

            # 定义高价和低价的颜色
            high_color = 'red'
            low_color = 'green'

            for idx, (i, price, price_type, angle) in enumerate(zero_degree_prices):
                # 选择颜色
                color = high_color if price_type == 'high' else low_color

                # 绘制点
                ax1.scatter([i], [price], color=color, s=80, alpha=0.7, zorder=10,
                           marker='*', edgecolors='black', linewidths=1.5, label='zero_degree_price')

                # 添加标注
                ax1.annotate(f"{price_type.upper()}-{idx+1}\n{price:.2f}\n度数:{angle:.1f}°",
                            (i, price), xytext=(0, 10 if price_type == 'high' else -10),
                            textcoords='offset points', ha='center',
                            va='bottom' if price_type == 'high' else 'top', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.8, edgecolor=color, pad=1),
                            color=color)

            # 刷新画布
            self.canvas.draw()

            # 显示消息框
            QMessageBox.information(self, "提示", f"找到 {len(zero_degree_prices)} 个角度接近 {target_angle}° 的价格点\n\n"
                                   f"高价点标记为红色，低价点标记为绿色\n"
                                   f"误差范围: ±{error_range}°")

        except Exception as e:
            print(f"计算零度价格时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"计算零度价格时出错: {str(e)}")

    def find_n_pattern(self):
        """查找N字形态"""
        try:
            # 检查是否有数据
            if 'czsc' not in self.data or not self.data['czsc']:
                QMessageBox.warning(self, "警告", "请先分析数据！")
                return

            # 创建一个简单的消息框，询问是否使用OpenCV方法
            use_opencv = QMessageBox.question(self, "选择方法", "是否使用OpenCV方法查找N字形态？\n\n选择'是'使用OpenCV图像处理方法\n选择'否'使用改进的峰值检测方法",
                                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes

            # 弹出对话框让用户选择参数
            dialog = QDialog(self)
            dialog.setWindowTitle("N字形态参数设置")
            dialog.setMinimumWidth(300)

            layout = QVBoxLayout(dialog)

            # 添加参数设置
            form_layout = QFormLayout()

            if use_opencv:
                # OpenCV方法参数
                window_size_spin = QSpinBox()
                window_size_spin.setRange(30, 150)
                window_size_spin.setValue(80)  # 增大默认窗口大小
                window_size_spin.setSingleStep(10)
                form_layout.addRow("窗口大小:", window_size_spin)

                step_spin = QSpinBox()
                step_spin.setRange(1, 20)
                step_spin.setValue(10)  # 增大步长以减少计算量
                step_spin.setSingleStep(1)
                form_layout.addRow("步长:", step_spin)

                # 添加最小振幅参数
                min_amplitude_spin = QDoubleSpinBox()
                min_amplitude_spin.setRange(0.05, 0.5)
                min_amplitude_spin.setValue(0.15)  # 15%的最小振幅
                min_amplitude_spin.setSingleStep(0.05)
                min_amplitude_spin.setDecimals(2)
                form_layout.addRow("最小振幅:", min_amplitude_spin)

                # 添加斜率阈值参数
                slope_threshold_spin = QDoubleSpinBox()
                slope_threshold_spin.setRange(0.1, 1.0)
                slope_threshold_spin.setValue(0.3)  # 更严格的斜率要求
                slope_threshold_spin.setSingleStep(0.1)
                slope_threshold_spin.setDecimals(1)
                form_layout.addRow("斜率阈值:", slope_threshold_spin)
            else:
                # 改进的峰值检测方法参数
                window_size_spin = QSpinBox()
                window_size_spin.setRange(10, 100)
                window_size_spin.setValue(40)  # 增大默认窗口大小
                window_size_spin.setSingleStep(5)
                form_layout.addRow("窗口大小:", window_size_spin)

                min_trend_length_spin = QSpinBox()
                min_trend_length_spin.setRange(3, 20)
                min_trend_length_spin.setValue(5)
                min_trend_length_spin.setSingleStep(1)
                form_layout.addRow("最小趋势长度:", min_trend_length_spin)

                # 添加价格变化阈值参数
                price_change_spin = QDoubleSpinBox()
                price_change_spin.setRange(0.01, 0.3)
                price_change_spin.setValue(0.05)  # 5%的价格变化
                price_change_spin.setSingleStep(0.01)
                price_change_spin.setDecimals(2)
                form_layout.addRow("价格变化阈值:", price_change_spin)

                # 添加振幅阈值参数
                amplitude_spin = QDoubleSpinBox()
                amplitude_spin.setRange(0.05, 0.5)
                amplitude_spin.setValue(0.1)  # 10%的振幅
                amplitude_spin.setSingleStep(0.05)
                amplitude_spin.setDecimals(2)
                form_layout.addRow("振幅阈值:", amplitude_spin)

            layout.addLayout(form_layout)

            # 添加按钮
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            layout.addWidget(button_box)

            # 显示对话框
            if dialog.exec() != QDialog.DialogCode.Accepted:
                return

            # 获取参数值
            if use_opencv:
                window_size = window_size_spin.value()
                step = step_spin.value()
                min_amplitude = min_amplitude_spin.value()
                slope_threshold = slope_threshold_spin.value()

                # 更新状态
                self.setWindowTitle(f"{self.windowTitle()} - 正在使用OpenCV增强方法查找N字形态...")

                # 调用OpenCV增强方法查找N字形态
                n_patterns = self.data['czsc'].identify_n_pattern_with_cv(
                    window_size=window_size,
                    step=step,
                    min_amplitude=min_amplitude,
                    slope_threshold=slope_threshold
                )
            else:
                window_size = window_size_spin.value()
                min_trend_length = min_trend_length_spin.value()
                price_change_threshold = price_change_spin.value()
                amplitude_threshold = amplitude_spin.value()

                # 更新状态
                self.setWindowTitle(f"{self.windowTitle()} - 正在使用峰值检测查找N字形态...")

                # 调用改进的峰值检测方法查找N字形态
                n_patterns = self.data['czsc'].identify_n_pattern(
                    window_size=window_size,
                    min_trend_length=min_trend_length,
                    price_change_threshold=price_change_threshold,
                    amplitude_threshold=amplitude_threshold
                )

            # 检查是否找到N字形态
            if not n_patterns:
                QMessageBox.information(self, "提示", "未找到符合条件的N字形态")
                self.setWindowTitle(self.windowTitle().replace(" - 正在使用OpenCV查找N字形态...", "").replace(" - 正在使用数学分析查找N字形态...", ""))
                return

            # 在图表上标记N字形态
            self.plot_n_patterns(n_patterns)

            # 更新状态
            self.setWindowTitle(f"{self.windowTitle().replace(' - 正在使用OpenCV查找N字形态...', '').replace(' - 正在使用数学分析查找N字形态...', '')} - 找到 {len(n_patterns)} 个N字形态")

        except Exception as e:
            print(f"查找N字形态时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"查找N字形态时出错: {str(e)}")

    def plot_n_patterns(self, n_patterns):
        """在图表上标记N字形态

        :param n_patterns: N字形态列表
        """
        try:
            print("plot_n_patterns: 开始绘制N字形态")

            # 保存N字形态数据，以便在放大图中使用
            self.n_patterns_data = n_patterns

            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 获取日期列表，用于查找索引
            dates = self.data['dates']

            # 定义N字形态的颜色
            n_pattern_color = 'cyan'

            # 遍历所有N字形态
            for i, pattern in enumerate(n_patterns):
                try:
                    # 获取N字形态的起始和结束索引
                    start_idx = dates.index(pattern['start_dt']) if pattern['start_dt'] in dates else pattern['start_idx']
                    end_idx = dates.index(pattern['end_dt']) if pattern['end_dt'] in dates else pattern['end_idx']

                    # 在N字形态区域上方绘制标记
                    # 计算标记的位置
                    y_pos = max(self.data['highs'][start_idx:end_idx+1]) * 1.02

                    # 添加N字形态标记
                    ax1.text((start_idx + end_idx) / 2, y_pos, f"N{i+1}",
                             horizontalalignment='center', verticalalignment='bottom',
                             fontsize=10, color='black', fontweight='bold',
                             bbox=dict(facecolor=n_pattern_color, alpha=0.3, boxstyle='round,pad=0.3'))

                    # 如果有关键点信息，标记关键点
                    if 'key_points' in pattern:
                        for point in pattern['key_points']:
                            point_idx = point['idx']
                            if 0 <= point_idx < len(dates):
                                # 绘制关键点
                                ax1.scatter(point_idx, point['price'], marker='o', color='red', s=50, zorder=5)
                                # 添加标注
                                ax1.annotate(point['type'], (point_idx, point['price']),
                                            xytext=(0, 10), textcoords='offset points',
                                            ha='center', va='bottom', fontsize=8,
                                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='red', pad=1))

                    # 连接N字形态的关键点
                    # 如果没有关键点信息，则使用起始和结束点
                    if 'key_points' not in pattern:
                        # 简单地连接起始和结束点
                        x_points = [start_idx, end_idx]
                        y_points = [self.data['closes'][start_idx], self.data['closes'][end_idx]]
                        ax1.plot(x_points, y_points, 'c--', linewidth=1.5, alpha=0.7, label='n_pattern')
                    else:
                        # 连接所有关键点
                        x_points = [point['idx'] for point in pattern['key_points']]
                        y_points = [point['price'] for point in pattern['key_points']]
                        ax1.plot(x_points, y_points, 'c-', linewidth=1.5, alpha=0.7, label='n_pattern')

                except Exception as e:
                    print(f"绘制第 {i+1} 个N字形态时出错: {str(e)}")
                    continue

            # 刷新画布
            self.canvas.draw()

            # 尝试将K线图滚动到第一个N字形态的位置
            try:
                if n_patterns and len(n_patterns) > 0:
                    # 获取第一个N字形态的起始索引
                    start_idx = dates.index(n_patterns[0]['start_dt']) if n_patterns[0]['start_dt'] in dates else n_patterns[0]['start_idx']

                    # 计算可见区域的大小
                    visible_range = ax1.get_xlim()
                    visible_width = visible_range[1] - visible_range[0]

                    # 计算新的可视区域，使N字形态在可视区域的中央
                    new_left = max(0, start_idx - visible_width * 0.3)  # 留出30%的空间
                    new_right = new_left + visible_width

                    # 设置新的可视区域
                    ax1.set_xlim(new_left, new_right)

                    # 刷新画布
                    self.canvas.draw()
            except Exception as e:
                print(f"滚动到N字形态时出错: {str(e)}")

            print("plot_n_patterns: 绘制完成")

        except Exception as e:
            print(f"绘制N字形态时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"绘制N字形态时出错: {str(e)}")

    def find_peak_points(self):
        """计算阶段高低点的峰值"""
        try:
            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 检查是否有数据
            if 'dates' not in self.data or len(self.data['dates']) < 10:  # 至少需要一定数量的K线
                QMessageBox.warning(self, "警告", "数据不足，无法计算阶段高低点")
                return

            # 弹出对话框让用户选择参数
            dialog = QDialog(self)
            dialog.setWindowTitle("阶段高低点参数设置")
            dialog.setMinimumWidth(300)

            layout = QVBoxLayout(dialog)

            # 添加阶段范围设置
            range_layout = QHBoxLayout()
            range_label = QLabel("阶段范围(周):")  # 周线数据使用周作为单位
            range_spin = QSpinBox()
            range_spin.setRange(5, 100)  # 阶段范围从5周到1年左右
            range_spin.setValue(10)  # 默认为10周
            range_spin.setSingleStep(5)
            range_layout.addWidget(range_label)
            range_layout.addWidget(range_spin)
            layout.addLayout(range_layout)

            # 添加高低点数量设置
            count_layout = QHBoxLayout()
            count_label = QLabel("显示高低点数量:")
            count_spin = QSpinBox()
            count_spin.setRange(3, 100)  # 显示3到100个高低点
            count_spin.setValue(25)  # 默认为25个
            count_spin.setSingleStep(5)
            count_layout.addWidget(count_label)
            count_layout.addWidget(count_spin)
            layout.addLayout(count_layout)

            # 添加价格误差范围设置
            error_layout = QHBoxLayout()
            error_label = QLabel("价格误差范围(%):")
            error_spin = QDoubleSpinBox()
            error_spin.setRange(0.1, 10)  # 误差范围从0.1%到10%
            error_spin.setValue(2)  # 默认为2%
            error_spin.setSingleStep(0.1)
            error_spin.setDecimals(1)
            error_layout.addWidget(error_label)
            error_layout.addWidget(error_spin)
            layout.addLayout(error_layout)

            # 添加显示模式选择
            mode_group = QGroupBox("显示模式")
            mode_layout = QVBoxLayout(mode_group)

            # 创建单选按钮
            self.mode_all = QRadioButton("显示高低点和连接线")
            self.mode_points = QRadioButton("只显示高低点")
            self.mode_lines = QRadioButton("只显示连接线")

            # 默认选择第一个选项
            self.mode_all.setChecked(True)

            # 添加到布局
            mode_layout.addWidget(self.mode_all)
            mode_layout.addWidget(self.mode_points)
            mode_layout.addWidget(self.mode_lines)

            layout.addWidget(mode_group)

            # 添加确定和取消按钮
            button_box = QHBoxLayout()
            ok_button = QPushButton("确定")
            cancel_button = QPushButton("取消")
            button_box.addWidget(ok_button)
            button_box.addWidget(cancel_button)
            layout.addLayout(button_box)

            # 连接信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)

            # 显示对话框
            if dialog.exec() != QDialog.DialogCode.Accepted:
                return

            # 获取参数
            stage_range = range_spin.value()
            peak_count = count_spin.value()
            price_error = error_spin.value() / 100.0  # 转换为小数形式

            # 获取显示模式
            display_mode = 'all'  # 默认显示所有
            if self.mode_points.isChecked():
                display_mode = 'points'
            elif self.mode_lines.isChecked():
                display_mode = 'lines'

            # 清除原有的标记
            for artist in ax1.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() in ['peak_high', 'peak_low', 'similar_high_line', 'similar_low_line']:
                    artist.remove()

            # 清除标注
            for artist in ax1.texts:
                if hasattr(artist, 'get_bbox') and artist.get_bbox() is not None:
                    artist.remove()

            # 计算阶段高低点
            highs = []
            lows = []

            # 首先找出所有的局部高低点
            for i in range(stage_range, len(self.data['dates']) - stage_range):
                # 检查是否是阶段高点
                is_high = True
                for j in range(i - stage_range, i + stage_range + 1):
                    if j != i and j >= 0 and j < len(self.data['dates']) and self.data['highs'][j] > self.data['highs'][i]:
                        is_high = False
                        break

                if is_high:
                    highs.append((i, self.data['highs'][i], self.data['dates'][i]))

                # 检查是否是阶段低点
                is_low = True
                for j in range(i - stage_range, i + stage_range + 1):
                    if j != i and j >= 0 and j < len(self.data['dates']) and self.data['lows'][j] < self.data['lows'][i]:
                        is_low = False
                        break

                if is_low:
                    lows.append((i, self.data['lows'][i], self.data['dates'][i]))

            # 按价格排序，高点从高到低，低点从低到高
            highs.sort(key=lambda x: x[1], reverse=True)
            lows.sort(key=lambda x: x[1])

            # 取前peak_count个高低点
            highs = highs[:peak_count]
            lows = lows[:peak_count]

            if not highs and not lows:
                QMessageBox.information(self, "提示", f"未找到阶段高低点（阶段范围: {stage_range}周）")
                return

            # 绘制高点
            print(f"\n找到 {len(highs)} 个阶段高点和 {len(lows)} 个阶段低点（阶段范围: {stage_range}周）")

            # 定义高低点的颜色
            high_color = 'red'
            low_color = 'green'

            # 根据显示模式决定是否绘制高低点
            if display_mode in ['all', 'points']:
                # 绘制高点
                for idx, (i, price, date) in enumerate(highs):
                    # 绘制点
                    ax1.scatter([i], [price], color=high_color, s=100, alpha=0.7, zorder=10,
                               marker='^', edgecolors='black', linewidths=1.5, label='peak_high')

                    # 添加标注 - 不显示时间
                    ax1.annotate(f"H-{idx+1}\n{price:.2f}",
                                (i, price), xytext=(0, 15),
                                textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor=high_color, pad=1),
                                color=high_color)

                # 绘制低点
                for idx, (i, price, date) in enumerate(lows):
                    # 绘制点
                    ax1.scatter([i], [price], color=low_color, s=100, alpha=0.7, zorder=10,
                               marker='v', edgecolors='black', linewidths=1.5, label='peak_low')

                    # 添加标注 - 不显示时间
                    ax1.annotate(f"L-{idx+1}\n{price:.2f}",
                                (i, price), xytext=(0, -15),
                                textcoords='offset points', ha='center', va='top', fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor=low_color, pad=1),
                                color=low_color)

            # 计算价格相近的高低点对
            # 连接价格相近的高点
            similar_high_pairs = []
            for i in range(len(highs)):
                for j in range(i+1, len(highs)):
                    idx1, price1, date1 = highs[i]
                    idx2, price2, date2 = highs[j]
                    # 计算价格差异百分比
                    price_diff_percent = abs(price1 - price2) / max(price1, price2)
                    if price_diff_percent <= price_error:  # 价格差异在误差范围内
                        similar_high_pairs.append((i, j, idx1, price1, date1, idx2, price2, date2))

            # 连接价格相近的低点
            similar_low_pairs = []
            for i in range(len(lows)):
                for j in range(i+1, len(lows)):
                    idx1, price1, date1 = lows[i]
                    idx2, price2, date2 = lows[j]
                    # 计算价格差异百分比
                    price_diff_percent = abs(price1 - price2) / max(price1, price2)
                    if price_diff_percent <= price_error:  # 价格差异在误差范围内
                        similar_low_pairs.append((i, j, idx1, price1, date1, idx2, price2, date2))

            # 根据显示模式决定是否绘制连接线
            if display_mode in ['all', 'lines']:
                # 绘制连接线和时间标注
                for pair_idx, (i, j, idx1, price1, date1, idx2, price2, date2) in enumerate(similar_high_pairs):
                    # 计算时间差（周数）
                    time_diff = abs(idx2 - idx1)

                    # 如果是只显示连接线模式，则显示连接线位置的高点
                    if display_mode == 'lines':
                        # 绘制第一个高点
                        ax1.scatter([idx1], [price1], color=high_color, s=100, alpha=0.7, zorder=10,
                                   marker='^', edgecolors='black', linewidths=1.5, label='peak_high')
                        ax1.annotate(f"H-{i+1}\n{price1:.2f}",
                                    (idx1, price1), xytext=(0, 15),
                                    textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor=high_color, pad=1),
                                    color=high_color)

                        # 绘制第二个高点
                        ax1.scatter([idx2], [price2], color=high_color, s=100, alpha=0.7, zorder=10,
                                   marker='^', edgecolors='black', linewidths=1.5, label='peak_high')
                        ax1.annotate(f"H-{j+1}\n{price2:.2f}",
                                    (idx2, price2), xytext=(0, 15),
                                    textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor=high_color, pad=1),
                                    color=high_color)

                    # 绘制连接线
                    line = ax1.plot([idx1, idx2], [price1, price2], 'r--', linewidth=1.5, alpha=0.7, label='similar_high_line')[0]
                    # 添加时间差标注
                    mid_x = (idx1 + idx2) / 2
                    mid_y = (price1 + price2) / 2
                    ax1.annotate(f"{time_diff}周",
                                (mid_x, mid_y), xytext=(0, 10),
                                textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1),
                                color='red')
                    print(f"高点对 {pair_idx+1}: H-{i+1}({date1.strftime('%Y-%m-%d')}, {price1:.2f}) 和 H-{j+1}({date2.strftime('%Y-%m-%d')}, {price2:.2f}), 时间差: {time_diff}周")

                for pair_idx, (i, j, idx1, price1, date1, idx2, price2, date2) in enumerate(similar_low_pairs):
                    # 计算时间差（周数）
                    time_diff = abs(idx2 - idx1)

                    # 如果是只显示连接线模式，则显示连接线位置的低点
                    if display_mode == 'lines':
                        # 绘制第一个低点
                        ax1.scatter([idx1], [price1], color=low_color, s=100, alpha=0.7, zorder=10,
                                   marker='v', edgecolors='black', linewidths=1.5, label='peak_low')
                        ax1.annotate(f"L-{i+1}\n{price1:.2f}",
                                    (idx1, price1), xytext=(0, -15),
                                    textcoords='offset points', ha='center', va='top', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor=low_color, pad=1),
                                    color=low_color)

                        # 绘制第二个低点
                        ax1.scatter([idx2], [price2], color=low_color, s=100, alpha=0.7, zorder=10,
                                   marker='v', edgecolors='black', linewidths=1.5, label='peak_low')
                        ax1.annotate(f"L-{j+1}\n{price2:.2f}",
                                    (idx2, price2), xytext=(0, -15),
                                    textcoords='offset points', ha='center', va='top', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor=low_color, pad=1),
                                    color=low_color)

                    # 绘制连接线
                    line = ax1.plot([idx1, idx2], [price1, price2], 'g--', linewidth=1.5, alpha=0.7, label='similar_low_line')[0]
                    # 添加时间差标注
                    mid_x = (idx1 + idx2) / 2
                    mid_y = (price1 + price2) / 2
                    ax1.annotate(f"{time_diff}周",
                                (mid_x, mid_y), xytext=(0, -10),
                                textcoords='offset points', ha='center', va='top', fontsize=8,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='green', pad=1),
                                color='green')
                    print(f"低点对 {pair_idx+1}: L-{i+1}({date1.strftime('%Y-%m-%d')}, {price1:.2f}) 和 L-{j+1}({date2.strftime('%Y-%m-%d')}, {price2:.2f}), 时间差: {time_diff}周")
            else:
                # 即使不绘制连接线，也输出日志信息
                for pair_idx, (i, j, idx1, price1, date1, idx2, price2, date2) in enumerate(similar_high_pairs):
                    time_diff = abs(idx2 - idx1)
                    print(f"高点对 {pair_idx+1}: H-{i+1}({date1.strftime('%Y-%m-%d')}, {price1:.2f}) 和 H-{j+1}({date2.strftime('%Y-%m-%d')}, {price2:.2f}), 时间差: {time_diff}周")

                for pair_idx, (i, j, idx1, price1, date1, idx2, price2, date2) in enumerate(similar_low_pairs):
                    time_diff = abs(idx2 - idx1)
                    print(f"低点对 {pair_idx+1}: L-{i+1}({date1.strftime('%Y-%m-%d')}, {price1:.2f}) 和 L-{j+1}({date2.strftime('%Y-%m-%d')}, {price2:.2f}), 时间差: {time_diff}周")

            # 刷新画布
            self.canvas.draw()

            # 显示消息框
            message = f"找到 {len(highs)} 个阶段高点和 {len(lows)} 个阶段低点\n"
            message += f"计算出 {len(similar_high_pairs)} 对价格相近的高点和 {len(similar_low_pairs)} 对价格相近的低点\n\n"

            if display_mode == 'all':
                message += f"当前显示模式: 显示高低点和连接线\n"
                message += f"高点标记为红色三角形，低点标记为绿色倒三角形\n"
                message += f"相近高点用红色虚线连接，相近低点用绿色虚线连接\n"
            elif display_mode == 'points':
                message += f"当前显示模式: 只显示高低点\n"
                message += f"高点标记为红色三角形，低点标记为绿色倒三角形\n"
            elif display_mode == 'lines':
                message += f"当前显示模式: 只显示连接线及其相应的高低点\n"
                message += f"相近高点用红色虚线连接，相近低点用绿色虚线连接\n"
                message += f"只显示与连接线相关的高低点，其他高低点不显示\n"

            message += f"\n阶段范围: {stage_range}周, 价格误差范围: {price_error*100:.1f}%"
            QMessageBox.information(self, "提示", message)

        except Exception as e:
            print(f"计算阶段高低点时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"计算阶段高低点时出错: {str(e)}")

    def show_price_density(self):
        """显示价格分布密度"""
        try:
            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 获取所有价格数据
            all_prices = []
            for i in range(len(self.data['dates'])):
                # 添加开盘价、收盘价、最高价和最低价
                all_prices.extend([self.data['opens'][i], self.data['closes'][i],
                                  self.data['highs'][i], self.data['lows'][i]])

            # 使用KDE（核密度估计）计算价格分布密度
            from scipy.stats import gaussian_kde
            import numpy as np

            # 确保有足够的数据点
            if len(all_prices) < 10:
                QMessageBox.warning(self, "警告", "数据点太少，无法计算价格分布密度")
                return

            # 计算价格范围
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price

            # 创建价格网格
            price_grid = np.linspace(min_price - price_range * 0.05, max_price + price_range * 0.05, 1000)

            # 计算KDE
            kde = gaussian_kde(all_prices)
            density = kde(price_grid)

            # 将密度归一化到[0, 1]区间
            density_normalized = (density - density.min()) / (density.max() - density.min())

            # 定义密度区间和对应的颜色 - 使用橙色的渐变色
            # 按照0.1的间隔进行更细致的分割
            density_levels = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            # 橙色系渐变色，从浅到深，更多的颜色级别
            colors = [
                '#fff5eb', # 最浅的橙色
                '#fff0e0',
                '#fee8d6',
                '#fee0cc',
                '#fdd8c2',
                '#fdc9a8',
                '#fdbb8e',
                '#fdad74',
                '#fd9e5a',
                '#fd8f40',
                '#fd8026', # 中等橙色
                '#f4700c',
                '#e86207',
                '#dc5402',
                '#d04602',
                '#c43802',
                '#b82a02',
                '#ac1c02',
                '#a00e02',
                '#940002'  # 最深的橙色
            ]

            # 清除原有的密度区域标记
            for artist in ax1.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'price_density':
                    artist.remove()

            # 定义高对比度的颜色来区分不同的高密度区域
            highlight_colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00']

            # 绘制价格密度区域
            for i in range(len(density_levels) - 1):
                level_min = density_levels[i]
                level_max = density_levels[i + 1]

                # 选择颜色 - 对于高密度区域使用高对比度颜色
                if level_min >= 0.5:
                    # 计算当前密度区间在高密度区间中的索引
                    high_density_idx = int((level_min - 0.5) * 10)  # 将[0.5, 1.0]映射到[0, 5]
                    high_density_idx = min(high_density_idx, len(highlight_colors) - 1)  # 确保索引不越界
                    color = highlight_colors[high_density_idx]
                else:
                    # 低密度区域使用原来的橙色渐变
                    color = colors[i]

                # 找到当前密度区间内的价格区域
                mask = (density_normalized >= level_min) & (density_normalized < level_max)
                if not any(mask):
                    continue

                # 找到连续的区域
                regions = []
                start_idx = None
                for j in range(len(mask)):
                    if mask[j] and start_idx is None:
                        start_idx = j
                    elif not mask[j] and start_idx is not None:
                        regions.append((start_idx, j - 1))
                        start_idx = None
                if start_idx is not None:
                    regions.append((start_idx, len(mask) - 1))

                # 绘制每个区域
                for start, end in regions:
                    price_start = price_grid[start]
                    price_end = price_grid[end]
                    # 对于高密度区域使用更高的不透明度
                    alpha = 0.5 if level_min >= 0.5 else 0.3
                    rect = plt.Rectangle((ax1.get_xlim()[0], price_start),
                                        ax1.get_xlim()[1] - ax1.get_xlim()[0],
                                        price_end - price_start,
                                        color=color, alpha=alpha, label='price_density')
                    ax1.add_patch(rect)

                    # 在色带上标上价格区间
                    mid_price = (price_start + price_end) / 2
                    mid_x = (ax1.get_xlim()[0] + ax1.get_xlim()[1]) / 2  # 色带的中间位置

                    # 对于所有密度区间，都添加价格标注
                    text_color = 'black' if alpha < 0.5 else 'white'  # 根据背景色的深浅选择文本颜色
                    ax1.annotate(f'{price_start:.2f}-{price_end:.2f}',
                                xy=(mid_x, mid_price),
                                xytext=(0, 0),  # 文本不偏移
                                textcoords='offset points',
                                ha='center', va='center',
                                fontsize=8, fontweight='bold',
                                color=text_color,
                                bbox=dict(facecolor=color, alpha=0.7, edgecolor='none', pad=1))

                    # 对于密度较高的区域（大于0.5），我们已经在色带上添加了标注
                    # 不需要再添加右侧的标注
                    pass

            # 添加图例 - 只显示密度较高的前五个区间
            from matplotlib.patches import Patch as MPatch
            legend_elements = []

            # 选择密度较高的前五个区间
            high_density_levels = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]  # 密度较高的区间

            # 找出每个密度区间对应的价格范围
            price_ranges = {}
            for i in range(len(high_density_levels) - 1):
                level_min = high_density_levels[i]
                level_max = high_density_levels[i + 1]

                # 找到当前密度区间内的价格区域
                mask = (density_normalized >= level_min) & (density_normalized < level_max)
                if any(mask):
                    prices_in_range = price_grid[mask]
                    if len(prices_in_range) > 0:
                        min_price_in_range = prices_in_range.min()
                        max_price_in_range = prices_in_range.max()
                        price_ranges[i] = (min_price_in_range, max_price_in_range)

            # 定义高对比度的颜色来区分不同的高密度区域
            highlight_colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00']

            # 为每个密度区间创建图例
            for i in range(len(high_density_levels) - 1):
                if i in price_ranges:
                    # 计算当前密度区间在高密度区间中的索引
                    high_density_idx = int((high_density_levels[i] - 0.5) * 10)  # 将[0.5, 1.0]映射到[0, 5]
                    high_density_idx = min(high_density_idx, len(highlight_colors) - 1)  # 确保索引不越界

                    min_price, max_price = price_ranges[i]
                    legend_elements.append(MPatch(facecolor=highlight_colors[high_density_idx], alpha=0.5,
                                                label=f'密度 {high_density_levels[i]:.1f}-{high_density_levels[i+1]:.1f} '
                                                      f'价格: {min_price:.2f}-{max_price:.2f}'))

            # 添加图例
            ax1.legend(handles=legend_elements, loc='upper right')

            # 更新标题
            ax1.set_title(f"{ax1.get_title()} - 价格分布密度")

            # 刷新画布
            self.canvas.draw()

            # 显示消息框
            QMessageBox.information(self, "提示", "价格分布密度计算完成\n\n"
                                   "橙色越深表示价格分布密度越高\n"
                                   "这些区域通常是重要的支撑和阻力位")

        except Exception as e:
            print(f"显示价格分布密度时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"显示价格分布密度时出错: {str(e)}")


    def show_all_klines(self):
        """显示全部K线"""
        try:
            # 获取K线图的轴
            ax1 = self.canvas.fig.axes[0]

            # 获取当前显示的K线范围
            current_xlim = ax1.get_xlim()
            current_visible_klines = int(current_xlim[1] - current_xlim[0])

            # 设置轴的范围为全部K线
            # 确保使用完整的数据
            total_klines = len(self.data['dates'])

            # 重绘K线图，显示全部K线
            # 清除原有的图表
            ax1.clear()

            # 重新绘制K线图
            dates = self.data['dates']
            opens = self.data['opens']
            highs = self.data['highs']
            lows = self.data['lows']
            closes = self.data['closes']

            # 绘制K线图
            for i in range(len(dates)):
                # 计算K线的位置和大小
                left = i - 0.4
                right = i + 0.4
                width = right - left

                # 绘制K线体
                if closes[i] >= opens[i]:  # 阶段上涨，绘制红色K线
                    color = 'red'
                    bottom = opens[i]
                    height = closes[i] - opens[i]
                else:  # 阶段下跌，绘制绿色K线
                    color = 'green'
                    bottom = closes[i]
                    height = opens[i] - closes[i]

                # 绘制K线体
                rect = Rectangle((left, bottom), width, height, color=color)
                ax1.add_patch(rect)

                # 绘制上影线和下影线
                ax1.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)

            # 设置轴的范围
            ax1.set_xlim(-1, total_klines)

            # 设置标题
            freq = self.data.get('freq', '')
            ax1.set_title(f"{self.symbol} - {freq}原始K线 - K线数量: {total_klines}")

            # 设置x轴刻度
            # 根据K线数量决定刻度间隔
            tick_interval = max(1, total_klines // 20)  # 最多显示20个刻度
            ax1.set_xticks(range(0, total_klines, tick_interval))
            ax1.set_xticklabels([dates[i].strftime('%Y-%m-%d') for i in range(0, total_klines, tick_interval)], rotation=45)

            # 重新计算y轴范围
            min_price = min(self.data['lows'])
            max_price = max(self.data['highs'])
            padding = (max_price - min_price) * 0.05  # 添加5%的空间
            ax1.set_ylim(min_price - padding, max_price + padding)

            # 更新标题，显示当前显示的K线数量和总数
            title = ax1.get_title()
            if "K线数量" not in title:
                ax1.set_title(f"{title} - K线数量: {total_klines}")

            # 刷新画布
            self.canvas.draw()

            # 显示消息框，提示用户当前显示的K线数量和总数
            QMessageBox.information(self, "提示", f"当前显示: {current_visible_klines} 根K线\n总计: {total_klines} 根K线")
        except Exception as e:
            print(f"显示全部K线时出错: {str(e)}")


# 主窗口类
class MainWindow(QMainWindow):
    def __init__(self):
        super(MainWindow, self).__init__()

        # 设置窗口标题和大小
        self.setWindowTitle("分型和笔的识别与可视化 - PyQt6版本")
        self.setMinimumSize(1200, 800)

        # 初始化变量
        self.file_path = None
        self.symbol = None
        self.bars = None
        self.czsc = None
        self.directory_files = []  # 存储目录中的文件列表

        # 创建菜单栏
        self.create_menu_bar()

        # 创建主窗口布局
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget)

        # 创建左侧控制面板
        self.control_panel = QWidget()
        self.control_layout = QVBoxLayout(self.control_panel)
        self.control_panel.setMaximumWidth(300)

        # 文件选择部分
        self.file_group = QGroupBox("文件选择")
        self.file_layout = QVBoxLayout(self.file_group)

        # 添加按钮行
        button_layout = QHBoxLayout()

        self.file_btn = QPushButton("选择文件")
        self.file_btn.clicked.connect(self.select_file)
        # 设置按钮样式 - 蓝色
        self.file_btn.setStyleSheet(
            "QPushButton {background-color: #4682B4; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #5c9bd1;}"
            "QPushButton:pressed {background-color: #3a6d99;}"
        )

        self.dir_btn = QPushButton("选择目录")
        self.dir_btn.clicked.connect(self.open_directory)
        # 设置按钮样式 - 绿色
        self.dir_btn.setStyleSheet(
            "QPushButton {background-color: #2E8B57; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #3cb371;}"
            "QPushButton:pressed {background-color: #276749;}"
        )

        button_layout.addWidget(self.file_btn)
        button_layout.addWidget(self.dir_btn)
        self.file_layout.addLayout(button_layout)

        self.file_label = QLabel("未选择文件")
        self.file_label.setWordWrap(True)
        self.file_layout.addWidget(self.file_label)

        # 添加文件列表
        self.file_list_label = QLabel("目录文件列表:")
        self.file_layout.addWidget(self.file_list_label)

        self.file_list_widget = QListWidget()
        self.file_list_widget.setMaximumHeight(150)  # 限制高度
        self.file_list_widget.itemClicked.connect(self.on_file_list_item_clicked)
        self.file_layout.addWidget(self.file_list_widget)

        # 参数设置部分
        self.param_group = QGroupBox("参数设置")
        self.param_layout = QFormLayout(self.param_group)

        self.symbol_label = QLabel("标的代码:")
        self.symbol_input = QLabel("未选择文件")

        self.freq_label = QLabel("K线周期:")
        self.freq_combo = QComboBox()
        self.freq_combo.addItems(["日线", "30分钟", "60分钟", "周线", "月线"])

        self.param_layout.addRow(self.symbol_label, self.symbol_input)
        self.param_layout.addRow(self.freq_label, self.freq_combo)

        # 分析按钮
        self.analyze_btn = QPushButton("分析")
        self.analyze_btn.clicked.connect(self.analyze_data)
        self.analyze_btn.setEnabled(False)
        # 设置按钮样式 - 绿色
        self.analyze_btn.setStyleSheet(
            "QPushButton {background-color: #2E8B57; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #3cb371;}"
            "QPushButton:pressed {background-color: #276749;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 保存图表按钮
        self.save_btn = QPushButton("保存图表")
        self.save_btn.clicked.connect(self.save_chart)
        self.save_btn.setEnabled(False)
        # 设置按钮样式 - 深灰色
        self.save_btn.setStyleSheet(
            "QPushButton {background-color: #696969; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #808080;}"
            "QPushButton:pressed {background-color: #505050;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 添加周线分析按钮
        self.weekly_btn = QPushButton("周线分析")
        self.weekly_btn.clicked.connect(self.analyze_weekly_data)
        self.weekly_btn.setEnabled(False)
        # 设置按钮样式 - 橙色
        self.weekly_btn.setStyleSheet(
            "QPushButton {background-color: #FF8C00; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #FFA500;}"
            "QPushButton:pressed {background-color: #D2691E;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 创建六方图组
        self.hexagon_group = QGroupBox("六方图设置")
        self.hexagon_layout = QVBoxLayout(self.hexagon_group)

        # 创建数量选择器
        self.num_points_layout = QHBoxLayout()
        self.num_points_label = QLabel("显示笔数:")
        self.num_points_spinbox = QSpinBox()
        self.num_points_spinbox.setRange(5, 50)  # 设置范围为5到50
        self.num_points_spinbox.setValue(30)  # 默认值为30
        self.num_points_spinbox.setSingleStep(5)  # 每次增减5
        self.num_points_layout.addWidget(self.num_points_label)
        self.num_points_layout.addWidget(self.num_points_spinbox)
        self.hexagon_layout.addLayout(self.num_points_layout)

        # 添加日线六方图按钮
        self.daily_hexagon_btn = QPushButton("日线六方图")
        self.daily_hexagon_btn.clicked.connect(self.show_daily_hexagon)
        self.daily_hexagon_btn.setEnabled(False)
        # 设置按钮样式 - 蓝紫色
        self.daily_hexagon_btn.setStyleSheet(
            "QPushButton {background-color: #6A5ACD; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #7B68EE;}"
            "QPushButton:pressed {background-color: #483D8B;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )
        self.hexagon_layout.addWidget(self.daily_hexagon_btn)

        # 添加周线六方图按钮
        self.weekly_hexagon_btn = QPushButton("周线六方图")
        self.weekly_hexagon_btn.clicked.connect(self.show_weekly_hexagon)
        self.weekly_hexagon_btn.setEnabled(False)
        # 设置按钮样式 - 深青色
        self.weekly_hexagon_btn.setStyleSheet(
            "QPushButton {background-color: #008B8B; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #00CED1;}"
            "QPushButton:pressed {background-color: #006666;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )
        self.hexagon_layout.addWidget(self.weekly_hexagon_btn)

        # 添加放大查看按钮
        self.zoom_group = QGroupBox("放大查看")
        self.zoom_layout = QVBoxLayout(self.zoom_group)  # 改为垂直布局，以便添加更多按钮

        # 放大按钮行
        self.zoom_buttons_row = QHBoxLayout()

        self.zoom_raw_btn = QPushButton("放大原始K线")
        self.zoom_raw_btn.clicked.connect(lambda: self.show_zoomed_chart("raw"))
        self.zoom_raw_btn.setEnabled(False)
        # 设置按钮样式 - 紫色
        self.zoom_raw_btn.setStyleSheet(
            "QPushButton {background-color: #9370DB; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #A080E0;}"
            "QPushButton:pressed {background-color: #7B68EE;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        self.zoom_analyzed_btn = QPushButton("放大分析结果")
        self.zoom_analyzed_btn.clicked.connect(lambda: self.show_zoomed_chart("analyzed"))
        self.zoom_analyzed_btn.setEnabled(False)
        # 设置按钮样式 - 红色
        self.zoom_analyzed_btn.setStyleSheet(
            "QPushButton {background-color: #CD5C5C; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #DC6E6E;}"
            "QPushButton:pressed {background-color: #B22222;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        self.zoom_buttons_row.addWidget(self.zoom_raw_btn)
        self.zoom_buttons_row.addWidget(self.zoom_analyzed_btn)
        self.zoom_layout.addLayout(self.zoom_buttons_row)

        # 显示全部K线按钮行
        self.show_all_buttons_row = QHBoxLayout()

        self.show_all_daily_btn = QPushButton("显示全部日K线")
        self.show_all_daily_btn.clicked.connect(self.show_all_daily_klines)
        self.show_all_daily_btn.setEnabled(False)
        # 设置按钮样式 - 蓝色
        self.show_all_daily_btn.setStyleSheet(
            "QPushButton {background-color: #3498db; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #2980b9;}"
            "QPushButton:pressed {background-color: #1f6aa5;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        self.show_all_weekly_btn = QPushButton("显示全部周K线")
        self.show_all_weekly_btn.clicked.connect(self.show_all_weekly_klines)
        self.show_all_weekly_btn.setEnabled(False)
        # 设置按钮样式 - 橙色
        self.show_all_weekly_btn.setStyleSheet(
            "QPushButton {background-color: #e67e22; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #d35400;}"
            "QPushButton:pressed {background-color: #a04000;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        self.show_all_buttons_row.addWidget(self.show_all_daily_btn)
        self.show_all_buttons_row.addWidget(self.show_all_weekly_btn)
        self.zoom_layout.addLayout(self.show_all_buttons_row)

        # 添加匹配最优均线按钮
        self.match_ma_btn = QPushButton("匹配日线最优均线")
        self.match_ma_btn.clicked.connect(lambda: self.match_optimal_ma(False))
        self.match_ma_btn.setEnabled(False)
        # 设置按钮样式 - 蓝紫色
        self.match_ma_btn.setStyleSheet(
            "QPushButton {background-color: #3498db; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #2980b9;}"
            "QPushButton:pressed {background-color: #1f6aa5;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 添加匹配周线最优均线按钮
        self.match_weekly_ma_btn = QPushButton("匹配周线最优均线")
        self.match_weekly_ma_btn.clicked.connect(lambda: self.match_optimal_ma(True))
        self.match_weekly_ma_btn.setEnabled(False)
        # 设置按钮样式 - 橙色
        self.match_weekly_ma_btn.setStyleSheet(
            "QPushButton {background-color: #e67e22; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #d35400;}"
            "QPushButton:pressed {background-color: #a04000;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 添加查找双底按钮
        self.find_double_bottom_btn = QPushButton("查找双底形态")
        self.find_double_bottom_btn.clicked.connect(self.find_double_bottom)
        self.find_double_bottom_btn.setEnabled(False)
        # 设置按钮样式 - 绿色
        self.find_double_bottom_btn.setStyleSheet(
            "QPushButton {background-color: #2ecc71; color: white; font-weight: bold; padding: 6px; border-radius: 4px;}"
            "QPushButton:hover {background-color: #27ae60;}"
            "QPushButton:pressed {background-color: #1e8449;}"
            "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
        )

        # 添加到控制面板
        self.control_layout.addWidget(self.file_group)
        self.control_layout.addWidget(self.param_group)
        self.control_layout.addWidget(self.analyze_btn)
        self.control_layout.addWidget(self.weekly_btn)
        self.control_layout.addWidget(self.match_ma_btn)
        self.control_layout.addWidget(self.match_weekly_ma_btn)
        self.control_layout.addWidget(self.find_double_bottom_btn)
        self.control_layout.addWidget(self.hexagon_group)  # 添加六方图组
        self.control_layout.addWidget(self.zoom_group)
        self.control_layout.addWidget(self.save_btn)
        self.control_layout.addStretch()

        # 初始化六方图窗口变量
        self.hexagon_window = None
        self.daily_hexagon_window = None
        self.weekly_hexagon_window = None

        # 创建右侧图表区域
        self.chart_widget = QWidget()
        self.chart_layout = QVBoxLayout(self.chart_widget)

        # 创建两个画布，一个显示原始K线，一个显示分析后K线
        self.splitter = QSplitter(Qt.Orientation.Vertical)

        # 原始K线画布
        self.raw_canvas_widget = QWidget()
        self.raw_canvas_layout = QVBoxLayout(self.raw_canvas_widget)
        self.raw_canvas = MplCanvas(width=10, height=4, dpi=100)
        self.raw_toolbar = NavigationToolbar(self.raw_canvas, self)
        self.raw_canvas_layout.addWidget(self.raw_toolbar)
        self.raw_canvas_layout.addWidget(self.raw_canvas)

        # 分析后K线画布
        self.analyzed_canvas_widget = QWidget()
        self.analyzed_canvas_layout = QVBoxLayout(self.analyzed_canvas_widget)
        self.analyzed_canvas = MplCanvas(width=10, height=4, dpi=100)
        self.analyzed_toolbar = NavigationToolbar(self.analyzed_canvas, self)
        self.analyzed_canvas_layout.addWidget(self.analyzed_toolbar)
        self.analyzed_canvas_layout.addWidget(self.analyzed_canvas)

        # 添加到分割器
        self.splitter.addWidget(self.raw_canvas_widget)
        self.splitter.addWidget(self.analyzed_canvas_widget)

        # 添加到图表布局
        self.chart_layout.addWidget(self.splitter)

        # 添加到主布局
        self.main_layout.addWidget(self.control_panel)
        self.main_layout.addWidget(self.chart_widget, 1)  # 1是伸展因子，让图表区域占据更多空间

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪。请选择文件进行分析。")

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 创建文件菜单
        file_menu = menubar.addMenu('文件')

        # 添加打开文件动作
        open_action = QAction('打开文件', self)
        open_action.triggered.connect(self.select_file)
        file_menu.addAction(open_action)

        # 添加打开目录动作
        open_dir_action = QAction('打开目录', self)
        open_dir_action.triggered.connect(self.open_directory)
        file_menu.addAction(open_dir_action)

        # 添加文件列表子菜单
        self.files_menu = file_menu.addMenu('文件列表')
        self.files_menu.setEnabled(False)  # 初始时禁用

        # 添加分隔线
        file_menu.addSeparator()

        # 添加退出动作
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

    def open_directory(self):
        """打开目录并加载所有CSV和TXT文件"""
        directory = QFileDialog.getExistingDirectory(self, "选择目录", "D:\\tdxdata")

        if directory:
            # 清除当前文件列表菜单和文件列表控件
            self.files_menu.clear()
            self.file_list_widget.clear()
            self.directory_files = []

            # 获取目录中的所有CSV和TXT文件
            for file in os.listdir(directory):
                if file.endswith(('.csv', '.txt')):
                    file_path = os.path.join(directory, file)
                    self.directory_files.append(file_path)

            if not self.directory_files:
                QMessageBox.warning(self, "警告", "所选目录中没有CSV或TXT文件")
                return

            # 启用文件列表菜单
            self.files_menu.setEnabled(True)

            # 添加文件到菜单和文件列表控件
            for file_path in self.directory_files:
                file_name = os.path.basename(file_path)

                # 添加到菜单
                action = QAction(file_name, self)
                action.triggered.connect(lambda checked, path=file_path: self.load_file_from_menu(path))
                self.files_menu.addAction(action)

                # 添加到文件列表控件
                self.file_list_widget.addItem(file_name)

            self.status_bar.showMessage(f"已加载目录: {directory} (包含 {len(self.directory_files)} 个文件)")

    def on_file_list_item_clicked(self, item):
        """处理文件列表项的点击事件"""
        file_name = item.text()

        # 找到对应的文件路径
        for file_path in self.directory_files:
            if os.path.basename(file_path) == file_name:
                # 加载文件
                self.load_file_from_menu(file_path)

                # 自动执行分析
                self.analyze_data()
                break

    def load_file_from_menu(self, file_path):
        """从菜单加载文件"""
        self.file_path = file_path
        self.file_label.setText(os.path.basename(file_path))

        # 从文件名提取标的代码
        self.symbol = os.path.basename(file_path).split('.')[0]
        self.symbol_input.setText(self.symbol)

        # 启用分析按钮
        self.analyze_btn.setEnabled(True)

        self.status_bar.showMessage(f"已选择文件: {os.path.basename(file_path)}")

        # 自动执行分析
        self.analyze_data()

    def select_file(self):
        """选择文件对话框"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "选择CSV或TXT文件", "D:\\tdxdata", "CSV/TXT文件 (*.csv *.txt)"
        )

        if file_path:
            self.file_path = file_path
            self.file_label.setText(os.path.basename(file_path))

            # 从文件名提取标的代码
            self.symbol = os.path.basename(file_path).split('.')[0]
            self.symbol_input.setText(self.symbol)

            # 启用分析按钮
            self.analyze_btn.setEnabled(True)

            self.status_bar.showMessage(f"已选择文件: {os.path.basename(file_path)}")

            # 自动执行分析
            self.analyze_data()

    def analyze_data(self):
        """分析数据并绘制图表"""
        if not self.file_path:
            QMessageBox.warning(self, "警告", "请先选择文件！")
            return

        try:
            # 更新状态
            self.status_bar.showMessage("正在读取数据并分析...")
            QApplication.processEvents()  # 刷新UI

            # 读取K线数据
            freq = self.freq_combo.currentText()
            self.bars = read_kline_from_file(self.file_path, self.symbol, freq)

            if not self.bars or len(self.bars) == 0:
                QMessageBox.warning(self, "警告", "没有读取到有效数据！")
                return

            # 创建CZSC对象进行分析
            self.czsc = CZSC(self.bars)

            # 绘制图表
            self.plot_chart()

            # 启用保存按钮、周线分析按钮、六方图按钮、放大查看按钮、匹配最优均线按钮、查找双底按钮和显示全部K线按钮
            self.save_btn.setEnabled(True)
            self.weekly_btn.setEnabled(True)
            self.daily_hexagon_btn.setEnabled(True)
            self.weekly_hexagon_btn.setEnabled(True)
            self.zoom_raw_btn.setEnabled(True)
            self.zoom_analyzed_btn.setEnabled(True)
            self.match_ma_btn.setEnabled(True)
            self.find_double_bottom_btn.setEnabled(True)
            self.show_all_daily_btn.setEnabled(True)

            # 更新状态
            self.status_bar.showMessage(
                f"分析完成。共识别出 {len(self.czsc.fx_list)} 个分型、{len(self.czsc.bi_list)} 笔和 {len(self.czsc.zs_list)} 个中枚"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"分析过程中出现错误\n{str(e)}")
            self.status_bar.showMessage("分析失败")

    def plot_chart(self):
        """在画布上绘制图表"""
        if not self.czsc:
            return

        # 准备数据
        dates = [x.dt for x in self.czsc.bars_raw]
        opens = [x.open for x in self.czsc.bars_raw]
        highs = [x.high for x in self.czsc.bars_raw]
        lows = [x.low for x in self.czsc.bars_raw]
        closes = [x.close for x in self.czsc.bars_raw]
        volumes = [x.vol for x in self.czsc.bars_raw]

        # 绘制原始K线图
        self.plot_raw_kline(dates, opens, highs, lows, closes, volumes)

        # 绘制分析后K线图
        self.plot_analyzed_kline(dates, opens, highs, lows, closes, volumes)

    def plot_raw_kline(self, dates, opens, highs, lows, closes, volumes):
        """绘制原始K线图"""
        # 清除当前图表
        self.raw_canvas.fig.clear()

        # 设置K线图和成交量图的比例
        gs = self.raw_canvas.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = self.raw_canvas.fig.add_subplot(gs[0])
        ax2 = self.raw_canvas.fig.add_subplot(gs[1], sharex=ax1)

        # 绘制K线图
        width = 0.6  # K线宽度
        width2 = 0.3  # 影线宽度 - 增加影线宽度使其更清晰可见

        # 上涨和下跌的颜色
        up_color = 'red'
        down_color = 'green'

        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

        # 绘制成交量图
        for i in range(len(dates)):
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
            else:  # 下跌
                color = down_color
            ax2.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 设置x轴刻度为日期
        ax1.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置图表标题和标签
        ax1.set_title(f"{self.symbol} - {self.freq_combo.currentText()} 原始K线", fontsize=16)
        ax1.set_ylabel('价格', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=12)

        # 设置y轴范围
        ax1.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1.grid(True, linestyle='--', alpha=0.3)

        # 标注日线分析结果中的高低点
        if self.czsc:
            # 准备高点和低点数据
            high_points = []
            low_points = []

            # 获取指定数量的笔的顶底分型
            num_points = self.num_points_spinbox.value()
            bi_list = self.czsc.bi_list[-num_points:] if len(self.czsc.bi_list) > num_points else self.czsc.bi_list

            for bi in bi_list:
                # 添加笔的起始分型
                if bi.fx_a.mark == Mark.G:  # 顶分型
                    high_points.append({
                        'price': bi.fx_a.fx,
                        'date': bi.fx_a.dt
                    })
                else:  # 底分型
                    low_points.append({
                        'price': bi.fx_a.fx,
                        'date': bi.fx_a.dt
                    })

                # 添加笔的结束分型
                if bi.fx_b.mark == Mark.G:  # 顶分型
                    high_points.append({
                        'price': bi.fx_b.fx,
                        'date': bi.fx_b.dt
                    })
                else:  # 底分型
                    low_points.append({
                        'price': bi.fx_b.fx,
                        'date': bi.fx_b.dt
                    })

            # 在原始K线图上标注高点
            for point in high_points:
                # 找到对应的索引
                idx = dates.index(point['date']) if point['date'] in dates else -1
                if idx >= 0:
                    # 使用橙色正方形标记高点 - 调整透明度和位置
                    # 将标记放在高点上方，不遗挡K线
                    ax1.scatter(idx, point['price'] * 1.002, marker='s', color='orange', s=40, zorder=5, alpha=0.5)
                    # 标注价格 - 调整位置和透明度
                    ax1.annotate(f"{point['price']:.2f}", (idx, point['price'] * 1.004), xytext=(0, 2),
                                 textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.6, edgecolor='orange', pad=1))

            # 在原始K线图上标注低点
            for point in low_points:
                # 找到对应的索引
                idx = dates.index(point['date']) if point['date'] in dates else -1
                if idx >= 0:
                    # 使用蓝色正方形标记低点 - 调整透明度和位置
                    # 将标记放在低点下方，不遗挡K线
                    ax1.scatter(idx, point['price'] * 0.998, marker='s', color='blue', s=40, zorder=5, alpha=0.5)
                    # 标注价格 - 调整位置和透明度
                    ax1.annotate(f"{point['price']:.2f}", (idx, point['price'] * 0.996), xytext=(0, -2),
                                 textcoords='offset points', ha='center', va='top', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.6, edgecolor='blue', pad=1))

        # 标注周线分析结果中的高低点
        if hasattr(self, 'weekly_czsc') and self.weekly_czsc is not None:
            # 准备高点和低点数据
            weekly_high_points = []
            weekly_low_points = []

            # 获取指定数量的笔的顶底分型
            num_points = self.num_points_spinbox.value()
            bi_list = self.weekly_czsc.bi_list[-num_points:] if len(self.weekly_czsc.bi_list) > num_points else self.weekly_czsc.bi_list

            for bi in bi_list:
                # 添加笔的起始分型
                if bi.fx_a.mark == Mark.G:  # 顶分型
                    weekly_high_points.append({
                        'price': bi.fx_a.fx,
                        'date': bi.fx_a.dt
                    })
                else:  # 底分型
                    weekly_low_points.append({
                        'price': bi.fx_a.fx,
                        'date': bi.fx_a.dt
                    })

                # 添加笔的结束分型
                if bi.fx_b.mark == Mark.G:  # 顶分型
                    weekly_high_points.append({
                        'price': bi.fx_b.fx,
                        'date': bi.fx_b.dt
                    })
                else:  # 底分型
                    weekly_low_points.append({
                        'price': bi.fx_b.fx,
                        'date': bi.fx_b.dt
                    })

            # 在原始K线图上标注周线高点
            for point in weekly_high_points:
                # 找到对应的索引
                idx = dates.index(point['date']) if point['date'] in dates else -1
                if idx >= 0:
                    # 使用橙色菱形标记周线高点 - 调整透明度和位置
                    # 将标记放在高点上方，不遗挡K线
                    ax1.scatter(idx, point['price'] * 1.003, marker='D', color='orange', s=60, zorder=6, alpha=0.5, edgecolors='black')
                    # 标注价格 - 调整位置和透明度
                    ax1.annotate(f"W{point['price']:.2f}", (idx, point['price'] * 1.006), xytext=(0, 5),
                                 textcoords='offset points', ha='center', va='bottom', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.6, edgecolor='black', pad=1))

            # 在原始K线图上标注周线低点
            for point in weekly_low_points:
                # 找到对应的索引
                idx = dates.index(point['date']) if point['date'] in dates else -1
                if idx >= 0:
                    # 使用蓝色菱形标记周线低点 - 调整透明度和位置
                    # 将标记放在低点下方，不遗挡K线
                    ax1.scatter(idx, point['price'] * 0.997, marker='D', color='blue', s=60, zorder=6, alpha=0.5, edgecolors='black')
                    # 标注价格 - 调整位置和透明度
                    ax1.annotate(f"W{point['price']:.2f}", (idx, point['price'] * 0.994), xytext=(0, -5),
                                 textcoords='offset points', ha='center', va='top', fontsize=8,
                                 bbox=dict(facecolor='white', alpha=0.6, edgecolor='black', pad=1))

        # 添加图例
        from matplotlib.lines import Line2D
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
            Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
            Line2D([0], [0], marker='s', color='w', markerfacecolor='orange', markersize=8, label='日线高点'),
            Line2D([0], [0], marker='s', color='w', markerfacecolor='blue', markersize=8, label='日线低点')
        ]

        # 如果有周线分析结果，添加周线高低点图例
        if hasattr(self, 'weekly_czsc') and self.weekly_czsc is not None:
            legend_elements.extend([
                Line2D([0], [0], marker='D', color='w', markerfacecolor='orange', markeredgecolor='black', markersize=8, label='周线高点'),
                Line2D([0], [0], marker='D', color='w', markerfacecolor='blue', markeredgecolor='black', markersize=8, label='周线低点')
            ])

        ax1.legend(handles=legend_elements, loc='best')

        # 调整布局
        self.raw_canvas.fig.tight_layout()

        # 刷新画布
        self.raw_canvas.draw()

    def plot_analyzed_kline(self, dates, opens, highs, lows, closes, volumes):
        """绘制分析后K线图"""
        # 清除当前图表
        self.analyzed_canvas.fig.clear()

        # 设置K线图和成交量图的比例
        gs = self.analyzed_canvas.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1 = self.analyzed_canvas.fig.add_subplot(gs[0])
        ax2 = self.analyzed_canvas.fig.add_subplot(gs[1], sharex=ax1)

        # 绘制K线图
        width = 0.6  # K线宽度
        width2 = 0.3  # 影线宽度 - 增加影线宽度使其更清晰可见

        # 上涨和下跌的颜色
        up_color = 'red'
        down_color = 'green'

        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

        # 绘制成交量图
        for i in range(len(dates)):
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
            else:  # 下跌
                color = down_color
            ax2.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 标记分型
        for fx in self.czsc.fx_list:
            # 找到对应的索引
            idx = dates.index(fx.dt) if fx.dt in dates else -1
            if idx >= 0:
                if fx.mark == Mark.G:
                    ax1.scatter(idx, fx.fx, marker='^', color='blue', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                    # 标注价格
                    ax1.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, 5),
                                 textcoords='offset points', ha='center', va='bottom', fontsize=8)
                else:
                    ax1.scatter(idx, fx.fx, marker='v', color='purple', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                    # 标注价格
                    ax1.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, -5),
                                 textcoords='offset points', ha='center', va='top', fontsize=8)

        # 绘制笔
        if len(self.czsc.bi_list) > 0:
            bi_points = []
            for bi in self.czsc.bi_list:
                # 找到对应的索引
                idx_a = dates.index(bi.fx_a.dt) if bi.fx_a.dt in dates else -1
                if idx_a >= 0:
                    bi_points.append((idx_a, bi.fx_a.fx))

            # 添加最后一笔的结束点
            idx_b = dates.index(self.czsc.bi_list[-1].fx_b.dt) if self.czsc.bi_list[-1].fx_b.dt in dates else -1
            if idx_b >= 0:
                bi_points.append((idx_b, self.czsc.bi_list[-1].fx_b.fx))

            # 绘制笔的连线
            indices = [x[0] for x in bi_points]
            values = [x[1] for x in bi_points]
            ax1.plot(indices, values, 'y-', linewidth=0.5, zorder=4)  # 进一步减小笔连线的粗细

        # 绘制中枚
        zs_list = self.czsc.zs_list
        if zs_list:
            # 中枚颜色列表，使用淡色系
            zs_colors = ['#FFA07A', '#FFD700', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA', '#FFC0CB']

            for i, zs in enumerate(zs_list):
                # 选择颜色，循环使用颜色列表
                color_idx = i % len(zs_colors)
                zs_color = zs_colors[color_idx]

                # 找到中枚开始和结束的索引
                zs_start_dt = zs.bis[0].fx_a.dt
                zs_end_dt = zs.bis[-1].fx_b.dt
                zs_start_idx = dates.index(zs_start_dt) if zs_start_dt in dates else -1
                zs_end_idx = dates.index(zs_end_dt) if zs_end_dt in dates else -1

                if zs_start_idx >= 0 and zs_end_idx >= 0:
                    # 绘制中枚区域，增加透明度，降低zorder使其不遮挡K线
                    ax1.axhspan(zs.zd, zs.zg, xmin=zs_start_idx/len(dates), xmax=zs_end_idx/len(dates),
                                alpha=0.08, color=zs_color, zorder=0)

                    # 绘制中枚中轴
                    ax1.hlines(zs.zz, zs_start_idx, zs_end_idx, colors=zs_color, linestyles='dashed',
                              linewidth=1, alpha=0.8, zorder=1)

                    # 标注中枚信息
                    ax1.text((zs_start_idx + zs_end_idx) / 2, zs.zz, f"ZS{i+1}",
                             ha='center', va='center', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.6, edgecolor=zs_color, pad=1))

        # 标注三买点和三卖点
        third_buy_points, third_sell_points = identify_third_bs(self.czsc)

        # 标注三买点
        for dt, price in third_buy_points:
            idx = dates.index(dt) if dt in dates else -1
            if idx >= 0:
                # 使用绿色上三角标记三买点
                ax1.scatter(idx, price, marker='^', color='lime', s=80, zorder=6, edgecolors='black')
                # 标注三买文字
                ax1.annotate('三买', (idx, price), xytext=(0, -15),
                             textcoords='offset points', ha='center', va='top', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.8, edgecolor='lime', pad=1))

        # 标注三卖点
        for dt, price in third_sell_points:
            idx = dates.index(dt) if dt in dates else -1
            if idx >= 0:
                # 使用红色下三角标记三卖点
                ax1.scatter(idx, price, marker='v', color='red', s=80, zorder=6, edgecolors='black')
                # 标注三卖文字
                ax1.annotate('三卖', (idx, price), xytext=(0, 15),
                             textcoords='offset points', ha='center', va='bottom', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))

        # 设置x轴刻度为日期
        ax1.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置图表标题和标签
        ax1.set_title(f"{self.symbol} - {self.freq_combo.currentText()} 分析结果", fontsize=16)
        ax1.set_ylabel('价格', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=12)

        # 设置y轴范围
        ax1.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1.grid(True, linestyle='--', alpha=0.3)

        # 添加图例
        from matplotlib.lines import Line2D
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
            Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
            Line2D([0], [0], color='yellow', lw=0.5, label='笔'),  # 进一步减小笔连线的粗细
            Line2D([0], [0], marker='^', color='blue', markersize=3, label='顶分型', linestyle='None'),  # 进一步缩小箭头
            Line2D([0], [0], marker='v', color='purple', markersize=3, label='底分型', linestyle='None'),  # 进一步缩小箭头
            Line2D([0], [0], marker='^', color='lime', markersize=8, label='三买点', linestyle='None', markeredgecolor='black'),  # 添加三买点
            Line2D([0], [0], marker='v', color='red', markersize=8, label='三卖点', linestyle='None', markeredgecolor='black'),  # 添加三卖点
        ]

        # 添加中枚图例
        if zs_list:
            # 为每个中枚添加一个图例项
            for i in range(min(len(zs_list), 5)):  # 最多显示5个中枚图例
                color_idx = i % len(zs_colors)
                zs_color = zs_colors[color_idx]
                legend_elements.append(Patch(facecolor=zs_color, alpha=0.08, label=f'ZS{i+1}'))
        ax1.legend(handles=legend_elements, loc='best')

        # 调整布局
        self.analyzed_canvas.fig.tight_layout()

        # 刷新画布
        self.analyzed_canvas.draw()

    def analyze_weekly_data(self):
        """分析周线数据"""
        if not self.bars:
            QMessageBox.warning(self, "警告", "请先加载数据！")
            return

        try:
            # 更新状态
            self.status_bar.showMessage("正在将日线数据重采样为周线数据...")
            QApplication.processEvents()  # 刷新UI

            # 将日线数据重采样为周线数据
            weekly_bars = resample_to_weekly(self.bars)

            if not weekly_bars or len(weekly_bars) == 0:
                QMessageBox.warning(self, "警告", "重采样后没有有效的周线数据！")
                return

            # 创建CZSC对象进行分析
            self.status_bar.showMessage("正在分析周线数据...")
            QApplication.processEvents()  # 刷新UI

            self.weekly_czsc = CZSC(weekly_bars)

            # 准备数据
            dates = [x.dt for x in self.weekly_czsc.bars_raw]
            opens = [x.open for x in self.weekly_czsc.bars_raw]
            highs = [x.high for x in self.weekly_czsc.bars_raw]
            lows = [x.low for x in self.weekly_czsc.bars_raw]
            closes = [x.close for x in self.weekly_czsc.bars_raw]
            volumes = [x.vol for x in self.weekly_czsc.bars_raw]

            # 绘制周线图表
            self.plot_weekly_chart(self.weekly_czsc, dates, opens, highs, lows, closes, volumes)

            # 启用放大查看按钮、周线最优均线按钮和显示全部周K线按钮
            self.zoom_raw_btn.setEnabled(True)
            self.zoom_analyzed_btn.setEnabled(True)
            self.match_weekly_ma_btn.setEnabled(True)
            self.show_all_weekly_btn.setEnabled(True)

            # 更新状态
            self.status_bar.showMessage(
                f"周线分析完成。共识别出 {len(self.weekly_czsc.fx_list)} 个分型、{len(self.weekly_czsc.bi_list)} 笔和 {len(self.weekly_czsc.zs_list)} 个中枚"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"周线分析过程中出现错误\n{str(e)}")
            self.status_bar.showMessage("周线分析失败")

    def plot_weekly_chart(self, weekly_czsc, dates, opens, highs, lows, closes, volumes):
        """绘制周线图表"""
        # 清除当前图表
        self.raw_canvas.fig.clear()
        self.analyzed_canvas.fig.clear()

        # 绘制原始周线K线图
        gs_raw = self.raw_canvas.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1_raw = self.raw_canvas.fig.add_subplot(gs_raw[0])
        ax2_raw = self.raw_canvas.fig.add_subplot(gs_raw[1], sharex=ax1_raw)

        # 绘制分析后周线K线图
        gs_analyzed = self.analyzed_canvas.fig.add_gridspec(2, 1, height_ratios=[3, 1])
        ax1_analyzed = self.analyzed_canvas.fig.add_subplot(gs_analyzed[0])
        ax2_analyzed = self.analyzed_canvas.fig.add_subplot(gs_analyzed[1], sharex=ax1_analyzed)

        # 绘制K线图参数
        width = 0.6  # K线宽度
        width2 = 0.3  # 影线宽度 - 增加影线宽度使其更清晰可见
        up_color = 'red'
        down_color = 'green'

        # 绘制原始K线图
        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1_raw.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1_raw.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1_raw.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1_raw.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

            # 绘制成交量图
            ax2_raw.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 设置原始K线图标题和标签
        ax1_raw.set_title(f"{self.symbol} - 周线原始K线", fontsize=16)
        ax1_raw.set_ylabel('价格', fontsize=12)
        ax2_raw.set_ylabel('成交量', fontsize=12)

        # 设置x轴刻度为日期
        ax1_raw.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1_raw.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置y轴范围
        ax1_raw.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1_raw.grid(True, linestyle='--', alpha=0.3)

        # 在原始周线K线图上标注高低点
        # 准备高点和低点数据
        high_points = []
        low_points = []

        # 获取指定数量的笔的顶底分型
        num_points = self.num_points_spinbox.value()
        bi_list = weekly_czsc.bi_list[-num_points:] if len(weekly_czsc.bi_list) > num_points else weekly_czsc.bi_list

        for bi in bi_list:
            # 添加笔的起始分型
            if bi.fx_a.mark == Mark.G:  # 顶分型
                high_points.append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })
            else:  # 底分型
                low_points.append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })

            # 添加笔的结束分型
            if bi.fx_b.mark == Mark.G:  # 顶分型
                high_points.append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })
            else:  # 底分型
                low_points.append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })

        # 在原始周线K线图上标注高点
        for point in high_points:
            # 找到对应的索引
            idx = dates.index(point['date']) if point['date'] in dates else -1
            if idx >= 0:
                # 使用橙色正方形标记高点 - 调整透明度和位置
                # 将标记放在高点上方，不遗挡K线
                ax1_raw.scatter(idx, point['price'] * 1.002, marker='s', color='orange', s=40, zorder=5, alpha=0.5)
                # 标注价格 - 调整位置和透明度
                ax1_raw.annotate(f"{point['price']:.2f}", (idx, point['price'] * 1.004), xytext=(0, 2),
                             textcoords='offset points', ha='center', va='bottom', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.6, edgecolor='orange', pad=1))

        # 在原始周线K线图上标注低点
        for point in low_points:
            # 找到对应的索引
            idx = dates.index(point['date']) if point['date'] in dates else -1
            if idx >= 0:
                # 使用蓝色正方形标记低点 - 调整透明度和位置
                # 将标记放在低点下方，不遗挡K线
                ax1_raw.scatter(idx, point['price'] * 0.998, marker='s', color='blue', s=40, zorder=5, alpha=0.5)
                # 标注价格 - 调整位置和透明度
                ax1_raw.annotate(f"{point['price']:.2f}", (idx, point['price'] * 0.996), xytext=(0, -2),
                             textcoords='offset points', ha='center', va='top', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.6, edgecolor='blue', pad=1))

        # 添加图例
        from matplotlib.lines import Line2D
        from matplotlib.patches import Patch
        legend_elements_raw = [
            Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
            Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
            Line2D([0], [0], marker='s', color='w', markerfacecolor='orange', markersize=8, label='周线高点'),
            Line2D([0], [0], marker='s', color='w', markerfacecolor='blue', markersize=8, label='周线低点')
        ]
        ax1_raw.legend(handles=legend_elements_raw, loc='best')

        # 调整布局
        self.raw_canvas.fig.tight_layout()

        # 刷新原始K线图
        self.raw_canvas.draw()

        # 绘制分析后K线图
        for i in range(len(dates)):
            # 绘制K线实体
            if closes[i] >= opens[i]:  # 上涨
                color = up_color
                ax1_analyzed.add_patch(Rectangle((i-width/2, opens[i]), width, closes[i]-opens[i],
                                        fill=True, color=color))
            else:  # 下跌
                color = down_color
                ax1_analyzed.add_patch(Rectangle((i-width/2, closes[i]), width, opens[i]-closes[i],
                                        fill=True, color=color))

            # 绘制上下影线
            ax1_analyzed.plot([i, i], [lows[i], min(opens[i], closes[i])], color=color, linewidth=width2)
            ax1_analyzed.plot([i, i], [max(opens[i], closes[i]), highs[i]], color=color, linewidth=width2)

            # 绘制成交量图
            ax2_analyzed.bar(i, volumes[i], width=0.8, color=color, alpha=0.7)

        # 标记分型
        for fx in weekly_czsc.fx_list:
            # 找到对应的索引
            idx = dates.index(fx.dt) if fx.dt in dates else -1
            if idx >= 0:
                if fx.mark == Mark.G:
                    ax1_analyzed.scatter(idx, fx.fx, marker='^', color='blue', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                    # 标注价格
                    ax1_analyzed.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, 5),
                                 textcoords='offset points', ha='center', va='bottom', fontsize=8)
                else:
                    ax1_analyzed.scatter(idx, fx.fx, marker='v', color='purple', s=10, zorder=5)  # 进一步缩小箭头到原来的1/5
                    # 标注价格
                    ax1_analyzed.annotate(f"{fx.fx:.2f}", (idx, fx.fx), xytext=(0, -5),
                                 textcoords='offset points', ha='center', va='top', fontsize=8)

        # 绘制笔
        if len(weekly_czsc.bi_list) > 0:
            bi_points = []
            for bi in weekly_czsc.bi_list:
                # 找到对应的索引
                idx_a = dates.index(bi.fx_a.dt) if bi.fx_a.dt in dates else -1
                if idx_a >= 0:
                    bi_points.append((idx_a, bi.fx_a.fx))

            # 添加最后一笔的结束点
            idx_b = dates.index(weekly_czsc.bi_list[-1].fx_b.dt) if weekly_czsc.bi_list[-1].fx_b.dt in dates else -1
            if idx_b >= 0:
                bi_points.append((idx_b, weekly_czsc.bi_list[-1].fx_b.fx))

            # 绘制笔的连线
            indices = [x[0] for x in bi_points]
            values = [x[1] for x in bi_points]
            ax1_analyzed.plot(indices, values, 'y-', linewidth=0.5, zorder=4)  # 进一步减小笔连线的粗细

        # 绘制中枚
        zs_list = weekly_czsc.zs_list
        if zs_list:
            # 中枚颜色列表，使用淡色系
            zs_colors = ['#FFA07A', '#FFD700', '#98FB98', '#87CEFA', '#DDA0DD', '#F0E68C', '#E6E6FA', '#FFC0CB']

            for i, zs in enumerate(zs_list):
                # 选择颜色，循环使用颜色列表
                color_idx = i % len(zs_colors)
                zs_color = zs_colors[color_idx]

                # 找到中枚开始和结束的索引
                zs_start_dt = zs.bis[0].fx_a.dt
                zs_end_dt = zs.bis[-1].fx_b.dt
                zs_start_idx = dates.index(zs_start_dt) if zs_start_dt in dates else -1
                zs_end_idx = dates.index(zs_end_dt) if zs_end_dt in dates else -1

                if zs_start_idx >= 0 and zs_end_idx >= 0:
                    # 绘制中枚区域，增加透明度，降低zorder使其不遮挡K线
                    ax1_analyzed.axhspan(zs.zd, zs.zg, xmin=zs_start_idx/len(dates), xmax=zs_end_idx/len(dates),
                                alpha=0.08, color=zs_color, zorder=0)

                    # 绘制中枚中轴
                    ax1_analyzed.hlines(zs.zz, zs_start_idx, zs_end_idx, colors=zs_color, linestyles='dashed',
                              linewidth=1, alpha=0.8, zorder=1)

                    # 标注中枚信息
                    ax1_analyzed.text((zs_start_idx + zs_end_idx) / 2, zs.zz, f"ZS{i+1}",
                             ha='center', va='center', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.6, edgecolor=zs_color, pad=1))

        # 标注三买点和三卖点
        third_buy_points, third_sell_points = identify_third_bs(weekly_czsc)

        # 标注三买点
        for dt, price in third_buy_points:
            idx = dates.index(dt) if dt in dates else -1
            if idx >= 0:
                # 使用绿色上三角标记三买点
                ax1_analyzed.scatter(idx, price, marker='^', color='lime', s=80, zorder=6, edgecolors='black')
                # 标注三买文字
                ax1_analyzed.annotate('三买', (idx, price), xytext=(0, -15),
                             textcoords='offset points', ha='center', va='top', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.8, edgecolor='lime', pad=1))

        # 标注三卖点
        for dt, price in third_sell_points:
            idx = dates.index(dt) if dt in dates else -1
            if idx >= 0:
                # 使用红色下三角标记三卖点
                ax1_analyzed.scatter(idx, price, marker='v', color='red', s=80, zorder=6, edgecolors='black')
                # 标注三卖文字
                ax1_analyzed.annotate('三卖', (idx, price), xytext=(0, 15),
                             textcoords='offset points', ha='center', va='bottom', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))

        # 设置分析后K线图标题和标签
        ax1_analyzed.set_title(f"{self.symbol} - 周线分析结果", fontsize=16)
        ax1_analyzed.set_ylabel('价格', fontsize=12)
        ax2_analyzed.set_ylabel('成交量', fontsize=12)

        # 设置x轴刻度为日期
        ax1_analyzed.set_xticks(range(0, len(dates), max(1, len(dates)//10)))
        ax1_analyzed.set_xticklabels([d.strftime('%Y-%m-%d') for d in dates[::max(1, len(dates)//10)]], rotation=45)

        # 设置y轴范围
        ax1_analyzed.set_ylim(min(lows) * 0.98, max(highs) * 1.02)

        # 添加网格
        ax1_analyzed.grid(True, linestyle='--', alpha=0.3)

        # 添加图例
        legend_elements_analyzed = [
            Patch(facecolor=up_color, edgecolor=up_color, label='上涨'),
            Patch(facecolor=down_color, edgecolor=down_color, label='下跌'),
            Line2D([0], [0], color='yellow', lw=0.5, label='笔'),  # 进一步减小笔连线的粗细
            Line2D([0], [0], marker='^', color='blue', markersize=3, label='顶分型', linestyle='None'),  # 进一步缩小箭头
            Line2D([0], [0], marker='v', color='purple', markersize=3, label='底分型', linestyle='None'),  # 进一步缩小箭头
            Line2D([0], [0], marker='^', color='lime', markersize=8, label='三买点', linestyle='None', markeredgecolor='black'),  # 添加三买点
            Line2D([0], [0], marker='v', color='red', markersize=8, label='三卖点', linestyle='None', markeredgecolor='black'),  # 添加三卖点
        ]

        # 添加中枚图例
        if zs_list:
            # 为每个中枚添加一个图例项
            for i in range(min(len(zs_list), 5)):  # 最多显示5个中枚图例
                color_idx = i % len(zs_colors)
                zs_color = zs_colors[color_idx]
                legend_elements_analyzed.append(Patch(facecolor=zs_color, alpha=0.08, label=f'ZS{i+1}'))
        ax1_analyzed.legend(handles=legend_elements_analyzed, loc='best')

        # 调整布局
        self.analyzed_canvas.fig.tight_layout()

        # 刷新分析后K线图
        self.analyzed_canvas.draw()

    def show_zoomed_chart(self, chart_type):
        """在新窗口中显示放大的图表

        :param chart_type: 图表类型，"raw"表示原始K线图，"analyzed"表示分析后K线图
        """
        if not self.czsc:
            QMessageBox.warning(self, "警告", "请先分析数据！")
            return

        # 检查当前显示的是日线还是周线
        # 如果当前显示的是周线，则从原始画布和分析画布中获取数据
        is_weekly = False
        if hasattr(self, 'weekly_czsc') and self.weekly_czsc is not None:
            # 检查raw_canvas和analyzed_canvas中的数据
            raw_axes = self.raw_canvas.fig.get_axes()
            if raw_axes and len(raw_axes) > 0:
                title = raw_axes[0].get_title()
                if '周线' in title:
                    is_weekly = True

        if is_weekly:
            # 使用周线数据
            # 使用完整的周线数据，而不是可能经过筛选的self.weekly_czsc.bars_raw
            weekly_bars = resample_to_weekly(self.bars)
            dates = [x.dt for x in weekly_bars]
            opens = [x.open for x in weekly_bars]
            highs = [x.high for x in weekly_bars]
            lows = [x.low for x in weekly_bars]
            closes = [x.close for x in weekly_bars]
            volumes = [x.vol for x in weekly_bars]

            data = {
                'symbol': self.symbol,
                'freq': '周线',
                'dates': dates,
                'opens': opens,
                'highs': highs,
                'lows': lows,
                'closes': closes,
                'volumes': volumes
            }

            # 添加czsc对象，无论是原始K线图还是分析后的图表
            data['czsc'] = self.weekly_czsc

            # 添加双底形态信息，如果有的话
            # 检查原始K线图的轴中是否有双底形态标记
            double_bottoms = []
            for artist in self.raw_canvas.fig.axes[0].get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'double_bottom':
                    double_bottoms.append(True)
                    break

            if double_bottoms:
                # 如果有双底形态标记，则将其添加到数据中
                data['has_double_bottoms'] = True
                data['use_weekly'] = True

                # 尝试获取双底形态数据
                try:
                    # 如果有双底形态数据，则将其添加到数据中
                    if hasattr(self, 'double_bottoms_data'):
                        data['double_bottoms'] = self.double_bottoms_data
                except Exception as e:
                    print(f"获取双底形态数据时出错: {str(e)}")

            # 创建并显示放大图表窗口
            self.zoomed_window = ZoomedChartWindow(chart_type, data, self.symbol, '周线', self)
            self.zoomed_window.show()
        else:
            # 使用日线数据
            # 使用完整的日线数据，而不是可能经过筛选的self.czsc.bars_raw
            dates = [x.dt for x in self.bars]
            opens = [x.open for x in self.bars]
            highs = [x.high for x in self.bars]
            lows = [x.low for x in self.bars]
            closes = [x.close for x in self.bars]
            volumes = [x.vol for x in self.bars]

            data = {
                'symbol': self.symbol,
                'freq': self.freq_combo.currentText(),
                'dates': dates,
                'opens': opens,
                'highs': highs,
                'lows': lows,
                'closes': closes,
                'volumes': volumes
            }

            # 添加czsc对象，无论是原始K线图还是分析后的图表
            data['czsc'] = self.czsc

            # 添加双底形态信息，如果有的话
            # 检查原始K线图的轴中是否有双底形态标记
            double_bottoms = []
            for artist in self.raw_canvas.fig.axes[0].get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'double_bottom':
                    double_bottoms.append(True)
                    break

            if double_bottoms:
                # 如果有双底形态标记，则将其添加到数据中
                data['has_double_bottoms'] = True
                data['use_weekly'] = False

                # 尝试获取双底形态数据
                try:
                    # 如果有双底形态数据，则将其添加到数据中
                    if hasattr(self, 'double_bottoms_data'):
                        data['double_bottoms'] = self.double_bottoms_data
                except Exception as e:
                    print(f"获取双底形态数据时出错: {str(e)}")

            # 创建并显示放大图表窗口
            self.zoomed_window = ZoomedChartWindow(chart_type, data, self.symbol, self.freq_combo.currentText(), self)
            self.zoomed_window.show()

    def save_chart(self):
        """保存图表为图片文件"""
        if not self.czsc:
            QMessageBox.warning(self, "警告", "请先分析数据！")
            return

        # 选择保存路径
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

        # 弹出选择对话框，选择保存哪个图表
        options = ["原始K线图", "分析后K线图", "两个图表都保存"]
        selected_option, ok = QInputDialog.getItem(
            self, "选择保存选项", "请选择要保存的图表:", options, 0, False
        )

        if not ok:
            return

        # 保存原始K线图
        if selected_option == "原始K线图" or selected_option == "两个图表都保存":
            default_name = f"{self.symbol}_{self.freq_combo.currentText()}_原始_{timestamp}.png"
            file_dialog = QFileDialog()
            raw_save_path, _ = file_dialog.getSaveFileName(
                self, "保存原始K线图", default_name, "图片文件 (*.png *.jpg)"
            )

            if raw_save_path:
                self.raw_canvas.fig.savefig(raw_save_path, dpi=100)
                self.status_bar.showMessage(f"原始K线图已保存至: {raw_save_path}")

        # 保存分析后K线图
        if selected_option == "分析后K线图" or selected_option == "两个图表都保存":
            default_name = f"{self.symbol}_{self.freq_combo.currentText()}_分析_{timestamp}.png"
            file_dialog = QFileDialog()
            analyzed_save_path, _ = file_dialog.getSaveFileName(
                self, "保存分析后K线图", default_name, "图片文件 (*.png *.jpg)"
            )

            if analyzed_save_path:
                self.analyzed_canvas.fig.savefig(analyzed_save_path, dpi=100)
                self.status_bar.showMessage(f"分析后K线图已保存至: {analyzed_save_path}")

        QMessageBox.information(self, "成功", "图表保存成功!")

    def show_daily_hexagon(self):
        """显示日线六方图"""
        if not self.czsc:
            QMessageBox.warning(self, "警告", "请先分析数据！")
            return

        # 获取指定数量的笔的顶底分型
        num_points = self.num_points_spinbox.value()  # 从数量选择器获取值
        bi_list = self.czsc.bi_list[-num_points:] if len(self.czsc.bi_list) > num_points else self.czsc.bi_list

        # 准备高点和低点数据
        manual_points = {'high': [], 'low': []}

        for bi in bi_list:
            # 添加笔的起始分型
            if bi.fx_a.mark == Mark.G:  # 顶分型
                manual_points['high'].append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })
            else:  # 底分型
                manual_points['low'].append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })

            # 添加笔的结束分型
            if bi.fx_b.mark == Mark.G:  # 顶分型
                manual_points['high'].append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })
            else:  # 底分型
                manual_points['low'].append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })

        # 创建六方图窗口
        self.create_hexagon_chart("日线六方图", manual_points)

    def find_optimal_ma_periods_with_offset(self, target_type='both', max_points=10, use_weekly=False):
        """寻找最优的均线周期组合和起始点偏移量

        :param target_type: 匹配目标类型，'both'表示同时匹配高点和低点，'high'只匹配高点，'low'只匹配低点
        :param max_points: 最大匹配点数量，默认为10
        :param use_weekly: 是否使用周线数据
        :return: (最优短期均线周期, 最优长期均线周期, 最优起始点偏移量, 匹配的交叉点列表, 所有交叉点列表)
        """
        # 确保已经分析了数据
        if use_weekly:
            if not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None:
                return 20, 60, 0, [], []  # 如果没有周线分析数据，返回默认值
            czsc_obj = self.weekly_czsc
        elif not self.czsc:
            return 20, 60, 0, [], []  # 如果没有分析数据，返回默认值
        else:
            czsc_obj = self.czsc

        try:
            # 准备数据
            dates = [x.dt for x in czsc_obj.bars_raw]
            closes = [x.close for x in czsc_obj.bars_raw]

            # 创建DataFrame
            df = pd.DataFrame({'Close': closes}, index=dates)

            # 收集所有标记点的日期和价格
            marked_points = []

            # 获取指定数量的笔的顶底分型
            bi_list = czsc_obj.bi_list

            # 根据目标类型收集标记点
            if target_type == 'both' or target_type == 'high':
                for bi in bi_list:
                    if bi.fx_a.mark == Mark.G:  # 顶分型
                        marked_points.append({
                            'date': bi.fx_a.dt,
                            'price': bi.fx_a.fx,
                            'type': 'high'
                        })
                    if bi.fx_b.mark == Mark.G:  # 顶分型
                        marked_points.append({
                            'date': bi.fx_b.dt,
                            'price': bi.fx_b.fx,
                            'type': 'high'
                        })

            if target_type == 'both' or target_type == 'low':
                for bi in bi_list:
                    if bi.fx_a.mark == Mark.D:  # 底分型
                        marked_points.append({
                            'date': bi.fx_a.dt,
                            'price': bi.fx_a.fx,
                            'type': 'low'
                        })
                    if bi.fx_b.mark == Mark.D:  # 底分型
                        marked_points.append({
                            'date': bi.fx_b.dt,
                            'price': bi.fx_b.fx,
                            'type': 'low'
                        })

            if not marked_points:
                return 20, 60, 0, []  # 如果没有符合条件的标记点，返回默认值

            # 按日期排序，取最近的max_points个点
            marked_points.sort(key=lambda x: x['date'], reverse=True)
            marked_points = marked_points[:max_points]

            # 提取日期列表供后续使用
            marked_dates = [point['date'] for point in marked_points]

            best_score = -1
            best_periods = (20, 60)
            best_offset = 0
            best_crosses = []
            best_all_crosses = []

            # 测试不同的起始点偏移量
            max_offset = min(30, len(df) // 4)  # 最大偏移量不超过数据长度的1/4或30

            for offset in range(0, max_offset + 1):
                # 测试不同的均线组合
                for ma1 in range(5, 51, 1):  # 5到50，步长1
                    for ma2 in range(51, 201, 5):  # 51到200，步长5，减少计算量
                        # 计算均线，考虑偏移量
                        if offset > 0:
                            # 从偏移点开始计算均线
                            shifted_data = df['Close'].shift(-offset)
                            ma1_series = shifted_data.rolling(window=ma1).mean().shift(offset)
                            ma2_series = shifted_data.rolling(window=ma2).mean().shift(offset)
                        else:
                            # 正常计算均线
                            ma1_series = df['Close'].rolling(window=ma1).mean()
                            ma2_series = df['Close'].rolling(window=ma2).mean()

                        # 找出均线交叉点
                        crosses = []
                        for i in range(1, len(df)):
                            if pd.isna(ma1_series.iloc[i]) or pd.isna(ma2_series.iloc[i]) or \
                               pd.isna(ma1_series.iloc[i-1]) or pd.isna(ma2_series.iloc[i-1]):
                                continue

                            # 金叉（短期均线从下向上穿过长期均线）
                            if (ma1_series.iloc[i - 1] <= ma2_series.iloc[i - 1] and
                                ma1_series.iloc[i] > ma2_series.iloc[i]):
                                crosses.append({
                                    'date': df.index[i],
                                    'price': df['Close'].iloc[i],
                                    'type': 'golden'
                                })
                            # 死叉（短期均线从上向下穿过长期均线）
                            elif (ma1_series.iloc[i - 1] >= ma2_series.iloc[i - 1] and
                                  ma1_series.iloc[i] < ma2_series.iloc[i]):
                                crosses.append({
                                    'date': df.index[i],
                                    'price': df['Close'].iloc[i],
                                    'type': 'death'
                                })

                        # 计算交叉点与标记点的匹配程度
                        score = 0
                        matched_crosses = []

                        # 记录所有交叉点，不仅仅是匹配的交叉点
                        all_crosses = crosses.copy()

                        for cross in crosses:
                            for marked in marked_points:
                                # 如果交叉点在标记点5个交易日内，增加分数
                                if abs((cross['date'] - marked['date']).days) <= 5:
                                    # 金叉应该匹配底分型，死叉应该匹配顶分型
                                    if (cross['type'] == 'golden' and marked['type'] == 'low') or \
                                       (cross['type'] == 'death' and marked['type'] == 'high'):
                                        score += 1
                                        matched_crosses.append(cross)
                                        break

                        if score > best_score:
                            best_score = score
                            best_periods = (ma1, ma2)
                            best_offset = offset
                            best_crosses = matched_crosses
                            best_all_crosses = all_crosses

            print(f"找到最优均线组合: MA{best_periods[0]}, MA{best_periods[1]}, 偏移量: {best_offset}, 匹配分数: {best_score}")
            return best_periods[0], best_periods[1], best_offset, best_crosses, best_all_crosses

        except Exception as e:
            print(f"寻找最优均线失败: {str(e)}")
            return 20, 60, 0, [], []  # 发生错误时返回默认值

    def plot_raw_kline_with_ma(self, ma1_period, ma2_period, offset=0, crosses=None):
        """在原始K线图上绘制均线

        :param ma1_period: 短期均线周期
        :param ma2_period: 长期均线周期
        :param offset: 均线计算起始点偏移量
        :param crosses: 均线交叉点列表
        """
        if not self.czsc:
            return

        # 准备数据
        dates = [x.dt for x in self.czsc.bars_raw]
        closes = [x.close for x in self.czsc.bars_raw]

        # 创建DataFrame
        df = pd.DataFrame({'Close': closes}, index=dates)

        # 计算均线，考虑偏移量
        if offset > 0:
            # 从偏移点开始计算均线
            shifted_data = df['Close'].shift(-offset)
            ma1 = shifted_data.rolling(window=ma1_period).mean().shift(offset)
            ma2 = shifted_data.rolling(window=ma2_period).mean().shift(offset)
        else:
            # 正常计算均线
            ma1 = df['Close'].rolling(window=ma1_period).mean()
            ma2 = df['Close'].rolling(window=ma2_period).mean()

        # 获取原始K线图的轴
        ax1 = self.raw_canvas.fig.axes[0]

        # 清除原有的均线和标记
        for line in ax1.lines:
            line.remove()

        # 绘制均线
        ax1.plot(range(len(ma1)), ma1, label=f'MA{ma1_period}' + (f' (偏移: {offset})' if offset > 0 else ''), color='blue', linewidth=1)
        ax1.plot(range(len(ma2)), ma2, label=f'MA{ma2_period}' + (f' (偏移: {offset})' if offset > 0 else ''), color='orange', linewidth=1)

        # 标记均线计算的起始点 - 无论偏移量是否为零，都显示起始点
        # 如果偏移量为零，则起始点就是第一个K线
        start_idx = offset if offset > 0 else 0
        # 确保起始点在有效范围内
        if 0 <= start_idx < len(dates):
            # 绘制起始点的垂直线
            ax1.axvline(x=start_idx, color='purple', linestyle='--', alpha=0.7, linewidth=1)
            # 标注起始点
            ax1.annotate('均线起点', xy=(start_idx, df['Close'].iloc[start_idx]),
                         xytext=(start_idx + 5, df['Close'].iloc[start_idx] * 1.05),
                         arrowprops=dict(facecolor='purple', shrink=0.05, width=1.5, headwidth=8),
                         fontsize=10, color='purple')

        # 标记均线交叉点
        if crosses:
            for cross in crosses:
                # 找到交叉点在日期列表中的索引
                if cross['date'] in dates:
                    idx = dates.index(cross['date'])
                    # 金叉用红色，死叉用绿色
                    color = 'red' if cross['type'] == 'golden' else 'green'
                    # 绘制交叉点的垂直线
                    ax1.axvline(x=idx, color=color, linestyle='-', alpha=0.5, linewidth=1)
                    # 标记交叉点
                    cross_type = '金叉' if cross['type'] == 'golden' else '死叉'
                    ax1.scatter(idx, cross['price'], marker='o', color=color, s=50, zorder=5)
                    ax1.annotate(f'{cross_type}', xy=(idx, cross['price']),
                                 xytext=(idx, cross['price'] * 1.03),
                                 ha='center', fontsize=8, color=color)

        # 更新图例
        ax1.legend()

        # 刷新画布
        self.raw_canvas.draw()

    def match_optimal_ma(self, use_weekly=False):
        """匹配最优均线

        :param use_weekly: 是否使用周线数据
        """
        if use_weekly:
            if not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None:
                QMessageBox.warning(self, "警告", "请先进行周线分析！")
                return
        elif not self.czsc:
            QMessageBox.warning(self, "警告", "请先分析数据！")
            return

        # 弹出对话框让用户选择匹配目标类型
        items = ["同时匹配高点和低点", "只匹配高点", "只匹配低点"]
        item, ok = QInputDialog.getItem(self, "选择匹配目标", "请选择均线匹配的目标类型:", items, 0, False)

        if not ok:
            return

        target_type = 'both'
        if item == "只匹配高点":
            target_type = 'high'
        elif item == "只匹配低点":
            target_type = 'low'

        # 弹出对话框让用户输入要匹配的点数
        max_points, ok = QInputDialog.getInt(self, "输入匹配点数", "请输入要匹配的最近点数(默认10):", 10, 1, 30, 1)

        if not ok:
            max_points = 10  # 默认值

        # 更新状态
        self.status_bar.showMessage("正在寻找最优均线组合...")
        QApplication.processEvents()  # 刷新UI

        # 寻找最优均线组合
        ma1_period, ma2_period, offset, matched_crosses, all_crosses = self.find_optimal_ma_periods_with_offset(target_type, max_points, use_weekly)

        # 在原始K线图上绘制均线和交叉点
        # 使用所有交叉点，而不仅仅是匹配的交叉点
        if use_weekly:
            # 在周线K线图上绘制均线和交叉点
            self.plot_weekly_kline_with_ma(ma1_period, ma2_period, offset, all_crosses)
        else:
            # 在日线K线图上绘制均线和交叉点
            self.plot_raw_kline_with_ma(ma1_period, ma2_period, offset, all_crosses)

        # 更新状态
        matched_count = len(matched_crosses)
        all_count = len(all_crosses)
        self.status_bar.showMessage(f"找到最优均线组合: MA{ma1_period}, MA{ma2_period}, 偏移量: {offset}, 匹配交叉点数: {matched_count}/{all_count}")

    def plot_weekly_kline_with_ma(self, ma1_period, ma2_period, offset=0, crosses=None):
        """在周线K线图上绘制均线

        :param ma1_period: 短期均线周期
        :param ma2_period: 长期均线周期
        :param offset: 均线计算起始点偏移量
        :param crosses: 均线交叉点列表
        """
        if not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None:
            return

        # 准备数据
        dates = [x.dt for x in self.weekly_czsc.bars_raw]
        closes = [x.close for x in self.weekly_czsc.bars_raw]

        # 创建DataFrame
        df = pd.DataFrame({'Close': closes}, index=dates)

        # 计算均线，考虑偏移量
        if offset > 0:
            # 从偏移点开始计算均线
            shifted_data = df['Close'].shift(-offset)
            ma1 = shifted_data.rolling(window=ma1_period).mean().shift(offset)
            ma2 = shifted_data.rolling(window=ma2_period).mean().shift(offset)
        else:
            # 正常计算均线
            ma1 = df['Close'].rolling(window=ma1_period).mean()
            ma2 = df['Close'].rolling(window=ma2_period).mean()

        # 获取周线K线图的轴
        ax1_raw = self.raw_canvas.fig.axes[0]

        # 清除原有的均线和标记
        for line in ax1_raw.lines:
            line.remove()

        # 绘制均线
        ax1_raw.plot(range(len(ma1)), ma1, label=f'MA{ma1_period}' + (f' (偏移: {offset})' if offset > 0 else ''), color='blue', linewidth=1)
        ax1_raw.plot(range(len(ma2)), ma2, label=f'MA{ma2_period}' + (f' (偏移: {offset})' if offset > 0 else ''), color='orange', linewidth=1)

        # 标记均线计算的起始点 - 无论偏移量是否为零，都显示起始点
        # 如果偏移量为零，则起始点就是第一个K线
        start_idx = offset if offset > 0 else 0
        # 确保起始点在有效范围内
        if 0 <= start_idx < len(dates):
            # 绘制起始点的垂直线
            ax1_raw.axvline(x=start_idx, color='purple', linestyle='--', alpha=0.7, linewidth=1)
            # 标注起始点
            ax1_raw.annotate('均线起点', xy=(start_idx, df['Close'].iloc[start_idx]),
                         xytext=(start_idx + 5, df['Close'].iloc[start_idx] * 1.05),
                         arrowprops=dict(facecolor='purple', shrink=0.05, width=1.5, headwidth=8),
                         fontsize=10, color='purple')

        # 标记均线交叉点
        if crosses:
            for cross in crosses:
                # 找到交叉点在日期列表中的索引
                if cross['date'] in dates:
                    idx = dates.index(cross['date'])
                    # 金叉用红色，死叉用绿色
                    color = 'red' if cross['type'] == 'golden' else 'green'
                    # 绘制交叉点的垂直线
                    ax1_raw.axvline(x=idx, color=color, linestyle='-', alpha=0.5, linewidth=1)
                    # 标记交叉点
                    cross_type = '金叉' if cross['type'] == 'golden' else '死叉'
                    ax1_raw.scatter(idx, cross['price'], marker='o', color=color, s=50, zorder=5)
                    ax1_raw.annotate(f'{cross_type}', xy=(idx, cross['price']),
                                 xytext=(idx, cross['price'] * 1.03),
                                 ha='center', fontsize=8, color=color)

        # 更新图例
        ax1_raw.legend()

        # 刷新画布
        self.raw_canvas.draw()

    def find_double_bottom(self):
        """查找双底形态"""
        try:
            if not self.czsc:
                QMessageBox.warning(self, "警告", "请先分析数据！")
                return

            # 创建一个简单的消息框，而不是复杂的对话框
            # 这样可以减少可能的错误来源
            use_weekly = QMessageBox.question(self, "选择数据类型", "是否使用周线数据？",
                                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes

            # 检查周线数据是否可用
            if use_weekly and (not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None):
                QMessageBox.warning(self, "警告", "请先进行周线分析！")
                return

            # 弹出对话框让用户选择参数
            # 使用更宽松的参数，以便找到更多的双底形态
            min_distance = 10  # 降低最小间隔，以便找到更多的双底形态
            max_distance = 100  # 增加最大间隔，以便找到更多的双底形态
            price_tolerance = 0.05  # 增加价格容差，以便找到更多的双底形态
            volume_threshold = 1.0  # 降低成交量阈值，以便找到更多的双底形态

            # 更新状态
            self.status_bar.showMessage("正在查找双底形态...")
            QApplication.processEvents()  # 刷新UI

            # 查找双底形态
            try:
                if use_weekly:
                    print("使用周线数据查找双底形态")
                    bars = self.weekly_czsc.bars_raw
                else:
                    print("使用日线数据查找双底形态")
                    bars = self.czsc.bars_raw

                # 检查bars是否为空
                if not bars or len(bars) == 0:
                    QMessageBox.warning(self, "警告", "数据为空，无法查找双底形态")
                    return

                print(f"开始查找双底形态，数据长度: {len(bars)}")
                double_bottoms = identify_double_bottom(bars, min_distance, max_distance, price_tolerance, volume_threshold)
                print(f"找到 {len(double_bottoms)} 个双底形态")
            except Exception as e:
                print(f"查找双底形态时出错: {str(e)}")
                QMessageBox.warning(self, "警告", f"查找双底形态时出错: {str(e)}")
                return

            if not double_bottoms:
                QMessageBox.information(self, "提示", "未找到符合条件的双底形态")
                self.status_bar.showMessage("未找到双底形态")
                return

            # 在图表上标记双底形态
            try:
                print("开始绘制双底形态")
                self.plot_double_bottoms(double_bottoms, use_weekly)
                print("绘制双底形态完成")
            except Exception as e:
                print(f"绘制双底形态时出错: {str(e)}")
                QMessageBox.warning(self, "警告", f"绘制双底形态时出错: {str(e)}")
                return

            # 更新状态栏，显示每个双底形态的位置信息
            status_msg = f"找到 {len(double_bottoms)} 个双底形态: "
            for i, db in enumerate(double_bottoms):
                first_idx = db['first_bottom']['index']
                second_idx = db['second_bottom']['index']
                status_msg += f"#{i+1}[第一底索引:{first_idx}, 第二底索引:{second_idx}] "
            self.status_bar.showMessage(status_msg)
        except Exception as e:
            print(f"查找双底形态功能出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"查找双底形态功能出错: {str(e)}")

    def show_all_daily_klines(self):
        """显示全部日K线"""
        try:
            if not self.czsc:
                QMessageBox.warning(self, "警告", "请先分析数据！")
                return

            # 获取日K线图的轴
            ax1 = self.raw_canvas.fig.axes[0]

            # 获取当前显示的K线范围
            current_xlim = ax1.get_xlim()
            current_visible_klines = int(current_xlim[1] - current_xlim[0])

            # 设置轴的范围为全部K线
            # 使用self.bars而不是self.czsc.bars_raw，因为self.bars包含所有原始K线数据
            total_klines = len(self.bars)

            # 重绘K线图，显示全部K线
            # 清除原有的图表
            ax1.clear()

            # 重新绘制K线图
            dates = [x.dt for x in self.bars]
            opens = [x.open for x in self.bars]
            highs = [x.high for x in self.bars]
            lows = [x.low for x in self.bars]
            closes = [x.close for x in self.bars]
            volumes = [x.vol for x in self.bars]

            # 绘制K线图
            for i in range(len(dates)):
                # 计算K线的位置和大小
                left = i - 0.4
                right = i + 0.4
                width = right - left

                # 绘制K线体
                if closes[i] >= opens[i]:  # 阶段上涨，绘制红色K线
                    color = 'red'
                    bottom = opens[i]
                    height = closes[i] - opens[i]
                else:  # 阶段下跌，绘制绿色K线
                    color = 'green'
                    bottom = closes[i]
                    height = opens[i] - closes[i]

                # 绘制K线体
                rect = Rectangle((left, bottom), width, height, color=color)
                ax1.add_patch(rect)

                # 绘制上影线和下影线
                ax1.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)

            # 设置轴的范围
            ax1.set_xlim(-1, total_klines)

            # 设置标题
            ax1.set_title(f"{self.symbol} - 日线原始K线 - K线数量: {total_klines}")

            # 设置x轴刻度
            # 根据K线数量决定刻度间隔
            tick_interval = max(1, total_klines // 20)  # 最多显示20个刻度
            ax1.set_xticks(range(0, total_klines, tick_interval))
            ax1.set_xticklabels([dates[i].strftime('%Y-%m-%d') for i in range(0, total_klines, tick_interval)], rotation=45)

            # 重新计算y轴范围
            # 使用self.bars而不是self.czsc.bars_raw，因为self.bars包含所有原始K线数据
            min_price = min([x.low for x in self.bars])
            max_price = max([x.high for x in self.bars])
            padding = (max_price - min_price) * 0.05  # 添加5%的空间
            ax1.set_ylim(min_price - padding, max_price + padding)

            # 更新标题，显示当前显示的K线数量和总数
            title = ax1.get_title()
            if "K线数量" not in title:
                ax1.set_title(f"{title} - K线数量: {total_klines}")

            # 刷新画布
            self.raw_canvas.draw()

            # 显示消息框，提示用户当前显示的K线数量和总数
            QMessageBox.information(self, "提示", f"当前显示: {current_visible_klines} 根日K线\n总计: {total_klines} 根日K线")
        except Exception as e:
            print(f"显示全部日K线时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"显示全部日K线时出错: {str(e)}")

    def show_all_weekly_klines(self):
        """显示全部周K线"""
        try:
            if not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None:
                QMessageBox.warning(self, "警告", "请先进行周线分析！")
                return

            # 获取周K线图的轴
            ax1 = self.raw_canvas.fig.axes[0]

            # 获取当前显示的K线范围
            current_xlim = ax1.get_xlim()
            current_visible_klines = int(current_xlim[1] - current_xlim[0])

            # 设置轴的范围为全部K线
            # 使用实际的周线K线数量
            # 在analyze_weekly_data方法中，我们使用resample_to_weekly函数将日线数据重采样为周线数据
            # 这个函数返回的周线数据应该是完整的
            weekly_bars = resample_to_weekly(self.bars)
            total_klines = len(weekly_bars)

            # 重绘K线图，显示全部K线
            # 清除原有的图表
            ax1.clear()

            # 重新绘制K线图
            dates = [x.dt for x in weekly_bars]
            opens = [x.open for x in weekly_bars]
            highs = [x.high for x in weekly_bars]
            lows = [x.low for x in weekly_bars]
            closes = [x.close for x in weekly_bars]
            volumes = [x.vol for x in weekly_bars]

            # 绘制K线图
            for i in range(len(dates)):
                # 计算K线的位置和大小
                left = i - 0.4
                right = i + 0.4
                width = right - left

                # 绘制K线体
                if closes[i] >= opens[i]:  # 阶段上涨，绘制红色K线
                    color = 'red'
                    bottom = opens[i]
                    height = closes[i] - opens[i]
                else:  # 阶段下跌，绘制绿色K线
                    color = 'green'
                    bottom = closes[i]
                    height = opens[i] - closes[i]

                # 绘制K线体
                rect = Rectangle((left, bottom), width, height, color=color)
                ax1.add_patch(rect)

                # 绘制上影线和下影线
                ax1.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)

            # 设置轴的范围
            ax1.set_xlim(-1, total_klines)

            # 设置标题
            ax1.set_title(f"{self.symbol} - 周线原始K线 - K线数量: {total_klines}")

            # 设置x轴刻度
            # 根据K线数量决定刻度间隔
            tick_interval = max(1, total_klines // 20)  # 最多显示20个刻度
            ax1.set_xticks(range(0, total_klines, tick_interval))
            ax1.set_xticklabels([dates[i].strftime('%Y-%m-%d') for i in range(0, total_klines, tick_interval)], rotation=45)

            # 重新计算y轴范围
            # 使用重采样的周线数据
            min_price = min([x.low for x in weekly_bars])
            max_price = max([x.high for x in weekly_bars])
            padding = (max_price - min_price) * 0.05  # 添加5%的空间
            ax1.set_ylim(min_price - padding, max_price + padding)

            # 更新标题，显示当前显示的K线数量和总数
            title = ax1.get_title()
            if "K线数量" not in title:
                ax1.set_title(f"{title} - K线数量: {total_klines}")

            # 刷新画布
            self.raw_canvas.draw()

            # 显示消息框，提示用户当前显示的K线数量和总数
            QMessageBox.information(self, "提示", f"当前显示: {current_visible_klines} 根周K线\n总计: {total_klines} 根周K线")
        except Exception as e:
            print(f"显示全部周K线时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"显示全部周K线时出错: {str(e)}")

    def plot_double_bottoms(self, double_bottoms, use_weekly=False):
        """在图表上标记双底形态

        :param double_bottoms: 双底形态列表
        :param use_weekly: 是否使用周线数据
        """
        try:
            print("plot_double_bottoms: 开始绘制双底形态")

            # 保存双底形态数据，以便在放大图中使用
            self.double_bottoms_data = double_bottoms

            # 首先显示一个消息框，显示每个双底形态的详细信息
            detail_msg = f"找到 {len(double_bottoms)} 个双底形态\n\n"
            for i, db in enumerate(double_bottoms):
                detail_msg += f"\u53cc\u5e95 #{i+1}:\n"
                detail_msg += f"  \u7b2c\u4e00\u5e95: \u7d22\u5f15={db['first_bottom']['index']}, \u4ef7\u683c={db['first_bottom']['price']:.2f}\n"
                detail_msg += f"  \u9888\u7ebf: \u7d22\u5f15={db['neckline']['index']}, \u4ef7\u683c={db['neckline']['price']:.2f}\n"
                detail_msg += f"  \u7b2c\u4e8c\u5e95: \u7d22\u5f15={db['second_bottom']['index']}, \u4ef7\u683c={db['second_bottom']['price']:.2f}\n"
                detail_msg += f"  \u7a81\u7834\u70b9: \u7d22\u5f15={db['breakout']['index']}, \u4ef7\u683c={db['breakout']['price']:.2f}\n\n"

            # 创建一个带有文本框的对话框，以显示详细信息
            dialog = QDialog(self)
            dialog.setWindowTitle("双底形态详细信息")
            dialog.setMinimumWidth(400)
            dialog.setMinimumHeight(300)

            layout = QVBoxLayout(dialog)
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)
            text_edit.setText(detail_msg)
            layout.addWidget(text_edit)

            button_box = QHBoxLayout()
            ok_button = QPushButton("确定")
            ok_button.clicked.connect(dialog.accept)
            button_box.addWidget(ok_button)
            layout.addLayout(button_box)

            dialog.exec()

            if not double_bottoms:  # 如果没有双底形态，直接返回
                return

            # 获取适当的图表轴
            if use_weekly:
                # 获取周线K线图的轴
                ax = self.raw_canvas.fig.axes[0]
                total_bars = len(self.weekly_czsc.bars_raw) if hasattr(self, 'weekly_czsc') else 100
            else:
                # 获取日线K线图的轴
                ax = self.raw_canvas.fig.axes[0]
                total_bars = len(self.czsc.bars_raw) if hasattr(self, 'czsc') else 100

            # 清除原有的双底标记
            for artist in ax.get_children():
                if hasattr(artist, 'get_label') and artist.get_label() == 'double_bottom':
                    artist.remove()

            # 标记双底形态
            for i, db in enumerate(double_bottoms):
                try:
                    # 第一个底
                    ax.scatter(db['first_bottom']['index'], db['first_bottom']['price'],
                               marker='o', color='blue', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax.annotate(f"B1-{i+1}", (db['first_bottom']['index'], db['first_bottom']['price']),
                                xytext=(0, -15), textcoords='offset points', ha='center', va='top', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='blue', pad=1))

                    # 颈线
                    ax.scatter(db['neckline']['index'], db['neckline']['price'],
                               marker='^', color='purple', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax.annotate(f"N-{i+1}", (db['neckline']['index'], db['neckline']['price']),
                                xytext=(0, 10), textcoords='offset points', ha='center', va='bottom', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='purple', pad=1))

                    # 第二个底
                    ax.scatter(db['second_bottom']['index'], db['second_bottom']['price'],
                               marker='o', color='blue', s=100, zorder=10, alpha=0.7, label='double_bottom')
                    ax.annotate(f"B2-{i+1}", (db['second_bottom']['index'], db['second_bottom']['price']),
                                xytext=(0, -15), textcoords='offset points', ha='center', va='top', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='blue', pad=1))

                    # 突破点
                    ax.scatter(db['breakout']['index'], db['breakout']['price'],
                               marker='*', color='red', s=150, zorder=10, alpha=0.7, label='double_bottom')
                    ax.annotate(f"BR-{i+1}", (db['breakout']['index'], db['breakout']['price']),
                                xytext=(0, 10), textcoords='offset points', ha='center', va='bottom', fontsize=10,
                                bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))

                    # 连接线
                    ax.plot([db['first_bottom']['index'], db['neckline']['index'], db['second_bottom']['index'], db['breakout']['index']],
                            [db['first_bottom']['price'], db['neckline']['price'], db['second_bottom']['price'], db['breakout']['price']],
                            'g--', linewidth=1.5, alpha=0.7, label='double_bottom')

                    # 标记颈线 - 使用安全的方式计算xmin和xmax
                    xmin = max(0, min(1, db['first_bottom']['index'] / total_bars))
                    xmax = max(0, min(1, db['breakout']['index'] / total_bars))
                    ax.axhline(y=db['neckline']['price'], xmin=xmin, xmax=xmax,
                               color='purple', linestyle='--', alpha=0.5, label='double_bottom')

                    # 添加成交量信息 - 安全地计算成交量增加倍数
                    if 'avg_volume_before' in db['breakout'] and db['breakout']['avg_volume_before'] > 0:
                        volume_increase = db['breakout']['volume'] / db['breakout']['avg_volume_before']
                        ax.annotate(f"Vol: {volume_increase:.1f}x", (db['breakout']['index'], db['breakout']['price']),
                                    xytext=(10, 0), textcoords='offset points', ha='left', va='center', fontsize=8,
                                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='red', pad=1))
                except Exception as e:
                    print(f"绘制双底形态 {i+1} 时出错: {str(e)}")
                    continue

            try:
                # 添加图例
                from matplotlib.lines import Line2D
                handles, labels = ax.get_legend_handles_labels()
                unique_labels = []
                unique_handles = []
                for handle, label in zip(handles, labels):
                    if label not in unique_labels or label != 'double_bottom':
                        unique_labels.append(label)
                        unique_handles.append(handle)

                # 添加双底图例
                unique_handles.append(Line2D([0], [0], color='g', linestyle='--', label='双底形态'))
                unique_labels.append('双底形态')

                ax.legend(unique_handles, unique_labels)
            except Exception as e:
                print(f"添加图例时出错: {str(e)}")

            # 刷新画布
            self.raw_canvas.draw()

            # 尝试将K线图滚动到第一个双底形态的位置
            try:
                if double_bottoms and len(double_bottoms) > 0:
                    # 获取第一个双底形态的第一个底的索引
                    first_bottom_idx = double_bottoms[0]['first_bottom']['index']

                    # 计算可见区域的大小
                    ax = self.raw_canvas.fig.axes[0]
                    visible_range = ax.get_xlim()
                    visible_width = visible_range[1] - visible_range[0]

                    # 计算新的可视区域，使第一个底在可视区域的左侧一定距离处
                    new_left = max(0, first_bottom_idx - visible_width * 0.2)  # 留出20%的空间
                    new_right = new_left + visible_width

                    # 设置新的可视区域
                    ax.set_xlim(new_left, new_right)

                    # 刷新画布
                    self.raw_canvas.draw()
            except Exception as e:
                print(f"滚动到双底形态时出错: {str(e)}")

            print("plot_double_bottoms: 绘制完成")

        except Exception as e:
            print(f"绘制双底形态时出错: {str(e)}")
            QMessageBox.warning(self, "警告", f"绘制双底形态时出错: {str(e)}")

    def show_weekly_hexagon(self):
        """显示周线六方图"""
        if not hasattr(self, 'weekly_czsc') or self.weekly_czsc is None:
            QMessageBox.warning(self, "警告", "请先进行周线分析！")
            return

        # 获取指定数量的笔的顶底分型
        num_points = self.num_points_spinbox.value()  # 从数量选择器获取值
        bi_list = self.weekly_czsc.bi_list[-num_points:] if len(self.weekly_czsc.bi_list) > num_points else self.weekly_czsc.bi_list

        # 准备高点和低点数据
        manual_points = {'high': [], 'low': []}

        for bi in bi_list:
            # 添加笔的起始分型
            if bi.fx_a.mark == Mark.G:  # 顶分型
                manual_points['high'].append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })
            else:  # 底分型
                manual_points['low'].append({
                    'price': bi.fx_a.fx,
                    'date': bi.fx_a.dt
                })

            # 添加笔的结束分型
            if bi.fx_b.mark == Mark.G:  # 顶分型
                manual_points['high'].append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })
            else:  # 底分型
                manual_points['low'].append({
                    'price': bi.fx_b.fx,
                    'date': bi.fx_b.dt
                })

        # 创建六方图窗口
        self.create_hexagon_chart("周线六方图", manual_points)

    def create_hexagon_chart(self, title, manual_points):
        """创建六方图窗口

        Args:
            title: 窗口标题
            manual_points: 包含高点和低点的字典
        """
        try:
            # 创建新窗口显示六方图
            hexagon_window = QMainWindow(self)  # 使用QMainWindow而非QDialog
            num_points = self.num_points_spinbox.value()
            hexagon_window.setWindowTitle(f"{title} - 显示{num_points}笔")
            hexagon_window.setGeometry(200, 200, 800, 800)

            # 创建中心部件
            central_widget = QWidget()
            hexagon_window.setCentralWidget(central_widget)

            # 设置窗口大小策略，使其能够随窗口大小变化而自动调整
            hexagon_window.setMinimumSize(600, 600)  # 设置最小大小
            hexagon_window.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)  # 设置大小策略为可扩展

            layout = QVBoxLayout(central_widget)

            # 创建工具栏
            toolbar = QToolBar()
            hexagon_window.addToolBar(toolbar)

            # 添加放大、缩小和重置按钮
            zoom_in_action = QAction("放大", hexagon_window)
            zoom_out_action = QAction("缩小", hexagon_window)
            reset_view_action = QAction("重置视图", hexagon_window)

            # 设置工具提示
            zoom_in_action.setToolTip("放大图形")
            zoom_out_action.setToolTip("缩小图形")
            reset_view_action.setToolTip("重置图形到原始大小")

            toolbar.addAction(zoom_in_action)
            toolbar.addAction(zoom_out_action)
            toolbar.addAction(reset_view_action)

            # 创建标注说明区域
            info_text = QTextEdit()
            info_text.setMaximumHeight(100)
            info_text.setReadOnly(True)

            # 收集所有点的价格并转换为六方图数字
            high_numbers = []
            high_point_numbers = set()  # 高点数字集合
            for point in manual_points['high']:
                price = point['price']
                hex_number = int(price * 10)  # 转换为六方图数字
                high_numbers.append(f"高点 {price:.2f} -> {hex_number}")
                high_point_numbers.add(hex_number)  # 添加到高点集合

            low_numbers = []
            low_point_numbers = set()  # 低点数字集合
            for point in manual_points['low']:
                price = point['price']
                hex_number = int(price * 10)  # 转换为六方图数字
                low_numbers.append(f"低点 {price:.2f} -> {hex_number}")
                low_point_numbers.add(hex_number)  # 添加到低点集合

            # 设置标注文本
            num_points = self.num_points_spinbox.value()
            info_text.setText(f"当前显示笔数: {num_points}\n" + "\n".join(high_numbers + low_numbers))
            layout.addWidget(info_text)

            # 创建六方图画布 - 使用更小的初始大小
            canvas = FigureCanvas(Figure(figsize=(8, 8)))

            # 设置画布的大小策略，使其能够随窗口大小变化而自动调整
            canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            canvas.updateGeometry()

            # 创建滚动区域，将画布放入其中
            scroll_area = QScrollArea()
            scroll_area.setWidget(canvas)
            scroll_area.setWidgetResizable(True)  # 允许小部件调整大小
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)  # 需要时显示垂直滚动条
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)  # 需要时显示水平滚动条

            # 添加Matplotlib导航工具栏，提供更好的缩放和平移功能
            nav_toolbar = NavigationToolbar(canvas, hexagon_window)
            layout.addWidget(nav_toolbar)
            layout.addWidget(scroll_area, 1)  # 添加滚动区域，而不是直接添加画布

            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            hexagon_path = os.path.join(current_dir, "六方图.py")

            # 导入六方图模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("六方图", hexagon_path)
            hexagon_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(hexagon_module)

            # 使用六方图的配置
            ax = canvas.figure.add_subplot(111, polar=True)

            # 设置图形的自适应属性，确保它能正确显示整个图形
            ax.set_adjustable('box')
            ax.set_aspect('equal')

            # 设置30度间隔的刻度
            angles_30deg = np.arange(0, 360, 30)
            ax.set_xticks(np.deg2rad(angles_30deg))
            ax.set_xticklabels([f'{int(deg)}°' for deg in angles_30deg])

            # 从六方图模块获取数据
            all_circles = hexagon_module.all_circles
            digits = hexagon_module.digits
            highlight_digits_red = hexagon_module.highlight_digits_red.union(high_point_numbers)  # 合并高亮数字
            highlight_angles_red = hexagon_module.highlight_angles_red
            highlight_angles_green = hexagon_module.highlight_angles_green

            # 绘制每个数字的位置
            for d in digits:
                # 判断是否在红色高亮方向
                is_highlight_red = d[0] in highlight_digits_red or \
                                any(math.isclose(d[1] % 360, angle, abs_tol=1.0)
                                    for angle in highlight_angles_red)

                # 判断是否在绿色高亮方向
                is_highlight_green = any(math.isclose(d[1] % 360, angle, abs_tol=1.0)
                                    for angle in highlight_angles_green)

                # 判断是否是高点或低点
                is_high_point = d[0] in high_point_numbers
                is_low_point = d[0] in low_point_numbers

                # 设置颜色和字体属性
                # 设置文本颜色
                if is_highlight_red:
                    text_color = 'red'
                elif is_highlight_green:
                    text_color = 'green'
                else:
                    text_color = 'black'

                font_weight = 'bold' if is_highlight_red or is_highlight_green else 'normal'

                # 在数字位置绘制小白点
                ax.scatter(np.radians(d[1]), d[2], s=30, color='white')

                # 如果是高点，在数字下方绘制橙色正方形
                if is_high_point:
                    ax.scatter(np.radians(d[1]), d[2], s=100, marker='s', color='orange', alpha=0.5, zorder=5)
                # 如果是低点，在数字下方绘制蓝色正方形
                elif is_low_point:
                    ax.scatter(np.radians(d[1]), d[2], s=100, marker='s', color='blue', alpha=0.5, zorder=5)

                # 绘制数字
                ax.annotate(f"{d[0]}",
                            xy=(np.radians(d[1]), d[2] + 0.8),
                            fontsize=8,
                            color=text_color,
                            weight=font_weight,
                            ha='center',
                            va='center',
                            zorder=10)  # 确保数字显示在最上层

            # 设置标题和隐藏径向标签
            ax.set_title(f"{title} - 主要方向数字高亮显示", pad=20)
            ax.set_yticklabels([])

            # 设置放大和缩小按钮的响应函数
            def do_zoom_in():
                # 实现放大功能
                # 获取当前图形大小
                current_size = canvas.figure.get_size_inches()

                # 放大图形 - 使用更温和的放大比例1.1
                new_width = current_size[0] * 1.1
                new_height = current_size[1] * 1.1
                new_size = (new_width, new_height)

                # 设置新大小
                canvas.figure.set_size_inches(new_size)

                # 更新画布大小
                new_width_pixels = int(new_width * canvas.figure.dpi)
                new_height_pixels = int(new_height * canvas.figure.dpi)
                canvas.setFixedSize(new_width_pixels, new_height_pixels)

                # 获取当前轴的范围
                ax = canvas.figure.axes[0]
                r_min, r_max = ax.get_ylim()

                # 重置视图范围
                ax.set_ylim(r_min, r_max)

                # 重新绘制
                canvas.draw()

                # 更新滚动区域
                scroll_area.updateGeometry()

                print("\u653e大图形，新大小:", new_size)

            def do_zoom_out():
                # 实现缩小功能
                # 获取当前图形大小
                current_size = canvas.figure.get_size_inches()

                # 缩小图形 - 除以1.5表示更明显的缩小
                new_width = current_size[0] / 1.5
                new_height = current_size[1] / 1.5
                new_size = (new_width, new_height)

                # 设置新大小
                canvas.figure.set_size_inches(new_size)

                # 更新画布大小
                new_width_pixels = int(new_width * canvas.figure.dpi)
                new_height_pixels = int(new_height * canvas.figure.dpi)
                canvas.setFixedSize(new_width_pixels, new_height_pixels)

                # 获取当前轴的范围
                ax = canvas.figure.axes[0]
                r_min, r_max = ax.get_ylim()

                # 重置视图范围
                ax.set_ylim(r_min, r_max)

                # 重新绘制
                canvas.draw()

                # 更新滚动区域
                scroll_area.updateGeometry()

                print("\u7f29小图形，新大小:", new_size)

            # 重置视图函数
            def reset_view():
                # 重置图形大小为更小的初始大小
                canvas.figure.set_size_inches(8, 8)

                # 更新画布大小
                canvas.setFixedSize(int(8 * canvas.figure.dpi), int(8 * canvas.figure.dpi))

                # 重置轴的范围
                ax = canvas.figure.axes[0]

                # 对于极坐标图，我们需要重置半径范围
                # 这里使用一个足够大的半径范围，以显示所有数字
                ax.set_ylim(0, 120)  # 这个值可能需要根据实际情况调整

                # 重新绘制
                canvas.draw()

                # 仅在重置时调用自动缩放，以确保显示所有内容
                ax.autoscale_view(tight=False, scalex=True, scaley=True)
                canvas.draw()

                # 更新滚动区域
                scroll_area.updateGeometry()

            # 连接信号和槽
            zoom_in_action.triggered.connect(do_zoom_in)  # 使用新的放大函数
            zoom_out_action.triggered.connect(do_zoom_out)  # 使用新的缩小函数
            reset_view_action.triggered.connect(reset_view)

            # 显示窗口
            hexagon_window.show()
            canvas.draw()

            # 初始化时调用一次重置视图函数，确保初始视图正确
            reset_view()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建六方图时出错: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
