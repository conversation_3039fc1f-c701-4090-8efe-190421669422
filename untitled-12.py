import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
from mplfinance.original_flavor import candlestick_ohlc
import numpy as np
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QFileDialog,
    QLineEdit, QLabel, QListWidget, QMessageBox
)
from PyQt6.QtCore import Qt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar
import cv2


class KLineApp(QWidget):
    def __init__(self):
        super().__init__()
        self.csv_paths = []
        self.all_marks = []
        self.model = None
        self.current_marks = []
        self.figure = plt.figure()
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)
        self.template_image = None
        self.initUI()

    def initUI(self):
        # 布局
        main_layout = QVBoxLayout()

        # 文件选择部分
        file_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        file_layout.addWidget(QLabel("选择 CSV 或 TXT 文件:"))
        file_layout.addWidget(self.file_input)
        select_button = QPushButton("选择文件")
        select_button.clicked.connect(self.select_file)
        file_layout.addWidget(select_button)
        main_layout.addLayout(file_layout)

        # 标注列表
        self.mark_list = QListWidget()
        main_layout.addWidget(QLabel("已标注文件列表:"))
        main_layout.addWidget(self.mark_list)

        # 生成 K 线图按钮
        plot_button = QPushButton("生成 K 线图")
        plot_button.clicked.connect(self.plot_candlestick)
        main_layout.addWidget(plot_button)

        # 开始标注按钮
        start_mark_button = QPushButton("开始标注")
        start_mark_button.clicked.connect(self.start_mark)
        main_layout.addWidget(start_mark_button)

        # 构建模型按钮
        build_model_button = QPushButton("构建形态模型")
        build_model_button.clicked.connect(self.build_model)
        main_layout.addWidget(build_model_button)

        # 识别相似形态按钮
        recognize_button = QPushButton("识别相似形态")
        recognize_button.clicked.connect(self.recognize_pattern)
        main_layout.addWidget(recognize_button)

        # 图表显示部分
        main_layout.addWidget(self.toolbar)
        main_layout.addWidget(self.canvas)

        self.setLayout(main_layout)
        self.setWindowTitle('K 线图形态识别')
        self.setGeometry(100, 100, 1200, 800)
        self.show()

    def select_file(self):
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if file_path:
            self.file_input.setText(file_path)

    def read_file(self, file_path):
        try:
            if file_path.endswith('.csv'):
                return pd.read_csv(file_path)
            elif file_path.endswith('.txt'):
                return pd.read_csv(file_path, sep=',')
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")
        return None

    def plot_candlestick(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            print("请选择有效的文件路径")
            QMessageBox.warning(self, "警告", "请选择有效的文件路径")
            return

        df = self.read_file(file_path)
        if df is None:
            return

        # 映射小写列名到大写
        column_mapping = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close'
        }

        # 验证数据列
        required_columns = list(column_mapping.values())
        if not all(col in df.columns for col in required_columns):
            print(f"文件缺少必要列: {', '.join(required_columns)}")
            QMessageBox.critical(self, "错误", f"文件缺少必要列: {', '.join(required_columns)}")
            return

        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)

            # 使用连续整数索引作为 X 轴
            df['index'] = range(len(df))
            ohlc = df[['index', column_mapping['Open'], column_mapping['High'], column_mapping['Low'], column_mapping['Close']]]

            candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')

            # 设置 X 轴刻度为日期
            ax.set_xticks(df['index'][::len(df) // 10])
            ax.set_xticklabels(df[column_mapping['Date']][::len(df) // 10], rotation=45)

            self.canvas.draw()
        except Exception as e:
            print(f"绘制 K 线图失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"绘制 K 线图失败: {str(e)}")

    def start_mark(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        # 映射小写列名到大写
        column_mapping = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close'
        }

        # 添加 index 列
        df['index'] = range(len(df))

        self.current_marks = []
        self.figure.canvas.mpl_connect('button_press_event', lambda event: self.on_click(event, df))

    def on_click(self, event, df):
        if event.inaxes is None:
            return

        if len(self.current_marks) >= 5:
            return

        x = event.xdata
        nearest_idx = np.abs(df['index'] - x).argmin()

        # 映射小写列名到大写
        column_mapping = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close'
        }

        if len(self.current_marks) % 2 == 0:
            y = df[column_mapping['Low']][nearest_idx]
            # 低点标注，箭头朝下
            arrowprops = dict(facecolor='black', arrowstyle='->, head_length=0.6, head_width=0.4', connectionstyle="arc3,rad=0")
        else:
            y = df[column_mapping['High']][nearest_idx]
            # 高点标注，箭头朝上
            arrowprops = dict(facecolor='black', arrowstyle='<-, head_length=0.6, head_width=0.4', connectionstyle="arc3,rad=0")

        self.current_marks.append(nearest_idx)
        ax = self.figure.gca()
        ax.annotate(f'{len(self.current_marks)}', xy=(x, y), xytext=(x, y + 0.05),
                    arrowprops=arrowprops)
        self.canvas.draw()

        if len(self.current_marks) == 5:
            # 绘制连接 5 个点的线段
            x_coords = [df['index'][idx] for idx in self.current_marks]
            y_coords = []
            for i, idx in enumerate(self.current_marks):
                if i % 2 == 0:
                    y_coords.append(df[column_mapping['Low']][idx])
                else:
                    y_coords.append(df[column_mapping['High']][idx])
            ax.plot(x_coords, y_coords, 'b-')
            self.canvas.draw()

            self.csv_paths.append(self.file_input.text())
            self.all_marks.append(self.current_marks)
            self.mark_list.addItem(os.path.basename(self.file_input.text()))

            # 生成模板图像
            self.template_image = self.generate_template_image(df, x_coords, y_coords)

    def generate_template_image(self, df, x_coords, y_coords):
        fig = plt.figure(figsize=(len(x_coords), 5))
        ax = fig.add_subplot(111)
        ax.plot(x_coords, y_coords, 'b-')
        fig.canvas.draw()
        image = np.frombuffer(fig.canvas.buffer_rgba(), dtype=np.uint8)
        image = image.reshape(fig.canvas.get_width_height()[::-1] + (4,))
        image = cv2.cvtColor(image, cv2.COLOR_RGBA2GRAY)
        plt.close(fig)
        return image

    def build_model(self):
        if self.template_image is None:
            QMessageBox.warning(self, "警告", "请先完成标注并生成模板")
            return
        QMessageBox.information(self, "提示", "形态模板已准备好")

    def recognize_pattern(self):
        if self.template_image is None:
            QMessageBox.warning(self, "警告", "请先完成标注并生成模板")
            return

        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择要识别的 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        # 映射小写列名到大写
        column_mapping = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close'
        }

        # 生成待识别图像
        fig = plt.figure(figsize=(len(df), 5))
        ax = fig.add_subplot(111)
        df['index'] = range(len(df))
        ohlc = df[['index', column_mapping['Open'], column_mapping['High'], column_mapping['Low'], column_mapping['Close']]]
        candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')
        fig.canvas.draw()
        image = np.frombuffer(fig.canvas.buffer_rgba(), dtype=np.uint8)
        image = image.reshape(fig.canvas.get_width_height()[::-1] + (4,))
        image = cv2.cvtColor(image, cv2.COLOR_RGBA2GRAY)
        plt.close(fig)

        # 模板匹配
        result = cv2.matchTemplate(image, self.template_image, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        best_idx = max_loc[0]

        print(f"相似形态起始索引: {best_idx}")
        QMessageBox.information(self, "提示", f"相似形态起始索引: {best_idx}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = KLineApp()
    sys.exit(app.exec())
