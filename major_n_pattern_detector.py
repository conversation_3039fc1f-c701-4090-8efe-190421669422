#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大级别N字形态检测器
专门识别跨越较长时间的主要趋势N字形态
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from typing import List, Dict, Tuple

class MajorNPatternDetector:
    """大级别N字形态检测器"""

    def __init__(self):
        pass

    def load_csv_data(self, csv_path: str) -> Tuple[np.array, pd.DataFrame]:
        """加载CSV数据"""
        try:
            df = pd.read_csv(csv_path)

            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', '最高价',
                           'low', 'Low', '最低价', 'price', 'Price', '价格']

            price_col = None
            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break

            if price_col is None:
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    raise ValueError("未找到价格数据列")

            prices = df[price_col].values
            prices = prices[~np.isnan(prices)]

            print(f"加载CSV数据: {len(prices)} 个价格点，使用列: {price_col}")
            return prices, df

        except Exception as e:
            print(f"加载CSV失败 {csv_path}: {e}")
            return None, None

    def calculate_ma250(self, prices: np.array) -> np.array:
        """计算250日移动平均线"""
        if len(prices) < 250:
            print(f"数据长度 {len(prices)} 不足250天，无法计算250日均线")
            return None

        ma250 = np.full(len(prices), np.nan)
        for i in range(249, len(prices)):
            ma250[i] = np.mean(prices[i-249:i+1])

        return ma250

    def find_major_extremes(self, prices: np.array, min_distance: int = 50) -> Dict:
        """寻找主要的极值点（大级别的峰值和谷值）"""
        # 使用更大的prominence来找到主要极值
        price_range = np.max(prices) - np.min(prices)
        prominence_threshold = price_range * 0.1  # 10%的价格范围

        # 寻找主要峰值
        peaks, peak_props = signal.find_peaks(
            prices,
            prominence=prominence_threshold,
            distance=min_distance
        )

        # 寻找主要谷值
        troughs, trough_props = signal.find_peaks(
            -prices,
            prominence=prominence_threshold,
            distance=min_distance
        )

        print(f"找到 {len(peaks)} 个主要峰值, {len(troughs)} 个主要谷值")

        return {
            'peaks': peaks,
            'troughs': troughs,
            'peak_prominences': peak_props['prominences'],
            'trough_prominences': trough_props['prominences']
        }

    def detect_major_n_patterns(self, prices: np.array, ma250: np.array) -> List[Dict]:
        """检测大级别N字形态"""
        n_patterns = []

        # 找到主要极值点
        extremes = self.find_major_extremes(prices, min_distance=30)
        peaks = extremes['peaks']
        troughs = extremes['troughs']

        print(f"\n开始分析大级别N字形态...")

        # 寻找N字形态：低点1 -> 高点1 -> 低点2 -> 高点2
        for i, trough1 in enumerate(troughs):
            if trough1 < 250:  # 确保有250日均线数据
                continue

            for peak1 in peaks:
                if peak1 <= trough1:
                    continue

                for trough2 in troughs:
                    if trough2 <= peak1:
                        continue

                    for peak2 in peaks:
                        if peak2 <= trough2:
                            continue

                        # 检查时间跨度（至少100天，最多1000天）
                        total_duration = peak2 - trough1
                        if total_duration < 100 or total_duration > 1000:
                            continue

                        # 检查是否符合N字形态的条件
                        pattern_score = self._evaluate_major_n_pattern(
                            prices, ma250, trough1, peak1, trough2, peak2
                        )

                        if pattern_score > 0.3:  # 降低阈值，显示更多候选
                            pattern = {
                                'trough1_index': trough1,
                                'peak1_index': peak1,
                                'trough2_index': trough2,
                                'peak2_index': peak2,
                                'trough1_price': prices[trough1],
                                'peak1_price': prices[peak1],
                                'trough2_price': prices[trough2],
                                'peak2_price': prices[peak2],
                                'ma250_at_trough1': ma250[trough1] if not np.isnan(ma250[trough1]) else 0,
                                'ma250_at_trough2': ma250[trough2] if not np.isnan(ma250[trough2]) else 0,
                                'pattern_score': pattern_score,
                                'total_duration': total_duration,
                                'first_leg_gain': (prices[peak1] - prices[trough1]) / prices[trough1],
                                'second_leg_gain': (prices[peak2] - prices[trough2]) / prices[trough2],
                                'retracement': (prices[peak1] - prices[trough2]) / (prices[peak1] - prices[trough1])
                            }
                            n_patterns.append(pattern)

                            print(f"找到N字形态:")
                            print(f"  第{trough1}天({prices[trough1]:.2f}) -> 第{peak1}天({prices[peak1]:.2f}) -> 第{trough2}天({prices[trough2]:.2f}) -> 第{peak2}天({prices[peak2]:.2f})")
                            print(f"  得分: {pattern_score:.3f}")

        # 按得分排序
        n_patterns.sort(key=lambda x: x['pattern_score'], reverse=True)

        print(f"找到 {len(n_patterns)} 个大级别N字形态")
        return n_patterns

    def _evaluate_major_n_pattern(self, prices: np.array, ma250: np.array,
                                 trough1: int, peak1: int, trough2: int, peak2: int) -> float:
        """评估大级别N字形态的质量"""
        score = 0

        print(f"  评估N字形态: T1({trough1}) -> P1({peak1}) -> T2({trough2}) -> P2({peak2})")

        # 1. 检查第二个低点是否在250日均线附近（关键特征）
        if not np.isnan(ma250[trough2]):
            trough2_distance = abs(prices[trough2] - ma250[trough2]) / ma250[trough2]
            print(f"    低点2距离MA250: {trough2_distance*100:.1f}%")

            if trough2_distance <= 0.05:  # 5%以内
                score += 0.4
            elif trough2_distance <= 0.1:  # 10%以内
                score += 0.2

        # 2. 检查第二个高点是否创新高
        if prices[peak2] > prices[peak1]:
            score += 0.3
            print(f"    创新高: P2({prices[peak2]:.2f}) > P1({prices[peak1]:.2f})")

        # 3. 检查回撤幅度是否合理（30%-70%）
        total_gain = prices[peak1] - prices[trough1]
        retracement = prices[peak1] - prices[trough2]
        if total_gain > 0:
            retracement_ratio = retracement / total_gain
            print(f"    回撤比例: {retracement_ratio*100:.1f}%")

            if 0.3 <= retracement_ratio <= 0.8:  # 30%-80%回撤
                score += 0.2

        # 4. 检查涨幅是否显著
        first_leg_gain = (prices[peak1] - prices[trough1]) / prices[trough1]
        second_leg_gain = (prices[peak2] - prices[trough2]) / prices[trough2]

        print(f"    第一段涨幅: {first_leg_gain*100:.1f}%, 第二段涨幅: {second_leg_gain*100:.1f}%")

        if first_leg_gain > 0.3 and second_leg_gain > 0.2:  # 第一段30%+，第二段20%+
            score += 0.1

        print(f"    总得分: {score:.3f}")
        return score

    def visualize_major_patterns(self, csv_path: str, prices: np.array, ma250: np.array,
                                n_patterns: List[Dict], save_plot: bool = False):
        """可视化大级别N字形态"""
        if not n_patterns:
            print("没有找到N字形态可视化")
            return

        fig, ax = plt.subplots(1, 1, figsize=(20, 12))

        # 绘制价格线
        ax.plot(prices, 'b-', linewidth=1, alpha=0.6, label='Price')

        # 绘制250日均线
        valid_ma = ~np.isnan(ma250)
        ax.plot(np.where(valid_ma)[0], ma250[valid_ma], 'orange', linewidth=2, alpha=0.8, label='MA250')

        ax.set_title(f'Major N-Pattern Detection (显示前20个): {os.path.basename(csv_path)}')
        ax.set_ylabel('Price')
        ax.set_xlabel('Time (Days)')
        ax.grid(True, alpha=0.3)

        # 生成20种不同颜色
        import matplotlib.cm as cm
        colors = cm.tab20(np.linspace(0, 1, 20))

        # 显示前20个N字形态
        display_count = min(20, len(n_patterns))

        for i, pattern in enumerate(n_patterns[:display_count]):
            color = colors[i]

            # 绘制N字形态的连线
            n_indices = [
                pattern['trough1_index'],
                pattern['peak1_index'],
                pattern['trough2_index'],
                pattern['peak2_index']
            ]
            n_prices = [
                pattern['trough1_price'],
                pattern['peak1_price'],
                pattern['trough2_price'],
                pattern['peak2_price']
            ]

            # 使用不同的线型来区分
            line_styles = ['-', '--', '-.', ':']
            line_style = line_styles[i % 4]

            ax.plot(n_indices, n_prices, color=color, linewidth=3, alpha=0.8,
                   linestyle=line_style, marker='o', markersize=6,
                   label=f'N{i+1} (score: {pattern["pattern_score"]:.2f})')

            # 添加编号标注（只在第一个点标注编号）
            ax.annotate(f'N{i+1}',
                       (pattern['trough1_index'], pattern['trough1_price']),
                       xytext=(-10, -15),
                       textcoords='offset points',
                       ha='center',
                       fontsize=8,
                       color=color,
                       fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

        # 创建两列图例
        handles, labels = ax.get_legend_handles_labels()
        # 分成两列显示
        ncol = 2 if display_count > 10 else 1
        ax.legend(handles, labels, loc='upper left', ncol=ncol, fontsize=8)

        plt.tight_layout()

        if save_plot:
            plot_name = f"{os.path.splitext(os.path.basename(csv_path))[0]}_major_n_patterns_all.png"
            plt.savefig(plot_name, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {plot_name}")

        plt.show()

        # 显示详细信息供用户选择
        print(f"\n=== 找到的 {display_count} 个N字形态详情 ===")
        for i, pattern in enumerate(n_patterns[:display_count]):
            print(f"\nN{i+1} (得分: {pattern['pattern_score']:.3f}):")
            print(f"  时间: 第{pattern['trough1_index']}天 -> 第{pattern['peak1_index']}天 -> 第{pattern['trough2_index']}天 -> 第{pattern['peak2_index']}天")
            print(f"  价格: {pattern['trough1_price']:.2f} -> {pattern['peak1_price']:.2f} -> {pattern['trough2_price']:.2f} -> {pattern['peak2_price']:.2f}")
            print(f"  第一段涨幅: {pattern['first_leg_gain']*100:.1f}%, 第二段涨幅: {pattern['second_leg_gain']*100:.1f}%")
            print(f"  回撤比例: {pattern['retracement']*100:.1f}%, 总时长: {pattern['total_duration']}天")

            # 检查250日线距离
            if not np.isnan(pattern['ma250_at_trough2']) and pattern['ma250_at_trough2'] > 0:
                distance_pct = abs(pattern['trough2_price'] - pattern['ma250_at_trough2']) / pattern['ma250_at_trough2'] * 100
                print(f"  低点2距离MA250: {distance_pct:.1f}%")

        return display_count

    def select_correct_patterns(self, n_patterns: List[Dict], csv_path: str) -> List[Dict]:
        """让用户选择正确的N字形态"""
        if not n_patterns:
            return []

        print(f"\n=== 选择正确的N字形态 ===")
        print("请输入您认为正确的N字形态编号（用逗号分隔，如: 1,3,5）")
        print("输入 'all' 选择所有形态")
        print("输入 'none' 或直接回车跳过")

        user_input = input("您的选择: ").strip()

        if not user_input or user_input.lower() == 'none':
            return []

        selected_patterns = []

        if user_input.lower() == 'all':
            selected_patterns = n_patterns.copy()
            print(f"已选择所有 {len(selected_patterns)} 个N字形态")
        else:
            try:
                # 解析用户输入的编号
                selected_numbers = [int(x.strip()) for x in user_input.split(',')]

                for num in selected_numbers:
                    if 1 <= num <= len(n_patterns):
                        selected_patterns.append(n_patterns[num - 1])
                        print(f"已选择 N{num}")
                    else:
                        print(f"编号 {num} 超出范围，忽略")

            except ValueError:
                print("输入格式错误，请使用数字和逗号")
                return []

        if selected_patterns:
            # 保存选择的形态
            self.save_selected_patterns(selected_patterns, csv_path)

        return selected_patterns

    def save_selected_patterns(self, selected_patterns: List[Dict], csv_path: str):
        """保存用户选择的正确N字形态"""
        import json
        from datetime import datetime

        # 创建保存数据
        save_data = {
            'source_file': os.path.basename(csv_path),
            'selection_time': datetime.now().isoformat(),
            'pattern_count': len(selected_patterns),
            'patterns': []
        }

        for i, pattern in enumerate(selected_patterns):
            pattern_data = {
                'pattern_id': i + 1,
                'trough1_index': int(pattern['trough1_index']),
                'peak1_index': int(pattern['peak1_index']),
                'trough2_index': int(pattern['trough2_index']),
                'peak2_index': int(pattern['peak2_index']),
                'trough1_price': float(pattern['trough1_price']),
                'peak1_price': float(pattern['peak1_price']),
                'trough2_price': float(pattern['trough2_price']),
                'peak2_price': float(pattern['peak2_price']),
                'pattern_score': float(pattern['pattern_score']),
                'total_duration': int(pattern['total_duration']),
                'first_leg_gain': float(pattern['first_leg_gain']),
                'second_leg_gain': float(pattern['second_leg_gain']),
                'retracement': float(pattern['retracement'])
            }
            save_data['patterns'].append(pattern_data)

        # 保存到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"selected_n_patterns_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 已保存 {len(selected_patterns)} 个选择的N字形态到: {filename}")
        print("这些数据可以用于训练改进的模型")

    def analyze_csv(self, csv_path: str):
        """分析CSV文件"""
        print(f"\n=== 大级别N字形态分析: {os.path.basename(csv_path)} ===")

        # 加载数据
        prices, df = self.load_csv_data(csv_path)
        if prices is None:
            return []

        # 计算250日均线
        ma250 = self.calculate_ma250(prices)
        if ma250 is None:
            return []

        # 检测N字形态
        n_patterns = self.detect_major_n_patterns(prices, ma250)

        if n_patterns:
            # 可视化所有找到的形态
            display_count = self.visualize_major_patterns(csv_path, prices, ma250, n_patterns, save_plot=True)

            # 让用户选择正确的形态
            selected_patterns = self.select_correct_patterns(n_patterns, csv_path)

            if selected_patterns:
                print(f"\n✅ 您选择了 {len(selected_patterns)} 个正确的N字形态")
                print("这些数据已保存，可用于训练更精确的模型")
            else:
                print("\n未选择任何形态")
        else:
            print("未找到大级别N字形态")

        return n_patterns

def main():
    """主函数"""
    print("=== 大级别N字形态检测器 ===\n")
    print("专门识别跨越较长时间的主要趋势N字形态")
    print("特征: 低点1 -> 高点1 -> 低点2(250日线附近) -> 高点2(创新高)")

    detector = MajorNPatternDetector()

    csv_path = input("\n请输入CSV文件路径: ").strip()
    if not os.path.exists(csv_path):
        print("文件不存在!")
        return

    n_patterns = detector.analyze_csv(csv_path)

    if n_patterns:
        print(f"\n检测完成! 找到 {len(n_patterns)} 个大级别N字形态")
    else:
        print("\n未找到符合条件的大级别N字形态")

if __name__ == "__main__":
    main()
