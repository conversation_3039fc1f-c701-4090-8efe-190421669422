#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于CNN的N字形态图像识别系统
专门用于识别股票图表中的N字形态
"""

import os
import numpy as np
import pandas as pd
import cv2
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import json
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import glob
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

class ImageDataAnalyzer:
    """图像数据分析器 - 分析标注的图片"""

    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self.image_info = []

    def analyze_directory(self):
        """分析目录中的图片文件"""
        print(f"分析目录: {self.data_dir}")

        # 支持的图片格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']

        all_images = []
        for ext in image_extensions:
            all_images.extend(glob.glob(os.path.join(self.data_dir, ext)))
            all_images.extend(glob.glob(os.path.join(self.data_dir, ext.upper())))

        print(f"找到 {len(all_images)} 个图片文件")

        # 分析每个图片
        for img_path in all_images:
            info = self._analyze_single_image(img_path)
            if info:
                self.image_info.append(info)

        # 生成分析报告
        self._generate_analysis_report()

        return self.image_info

    def _analyze_single_image(self, img_path: str) -> Optional[Dict]:
        """分析单个图片"""
        try:
            # 首先检查文件是否存在和大小
            if not os.path.exists(img_path):
                print(f"文件不存在: {img_path}")
                return None

            file_size = os.path.getsize(img_path)
            if file_size == 0:
                print(f"文件为空: {img_path}")
                return None

            # 尝试用PIL读取图片
            try:
                from PIL import Image
                pil_img = Image.open(img_path)
                pil_img.verify()  # 验证图片完整性
                pil_img = Image.open(img_path)  # 重新打开，因为verify()后无法使用

                # 转换为RGB格式
                if pil_img.mode != 'RGB':
                    pil_img = pil_img.convert('RGB')

                # 转换为numpy数组
                img_array = np.array(pil_img)

                # 转换为OpenCV格式 (BGR)
                img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            except Exception as pil_error:
                print(f"PIL读取失败 {img_path}: {pil_error}")
                # 尝试用OpenCV读取
                img = cv2.imread(img_path)
                if img is None:
                    # 尝试用不同的编码读取
                    try:
                        # 尝试用utf-8编码的路径
                        img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                        if img is None:
                            print(f"OpenCV也无法读取图片: {img_path}")
                            return None
                    except Exception as cv_error:
                        print(f"OpenCV读取失败 {img_path}: {cv_error}")
                        return None

            height, width, channels = img.shape
            file_size = os.path.getsize(img_path)

            # 转换为灰度图进行分析
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 检测边缘和线条
            edges = cv2.Canny(gray, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=30, maxLineGap=10)

            # 计算图片特征
            brightness = np.mean(gray)
            contrast = np.std(gray)

            info = {
                'file_path': img_path,
                'filename': os.path.basename(img_path),
                'width': width,
                'height': height,
                'channels': channels,
                'file_size': file_size,
                'brightness': brightness,
                'contrast': contrast,
                'edge_density': np.sum(edges > 0) / (width * height),
                'line_count': len(lines) if lines is not None else 0
            }

            return info

        except Exception as e:
            print(f"分析图片时出错 {img_path}: {e}")
            return None

    def _generate_analysis_report(self):
        """生成分析报告"""
        if not self.image_info:
            print("没有有效的图片数据")
            return

        df = pd.DataFrame(self.image_info)

        print("\n=== 图片数据分析报告 ===")
        print(f"总图片数量: {len(df)}")
        print(f"平均尺寸: {df['width'].mean():.0f} x {df['height'].mean():.0f}")
        print(f"尺寸范围: {df['width'].min()}-{df['width'].max()} x {df['height'].min()}-{df['height'].max()}")
        print(f"平均文件大小: {df['file_size'].mean()/1024:.1f} KB")
        print(f"平均亮度: {df['brightness'].mean():.1f}")
        print(f"平均对比度: {df['contrast'].mean():.1f}")
        print(f"平均边缘密度: {df['edge_density'].mean():.3f}")
        print(f"平均线条数量: {df['line_count'].mean():.1f}")

        # 保存分析结果
        report_path = os.path.join(self.data_dir, "image_analysis_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.image_info, f, ensure_ascii=False, indent=2)
        print(f"分析报告已保存到: {report_path}")

class NPatternCNNTrainer:
    """基于CNN的N字形态识别训练器"""

    def __init__(self, img_size=(224, 224)):
        self.img_size = img_size
        self.model = None
        self.history = None

    def create_cnn_model(self, input_shape=(224, 224, 3), num_classes=2):
        """创建CNN模型"""
        model = keras.Sequential([
            # 第一个卷积块
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第二个卷积块
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第三个卷积块
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第四个卷积块
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 全连接层
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(num_classes, activation='softmax')
        ])

        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    def preprocess_image(self, img_path: str) -> Optional[np.ndarray]:
        """预处理单个图片"""
        try:
            # 首先尝试用PIL读取
            try:
                from PIL import Image
                pil_img = Image.open(img_path)

                # 转换为RGB格式
                if pil_img.mode != 'RGB':
                    pil_img = pil_img.convert('RGB')

                # 调整大小
                pil_img = pil_img.resize(self.img_size, Image.Resampling.LANCZOS)

                # 转换为numpy数组
                img = np.array(pil_img)

            except Exception as pil_error:
                print(f"PIL预处理失败，尝试OpenCV: {pil_error}")

                # 尝试用OpenCV读取
                img = cv2.imread(img_path)
                if img is None:
                    # 尝试用不同编码读取
                    img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if img is None:
                        print(f"无法读取图片: {img_path}")
                        return None

                # 转换颜色空间
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

                # 调整大小
                img = cv2.resize(img, self.img_size)

            # 归一化
            img = img.astype(np.float32) / 255.0

            return img

        except Exception as e:
            print(f"预处理图片时出错 {img_path}: {e}")
            return None

    def load_and_preprocess_data(self, positive_dir: str, negative_dir: str = None):
        """加载和预处理训练数据"""
        images = []
        labels = []

        print("加载正样本图片...")
        # 加载正样本
        positive_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            positive_files.extend(glob.glob(os.path.join(positive_dir, ext)))
            positive_files.extend(glob.glob(os.path.join(positive_dir, ext.upper())))

        for img_path in positive_files:
            img = self.preprocess_image(img_path)
            if img is not None:
                images.append(img)
                labels.append(1)  # 正样本标签

        print(f"加载了 {len([l for l in labels if l == 1])} 个正样本")

        # 如果没有提供负样本目录，从正样本中生成负样本
        if negative_dir is None or not os.path.exists(negative_dir):
            print("生成负样本（通过数据增强）...")
            negative_samples = self._generate_negative_samples(positive_files)
            for img in negative_samples:
                images.append(img)
                labels.append(0)  # 负样本标签
        else:
            print("加载负样本图片...")
            negative_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                negative_files.extend(glob.glob(os.path.join(negative_dir, ext)))
                negative_files.extend(glob.glob(os.path.join(negative_dir, ext.upper())))

            for img_path in negative_files:
                img = self.preprocess_image(img_path)
                if img is not None:
                    images.append(img)
                    labels.append(0)  # 负样本标签

        print(f"总共加载了 {len(images)} 个样本")
        print(f"正样本: {sum(labels)} 个, 负样本: {len(labels) - sum(labels)} 个")

        # 转换为numpy数组
        X = np.array(images)
        y = keras.utils.to_categorical(labels, 2)  # 转换为one-hot编码

        return X, y

    def _generate_negative_samples(self, positive_files: List[str], count_multiplier: float = 1.0) -> List[np.ndarray]:
        """通过数据增强生成负样本"""
        negative_samples = []
        target_count = int(len(positive_files) * count_multiplier)

        for i in range(target_count):
            # 随机选择一个正样本
            img_path = np.random.choice(positive_files)
            img = self.preprocess_image(img_path)

            if img is not None:
                # 应用随机变换来创建负样本
                transformed_img = self._apply_negative_transforms(img)
                negative_samples.append(transformed_img)

        return negative_samples

    def _apply_negative_transforms(self, img: np.ndarray) -> np.ndarray:
        """应用变换来创建负样本"""
        # 随机选择变换类型
        transform_type = np.random.choice(['flip', 'rotate', 'noise', 'blur', 'crop'])

        if transform_type == 'flip':
            # 水平或垂直翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
            else:
                img = np.flipud(img)

        elif transform_type == 'rotate':
            # 旋转
            angle = np.random.uniform(-30, 30)
            center = (img.shape[1]//2, img.shape[0]//2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            img = cv2.warpAffine(img, M, (img.shape[1], img.shape[0]))

        elif transform_type == 'noise':
            # 添加噪声
            noise = np.random.normal(0, 0.1, img.shape)
            img = np.clip(img + noise, 0, 1)

        elif transform_type == 'blur':
            # 模糊
            img = cv2.GaussianBlur(img, (5, 5), 0)

        elif transform_type == 'crop':
            # 随机裁剪和缩放
            h, w = img.shape[:2]
            crop_size = int(min(h, w) * 0.8)
            start_h = np.random.randint(0, h - crop_size)
            start_w = np.random.randint(0, w - crop_size)
            cropped = img[start_h:start_h+crop_size, start_w:start_w+crop_size]
            img = cv2.resize(cropped, (w, h))

        return img

    def train_model(self, X: np.ndarray, y: np.ndarray, validation_split: float = 0.2,
                   epochs: int = 50, batch_size: int = 32):
        """训练CNN模型"""
        print("开始训练CNN模型...")

        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, stratify=y.argmax(axis=1)
        )

        print(f"训练集: {len(X_train)} 个样本")
        print(f"验证集: {len(X_val)} 个样本")

        # 创建模型
        input_shape = X.shape[1:]
        self.model = self.create_cnn_model(input_shape)

        # 显示模型结构
        print("\n模型结构:")
        self.model.summary()

        # 设置回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
        ]

        # 训练模型
        self.history = self.model.fit(
            X_train, y_train,
            batch_size=batch_size,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )

        # 评估模型
        train_loss, train_acc = self.model.evaluate(X_train, y_train, verbose=0)
        val_loss, val_acc = self.model.evaluate(X_val, y_val, verbose=0)

        print(f"\n训练结果:")
        print(f"训练准确率: {train_acc:.4f}")
        print(f"验证准确率: {val_acc:.4f}")

        # 生成分类报告
        y_pred = self.model.predict(X_val)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(y_val, axis=1)

        print("\n分类报告:")
        print(classification_report(y_true_classes, y_pred_classes,
                                  target_names=['非N字形态', 'N字形态']))

        return self.history

    def save_model(self, model_path: str, metadata: Dict = None):
        """保存训练好的模型"""
        if self.model is None:
            raise ValueError("没有训练好的模型可以保存")

        # 保存模型
        self.model.save(model_path)

        # 保存元数据
        metadata_path = model_path.replace('.h5', '_metadata.json')
        model_metadata = {
            'img_size': self.img_size,
            'model_path': model_path,
            'created_at': datetime.now().isoformat(),
            'metadata': metadata or {}
        }

        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(model_metadata, f, ensure_ascii=False, indent=2)

        print(f"模型已保存到: {model_path}")
        print(f"元数据已保存到: {metadata_path}")

    def load_model(self, model_path: str):
        """加载训练好的模型"""
        try:
            self.model = keras.models.load_model(model_path)

            # 加载元数据
            metadata_path = model_path.replace('.h5', '_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    self.img_size = tuple(metadata.get('img_size', (224, 224)))
                    print(f"模型加载成功: {model_path}")
                    print(f"创建时间: {metadata.get('created_at', '未知')}")
                    return metadata
            else:
                print(f"模型加载成功，但未找到元数据文件")
                return {}

        except Exception as e:
            print(f"加载模型时出错: {e}")
            return None

    def predict_image(self, img_path: str, threshold: float = 0.5) -> Dict:
        """预测单个图片"""
        if self.model is None:
            raise ValueError("请先训练或加载模型")

        # 预处理图片
        img = self.preprocess_image(img_path)
        if img is None:
            return {'error': '无法处理图片'}

        # 添加batch维度
        img_batch = np.expand_dims(img, axis=0)

        # 预测
        predictions = self.model.predict(img_batch, verbose=0)
        prob_negative, prob_positive = predictions[0]

        result = {
            'file_path': img_path,
            'filename': os.path.basename(img_path),
            'probability_negative': float(prob_negative),
            'probability_positive': float(prob_positive),
            'confidence': float(max(prob_negative, prob_positive)),
            'prediction': 'N字形态' if prob_positive > threshold else '非N字形态',
            'is_n_pattern': bool(prob_positive > threshold)
        }

        return result

    def batch_predict(self, image_dir: str, threshold: float = 0.5) -> List[Dict]:
        """批量预测图片目录"""
        if self.model is None:
            raise ValueError("请先训练或加载模型")

        # 收集图片文件
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(glob.glob(os.path.join(image_dir, ext)))
            image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))

        if not image_files:
            print("目录中没有找到图片文件!")
            return []

        print(f"开始批量预测 {len(image_files)} 个图片...")

        results = []
        for i, img_path in enumerate(image_files):
            print(f"预测 {i+1}/{len(image_files)}: {os.path.basename(img_path)}")
            result = self.predict_image(img_path, threshold)
            results.append(result)

        # 统计结果
        n_patterns_found = sum(1 for r in results if r.get('is_n_pattern', False))
        print(f"\n预测完成! 找到 {n_patterns_found} 个可能的N字形态")

        return results

    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可以绘制")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # 绘制准确率
        ax1.plot(self.history.history['accuracy'], label='训练准确率')
        ax1.plot(self.history.history['val_accuracy'], label='验证准确率')
        ax1.set_title('模型准确率')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('准确率')
        ax1.legend()

        # 绘制损失
        ax2.plot(self.history.history['loss'], label='训练损失')
        ax2.plot(self.history.history['val_loss'], label='验证损失')
        ax2.set_title('模型损失')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('损失')
        ax2.legend()

        plt.tight_layout()
        plt.show()


def analyze_user_data(data_dir: str = "D:\\双回撤"):
    """分析用户标注的数据"""
    print("=== 分析用户标注数据 ===")

    if not os.path.exists(data_dir):
        print(f"目录不存在: {data_dir}")
        return None

    analyzer = ImageDataAnalyzer(data_dir)
    image_info = analyzer.analyze_directory()

    return image_info


def load_user_labels(data_dir: str):
    """加载用户的标注数据"""
    label_file = os.path.join(data_dir, "labels.json")

    if not os.path.exists(label_file):
        print(f"未找到标注文件: {label_file}")
        print("请先运行 interactive_labeling.py 进行标注")
        return None, None

    try:
        with open(label_file, 'r', encoding='utf-8') as f:
            labels = json.load(f)

        positive_files = []
        negative_files = []

        for filename, label in labels.items():
            file_path = os.path.join(data_dir, filename)
            if os.path.exists(file_path):
                if label == "positive":
                    positive_files.append(file_path)
                elif label == "negative":
                    negative_files.append(file_path)

        print(f"加载标注数据: 正样本 {len(positive_files)} 个, 负样本 {len(negative_files)} 个")
        return positive_files, negative_files

    except Exception as e:
        print(f"加载标注文件失败: {e}")
        return None, None

def main():
    """主函数"""
    print("=== 基于CNN的N字形态识别系统 ===\n")

    user_data_dir = "D:\\双回撤"

    # 首先分析用户的标注数据
    print("步骤1: 分析您的标注数据")
    image_info = analyze_user_data(user_data_dir)

    if not image_info:
        print("无法分析用户数据，程序退出")
        return

    print(f"\n发现 {len(image_info)} 个图片文件")

    # 询问用户如何处理
    print("\n请选择操作:")
    print("1. 使用已有标注数据训练模型")
    print("2. 重新标注数据")
    print("3. 仅分析数据，不训练模型")

    choice = input("请输入选择 (1/2/3): ").strip()

    if choice == "1":
        # 使用已有标注数据
        positive_files, negative_files = load_user_labels(user_data_dir)

        if positive_files is None or negative_files is None:
            print("请先运行标注工具: python interactive_labeling.py")
            return

        if len(positive_files) < 5 or len(negative_files) < 5:
            print("正负样本数量太少，建议每类至少5个样本")
            print(f"当前: 正样本 {len(positive_files)} 个, 负样本 {len(negative_files)} 个")
            return

        trainer = NPatternCNNTrainer()

        print("\n加载和预处理数据...")
        # 修改加载方法以使用标注数据
        images = []
        labels = []

        # 加载正样本
        for img_path in positive_files:
            img = trainer.preprocess_image(img_path)
            if img is not None:
                images.append(img)
                labels.append(1)

        # 加载负样本
        for img_path in negative_files:
            img = trainer.preprocess_image(img_path)
            if img is not None:
                images.append(img)
                labels.append(0)

        if len(images) < 10:
            print("可用数据太少，无法训练模型")
            return

        # 转换为numpy数组
        X = np.array(images)
        y = keras.utils.to_categorical(labels, 2)

        print(f"训练数据: {len(X)} 个样本")
        print(f"正样本: {sum(labels)} 个, 负样本: {len(labels) - sum(labels)} 个")

        print("\n开始训练模型...")
        trainer.train_model(X, y, epochs=50, batch_size=16)

        # 保存模型
        model_path = "n_pattern_cnn_model.h5"
        metadata = {
            'source_dir': user_data_dir,
            'positive_samples': len(positive_files),
            'negative_samples': len(negative_files),
            'total_samples': len(X)
        }
        trainer.save_model(model_path, metadata)

        # 绘制训练历史
        trainer.plot_training_history()

        print(f"\n模型训练完成并保存到: {model_path}")

    elif choice == "2":
        print("启动交互式标注工具...")
        print("请运行: python interactive_labeling.py")

    elif choice == "3":
        print("数据分析完成")

    else:
        print("无效的选择")


if __name__ == "__main__":
    main()