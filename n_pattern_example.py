#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
N字形态机器学习使用示例
简化版本，展示如何使用训练器
"""

from n_pattern_ml_trainer import NPatternMLTrainer
import os
import glob

def quick_train_example():
    """快速训练示例"""
    print("=== 快速训练示例 ===")
    
    # 创建训练器
    trainer = NPatternMLTrainer()
    
    # 首先创建示例数据
    from n_pattern_ml_trainer import create_sample_training_data
    create_sample_training_data()
    
    # 收集训练文件
    positive_files = glob.glob("training_data/positive/*.csv")
    negative_files = glob.glob("training_data/negative/*.csv")
    
    print(f"正样本: {len(positive_files)} 个")
    print(f"负样本: {len(negative_files)} 个")
    
    # 准备训练数据
    X, y = trainer.prepare_training_data(positive_files, negative_files)
    
    # 训练模型
    results = trainer.train_models(X, y)
    
    # 保存模型
    model_path = "n_pattern_model.pkl"
    trainer.save_model(model_path)
    
    print(f"模型已保存到: {model_path}")
    return model_path

def quick_predict_example(model_path, csv_file):
    """快速预测示例"""
    print("=== 快速预测示例 ===")
    
    # 创建训练器并加载模型
    trainer = NPatternMLTrainer()
    trainer.load_model(model_path)
    
    # 预测单个文件
    result = trainer.predict_csv(csv_file)
    
    print(f"文件: {os.path.basename(csv_file)}")
    print(f"是否包含N字形态: {'是' if result['is_n_pattern'] else '否'}")
    print(f"置信度: {result['confidence']:.3f}")
    print(f"正样本概率: {result['probability_positive']:.3f}")
    
    return result

def batch_predict_example(model_path, csv_directory):
    """批量预测示例"""
    print("=== 批量预测示例 ===")
    
    # 创建训练器并加载模型
    trainer = NPatternMLTrainer()
    trainer.load_model(model_path)
    
    # 收集CSV文件
    csv_files = glob.glob(os.path.join(csv_directory, "*.csv"))
    
    if not csv_files:
        print("目录中没有找到CSV文件!")
        return []
    
    # 批量预测
    results = trainer.batch_predict(csv_files, threshold=0.6)
    
    # 显示结果
    n_patterns = [r for r in results if r.get('is_n_pattern', False)]
    
    if n_patterns:
        print(f"找到 {len(n_patterns)} 个可能的N字形态:")
        for result in n_patterns:
            print(f"  {os.path.basename(result['file_path'])}: 置信度 {result['confidence']:.3f}")
    else:
        print("没有找到N字形态")
    
    return results

def main():
    """主函数"""
    print("=== N字形态机器学习示例 ===\n")
    
    # 1. 训练模型
    print("步骤1: 训练模型")
    model_path = quick_train_example()
    
    print("\n" + "="*50 + "\n")
    
    # 2. 预测示例
    print("步骤2: 预测示例")
    
    # 使用训练数据中的一个文件作为测试
    test_file = "training_data/positive/n_pattern_1.csv"
    if os.path.exists(test_file):
        quick_predict_example(model_path, test_file)
    
    print("\n" + "="*50 + "\n")
    
    # 3. 批量预测示例
    print("步骤3: 批量预测示例")
    batch_predict_example(model_path, "training_data/positive")
    
    print("\n=== 示例完成 ===")
    print(f"训练好的模型保存在: {model_path}")
    print("您可以使用这个模型来预测新的CSV文件!")

if __name__ == "__main__":
    main()
