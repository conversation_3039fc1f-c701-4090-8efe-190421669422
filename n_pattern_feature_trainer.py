#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
N字形态特征训练器
专门提取和学习N字形态的关键特征
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import joblib
from scipy import signal
from yellow_marker_detector import YellowMarkerDetector

class NPatternFeatureExtractor:
    """N字形态特征提取器"""
    
    def __init__(self):
        self.detector = YellowMarkerDetector()
        
    def extract_price_line_from_image(self, img_path: str):
        """从图像中提取价格线数据"""
        try:
            # 读取图像
            img = cv2.imread(img_path)
            if img is None:
                img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 检测黄色区域，用于确定分析范围
            detection_result = self.detector.detect_yellow_markers(img_path)
            
            if not detection_result or not detection_result['yellow_detected']:
                return None
            
            # 获取图像尺寸
            height, width = gray.shape
            
            # 寻找价格线（通常是白色或亮色的线条）
            # 使用阈值处理找到亮色区域
            _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
            
            # 寻找水平方向的价格变化
            price_line = []
            
            # 从左到右扫描，找到每列的最亮点作为价格点
            for x in range(width):
                column = thresh[:, x]
                bright_points = np.where(column == 255)[0]
                
                if len(bright_points) > 0:
                    # 取中位数作为价格点
                    price_y = np.median(bright_points)
                    # 转换为价格值（y坐标越小，价格越高）
                    price_value = (height - price_y) / height
                    price_line.append(price_value)
                else:
                    # 如果没有找到亮点，使用前一个值或0.5
                    if price_line:
                        price_line.append(price_line[-1])
                    else:
                        price_line.append(0.5)
            
            return np.array(price_line)
            
        except Exception as e:
            print(f"提取价格线失败 {img_path}: {e}")
            return None
    
    def extract_n_pattern_features(self, price_line: np.array):
        """提取N字形态特征"""
        if price_line is None or len(price_line) < 20:
            return None
        
        features = {}
        
        # 1. 基本统计特征
        features['mean'] = np.mean(price_line)
        features['std'] = np.std(price_line)
        features['range'] = np.max(price_line) - np.min(price_line)
        
        # 2. 趋势特征
        features['overall_trend'] = price_line[-1] - price_line[0]
        
        # 3. 寻找峰值和谷值
        peaks, _ = signal.find_peaks(price_line, height=np.mean(price_line), distance=5)
        troughs, _ = signal.find_peaks(-price_line, height=-np.mean(price_line), distance=5)
        
        features['peak_count'] = len(peaks)
        features['trough_count'] = len(troughs)
        
        # 4. N字形态特定特征
        if len(peaks) >= 2 and len(troughs) >= 1:
            # 分析峰值和谷值的模式
            all_extremes = []
            for p in peaks:
                all_extremes.append((p, price_line[p], 'peak'))
            for t in troughs:
                all_extremes.append((t, -price_line[t], 'trough'))
            
            # 按位置排序
            all_extremes.sort(key=lambda x: x[0])
            
            # 检查N字模式
            n_pattern_score = self._calculate_n_pattern_score(all_extremes, price_line)
            features['n_pattern_score'] = n_pattern_score
            
            # 计算关键点的相对位置
            if len(all_extremes) >= 4:
                # 取前4个极值点
                key_points = all_extremes[:4]
                
                # 计算相对高度差
                heights = [point[1] for point in key_points]
                features['height_variation'] = np.std(heights)
                
                # 计算时间间隔
                positions = [point[0] for point in key_points]
                time_intervals = np.diff(positions)
                features['avg_time_interval'] = np.mean(time_intervals)
                features['time_interval_std'] = np.std(time_intervals)
        else:
            features['n_pattern_score'] = 0
            features['height_variation'] = 0
            features['avg_time_interval'] = 0
            features['time_interval_std'] = 0
        
        # 5. 方向变化特征
        direction_changes = 0
        for i in range(1, len(price_line)):
            if i > 1:
                prev_direction = price_line[i-1] - price_line[i-2]
                curr_direction = price_line[i] - price_line[i-1]
                if prev_direction * curr_direction < 0:  # 方向改变
                    direction_changes += 1
        
        features['direction_changes'] = direction_changes
        features['direction_change_rate'] = direction_changes / len(price_line)
        
        # 6. 波动性特征
        price_diff = np.diff(price_line)
        features['volatility'] = np.std(price_diff)
        features['max_single_move'] = np.max(np.abs(price_diff))
        
        # 7. 形状特征
        # 计算价格线的"尖锐度"
        second_diff = np.diff(price_diff)
        features['sharpness'] = np.mean(np.abs(second_diff))
        
        return features
    
    def _calculate_n_pattern_score(self, extremes, price_line):
        """计算N字形态得分"""
        if len(extremes) < 4:
            return 0
        
        score = 0
        
        # 检查经典N字模式：高-低-高-低 或 低-高-低-高
        for i in range(len(extremes) - 3):
            pattern = [extremes[j][2] for j in range(i, i + 4)]
            
            if (pattern == ['peak', 'trough', 'peak', 'trough'] or 
                pattern == ['trough', 'peak', 'trough', 'peak']):
                
                # 计算形态的清晰度
                heights = [extremes[j][1] for j in range(i, i + 4)]
                height_range = max(heights) - min(heights)
                
                # 计算时间跨度
                positions = [extremes[j][0] for j in range(i, i + 4)]
                time_span = positions[-1] - positions[0]
                
                # 形态得分
                pattern_score = height_range * (time_span / len(price_line))
                score = max(score, pattern_score)
        
        return score

class NPatternFeatureTrainer:
    """N字形态特征训练器"""
    
    def __init__(self):
        self.feature_extractor = NPatternFeatureExtractor()
        self.scaler = StandardScaler()
        self.models = {
            'random_forest': RandomForestClassifier(n_estimators=200, random_state=42),
            'svm': SVC(kernel='rbf', random_state=42, probability=True)
        }
        self.best_model = None
        self.best_model_name = None
        
    def prepare_training_data(self, data_dir: str):
        """准备训练数据"""
        # 加载黄色检测结果
        summary_path = os.path.join(data_dir, "yellow_detection_summary.json")
        
        if not os.path.exists(summary_path):
            print("未找到黄色检测结果")
            return None, None
        
        with open(summary_path, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        positive_files = [result['image_path'] for result in summary['results']]
        
        print(f"处理 {len(positive_files)} 个包含黄色标识的图片...")
        
        # 提取特征
        features_list = []
        labels = []
        
        for i, img_path in enumerate(positive_files):
            print(f"处理 {i+1}/{len(positive_files)}: {os.path.basename(img_path)}")
            
            # 提取价格线
            price_line = self.feature_extractor.extract_price_line_from_image(img_path)
            
            if price_line is not None:
                # 提取N字形态特征
                features = self.feature_extractor.extract_n_pattern_features(price_line)
                
                if features is not None:
                    features_list.append(features)
                    labels.append(1)  # 正样本
        
        print(f"成功提取 {len(features_list)} 个样本的特征")
        
        # 生成负样本（随机特征）
        print("生成负样本...")
        negative_count = len(features_list)
        
        for i in range(negative_count):
            # 创建随机特征作为负样本
            random_features = self._generate_random_features()
            features_list.append(random_features)
            labels.append(0)  # 负样本
        
        # 转换为DataFrame格式
        import pandas as pd
        df = pd.DataFrame(features_list)
        df = df.fillna(0)  # 填充缺失值
        
        # 标准化特征
        X = self.scaler.fit_transform(df.values)
        y = np.array(labels)
        
        print(f"训练数据准备完成: {len(X)} 个样本, {X.shape[1]} 个特征")
        print(f"正样本: {sum(y)} 个, 负样本: {len(y) - sum(y)} 个")
        
        return X, y
    
    def _generate_random_features(self):
        """生成随机特征作为负样本"""
        return {
            'mean': np.random.uniform(0.3, 0.7),
            'std': np.random.uniform(0.05, 0.2),
            'range': np.random.uniform(0.1, 0.5),
            'overall_trend': np.random.uniform(-0.2, 0.2),
            'peak_count': np.random.randint(0, 5),
            'trough_count': np.random.randint(0, 5),
            'n_pattern_score': np.random.uniform(0, 0.1),  # 负样本的N字得分较低
            'height_variation': np.random.uniform(0, 0.1),
            'avg_time_interval': np.random.uniform(5, 50),
            'time_interval_std': np.random.uniform(1, 20),
            'direction_changes': np.random.randint(5, 30),
            'direction_change_rate': np.random.uniform(0.1, 0.8),
            'volatility': np.random.uniform(0.01, 0.1),
            'max_single_move': np.random.uniform(0.01, 0.1),
            'sharpness': np.random.uniform(0.001, 0.05)
        }
    
    def train_models(self, X: np.array, y: np.array):
        """训练模型"""
        if len(X) < 10:
            print("训练数据太少")
            return None
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        results = {}
        best_score = 0
        
        print("\n开始训练模型...")
        
        for model_name, model in self.models.items():
            print(f"\n训练 {model_name}...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5)
            
            # 测试集评估
            test_score = model.score(X_test, y_test)
            y_pred = model.predict(X_test)
            
            results[model_name] = {
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'test_score': test_score,
                'classification_report': classification_report(y_test, y_pred)
            }
            
            print(f"{model_name} - CV得分: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
            print(f"{model_name} - 测试得分: {test_score:.3f}")
            
            # 选择最佳模型
            if cv_scores.mean() > best_score:
                best_score = cv_scores.mean()
                self.best_model = model
                self.best_model_name = model_name
        
        print(f"\n最佳模型: {self.best_model_name} (CV得分: {best_score:.3f})")
        
        return results
    
    def save_model(self, model_path: str):
        """保存模型"""
        if self.best_model is None:
            print("没有训练好的模型")
            return
        
        model_data = {
            'model': self.best_model,
            'model_name': self.best_model_name,
            'scaler': self.scaler,
            'feature_extractor': self.feature_extractor
        }
        
        joblib.dump(model_data, model_path)
        print(f"模型已保存: {model_path}")

def main():
    """主函数"""
    print("=== N字形态特征训练器 ===\n")
    
    data_dir = "D:\\双回撤"
    
    # 创建训练器
    trainer = NPatternFeatureTrainer()
    
    # 准备训练数据
    X, y = trainer.prepare_training_data(data_dir)
    
    if X is None:
        print("数据准备失败")
        return
    
    # 训练模型
    results = trainer.train_models(X, y)
    
    if results:
        # 保存模型
        model_path = "n_pattern_feature_model.pkl"
        trainer.save_model(model_path)
        
        print(f"\n=== 训练完成 ===")
        print(f"模型已保存: {model_path}")
        
        # 显示详细结果
        for model_name, result in results.items():
            print(f"\n{model_name} 详细结果:")
            print(result['classification_report'])

if __name__ == "__main__":
    main()
