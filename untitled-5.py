    # ... existing code ...

    def plot_candlestick(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            print("请选择有效的文件路径")
            QMessageBox.warning(self, "警告", "请选择有效的文件路径")
            return

        df = self.read_file(file_path)
        if df is None:
            return

        # Map lowercase column names to uppercase
        column_mapping = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close'
        }

        # Verify data columns
        required_columns = list(column_mapping.values())
        if not all(col in df.columns for col in required_columns):
            print(f"文件缺少必要列: {', '.join(required_columns)}")
            QMessageBox.critical(self, "错误", f"文件缺少必要列: {', '.join(required_columns)}")
            return

        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            df[column_mapping['Date']] = pd.to_datetime(df[column_mapping['Date']])
            df[column_mapping['Date']] = df[column_mapping['Date']].map(mdates.date2num)
            ohlc = df[[column_mapping['Date'], column_mapping['Open'], column_mapping['High'], column_mapping['Low'], column_mapping['Close']]]

            candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')
            ax.xaxis_date()
            self.canvas.draw()
        except Exception as e:
            print(f"绘制 K 线图失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"绘制 K 线图失败: {str(e)}")

    # ... existing code ...
import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from mplfinance.original_flavor import candlestick_ohlc
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QFileDialog,
    QLineEdit, QLabel, QListWidget, QMessageBox
)
from PyQt6.QtCore import Qt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar


class KLineApp(QWidget):
    def __init__(self):
        super().__init__()
        self.csv_paths = []
        self.all_marks = []
        self.model = None
        self.current_marks = []
        self.figure = plt.figure()
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)
        self.initUI()

    def initUI(self):
        # 布局
        main_layout = QVBoxLayout()

        # 文件选择部分
        file_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        file_layout.addWidget(QLabel("选择 CSV 或 TXT 文件:"))
        file_layout.addWidget(self.file_input)
        select_button = QPushButton("选择文件")
        select_button.clicked.connect(self.select_file)
        file_layout.addWidget(select_button)
        main_layout.addLayout(file_layout)

        # 标注列表
        self.mark_list = QListWidget()
        main_layout.addWidget(QLabel("已标注文件列表:"))
        main_layout.addWidget(self.mark_list)

        # 生成 K 线图按钮
        plot_button = QPushButton("生成 K 线图")
        plot_button.clicked.connect(self.plot_candlestick)
        main_layout.addWidget(plot_button)

        # 开始标注按钮
        start_mark_button = QPushButton("开始标注")
        start_mark_button.clicked.connect(self.start_mark)
        main_layout.addWidget(start_mark_button)

        # 构建模型按钮
        build_model_button = QPushButton("构建形态模型")
        build_model_button.clicked.connect(self.build_model)
        main_layout.addWidget(build_model_button)

        # 识别相似形态按钮
        recognize_button = QPushButton("识别相似形态")
        recognize_button.clicked.connect(self.recognize_pattern)
        main_layout.addWidget(recognize_button)

        # 图表显示部分
        main_layout.addWidget(self.toolbar)
        main_layout.addWidget(self.canvas)

        self.setLayout(main_layout)
        self.setWindowTitle('K 线图形态识别')
        self.setGeometry(100, 100, 1200, 800)
        self.show()

    def select_file(self):
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if file_path:
            self.file_input.setText(file_path)

    def read_file(self, file_path):
        try:
            if file_path.endswith('.csv'):
                return pd.read_csv(file_path)
            elif file_path.endswith('.txt'):
                return pd.read_csv(file_path, sep=',')
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")
        return None

    def plot_candlestick(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            print("请选择有效的文件路径")
            QMessageBox.warning(self, "警告", "请选择有效的文件路径")
            return

        df = self.read_file(file_path)
        if df is None:
            return

        # 验证数据列
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close']
        if not all(col in df.columns for col in required_columns):
            print(f"文件缺少必要列: {', '.join(required_columns)}")
            QMessageBox.critical(self, "错误", f"文件缺少必要列: {', '.join(required_columns)}")
            return

        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            df['Date'] = pd.to_datetime(df['Date'])
            df['Date'] = df['Date'].map(mdates.date2num)
            ohlc = df[['Date', 'Open', 'High', 'Low', 'Close']]

            candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')
            ax.xaxis_date()
            self.canvas.draw()
        except Exception as e:
            print(f"绘制 K 线图失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"绘制 K 线图失败: {str(e)}")

    def start_mark(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        self.current_marks = []
        self.figure.canvas.mpl_connect('button_press_event', lambda event: self.on_click(event, df))

    def on_click(self, event, df):
        if event.inaxes is None:
            return

        if len(self.current_marks) >= 5:
            return

        x = event.xdata
        nearest_idx = np.abs(df['Date'] - x).argmin()

        if len(self.current_marks) % 2 == 0:
            y = df['Low'][nearest_idx]
        else:
            y = df['High'][nearest_idx]

        self.current_marks.append(nearest_idx)
        ax = self.figure.gca()
        ax.annotate(f'{len(self.current_marks)}', xy=(x, y), xytext=(x, y + 0.05),
                    arrowprops=dict(facecolor='black', shrink=0.05))
        self.canvas.draw()

        if len(self.current_marks) == 5:
            self.csv_paths.append(self.file_input.text())
            self.all_marks.append(self.current_marks)
            self.mark_list.addItem(os.path.basename(self.file_input.text()))

    def build_model(self):
        if not self.csv_paths or not self.all_marks:
            return

        X = []
        y = []
        for i, (csv_path, marks) in enumerate(zip(self.csv_paths, self.all_marks)):
            df = self.read_file(csv_path)
            if df is None:
                continue

            start_idx = marks[0]
            end_idx = marks[4]
            pattern = df['Close'][start_idx:end_idx + 1].values
            X.append(pattern)
            y.append(i)

        max_length = max(len(p) for p in X)
        X_padded = []
        for pattern in X:
            padded = np.pad(pattern, (0, max_length - len(pattern)), 'constant')
            X_padded.append(padded)

        X = np.array(X_padded)
        self.model = KNeighborsClassifier(n_neighbors=1)
        self.model.fit(X, y)

    def recognize_pattern(self):
        if not self.model:
            return

        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择要识别的 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        max_length = self.model.n_features_in_
        similarities = []
        for i in range(len(df) - max_length + 1):
            pattern = df['Close'][i:i + max_length].values
            pattern = np.pad(pattern, (0, max_length - len(pattern)), 'constant')
            similarity = self.model.predict_proba([pattern])
            similarities.append(similarity.max())
        best_idx = np.argmax(similarities)

        print(f"相似形态起始索引: {best_idx}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = KLineApp()
    sys.exit(app.exec())
