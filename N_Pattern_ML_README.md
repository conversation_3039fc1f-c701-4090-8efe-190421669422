# N字形态机器学习识别系统

这是一个独立的N字形态机器学习识别系统，可以训练模型来识别股票价格图表中的N字形态。

## 功能特点

- **多种输入格式支持**：支持CSV文件和图片文件
- **智能特征提取**：自动提取价格序列和图像特征
- **多模型训练**：支持随机森林和SVM模型
- **批量预测**：可以批量处理多个文件
- **模型保存/加载**：训练好的模型可以保存和重复使用

## 安装依赖

```bash
pip install numpy pandas scikit-learn opencv-python matplotlib joblib scipy
```

## 文件说明

- `n_pattern_ml_trainer.py` - 主要的训练器类
- `n_pattern_example.py` - 使用示例
- `N_Pattern_ML_README.md` - 说明文档

## 使用方法

### 方法1：交互式使用

直接运行主程序：

```bash
python n_pattern_ml_trainer.py
```

然后按照提示选择操作：
1. 训练新模型
2. 使用已有模型预测
3. 创建示例训练数据

### 方法2：快速示例

运行示例程序：

```bash
python n_pattern_example.py
```

这会自动创建示例数据、训练模型并进行预测。

### 方法3：编程使用

```python
from n_pattern_ml_trainer import NPatternMLTrainer

# 创建训练器
trainer = NPatternMLTrainer()

# 训练模型
positive_files = ["path/to/positive/samples/*.csv"]
negative_files = ["path/to/negative/samples/*.csv"]
X, y = trainer.prepare_training_data(positive_files, negative_files)
trainer.train_models(X, y)

# 保存模型
trainer.save_model("my_model.pkl")

# 加载模型并预测
trainer.load_model("my_model.pkl")
result = trainer.predict_csv("test_file.csv")
print(f"是否包含N字形态: {result['is_n_pattern']}")
```

## 训练数据准备

### CSV文件格式

CSV文件应包含价格数据，支持以下列名：
- `close`, `Close` - 收盘价
- `收盘价`, `收盘` - 中文列名

示例CSV格式：
```csv
close
100.5
101.2
102.1
...
```

### 目录结构

训练时需要准备两个目录：

```
training_data/
├── positive/          # 包含N字形态的文件
│   ├── n_pattern_1.csv
│   ├── n_pattern_2.csv
│   └── ...
└── negative/          # 不包含N字形态的文件
    ├── random_1.csv
    ├── random_2.csv
    └── ...
```

## 特征说明

系统会自动提取以下特征：

### 价格特征
- 基本统计：均值、标准差、偏度、峰度
- 趋势特征：整体趋势、最大回撤、波动率
- 形态特征：峰值数量、谷值数量、方向变化
- 技术指标：移动平均、RSI

### 图像特征（如果输入是图片）
- 线段检测：霍夫变换检测直线
- 形态评分：N字形态匹配度
- 纹理特征：梯度、对比度

## 模型评估

训练完成后会显示：
- 交叉验证得分
- 测试集准确率
- 分类报告
- 混淆矩阵

## 预测结果

预测结果包含：
- `is_n_pattern`: 是否包含N字形态（布尔值）
- `confidence`: 置信度（0-1）
- `probability_positive`: 正样本概率
- `probability_negative`: 负样本概率

## 注意事项

1. **数据质量**：确保训练数据质量，正负样本要有明显区别
2. **样本平衡**：正负样本数量尽量平衡
3. **数据量**：建议每类至少10个样本，越多越好
4. **阈值调整**：可以调整判断阈值来平衡精确率和召回率

## 高级用法

### 自定义特征提取

```python
# 可以继承NPatternFeatureExtractor类来添加自定义特征
class CustomFeatureExtractor(NPatternFeatureExtractor):
    def extract_custom_features(self, prices):
        # 添加自定义特征
        return custom_features
```

### 模型调优

```python
# 可以修改模型参数
trainer.models['random_forest'] = RandomForestClassifier(
    n_estimators=200,
    max_depth=10,
    random_state=42
)
```

## 故障排除

1. **导入错误**：确保安装了所有依赖包
2. **文件读取错误**：检查CSV文件格式和编码
3. **训练数据不足**：确保有足够的训练样本
4. **特征提取失败**：检查价格数据是否有效

## 扩展功能

系统设计为可扩展的，您可以：
- 添加新的特征提取方法
- 集成其他机器学习模型
- 支持更多文件格式
- 添加可视化功能
