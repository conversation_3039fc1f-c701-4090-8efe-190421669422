#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的N字形态训练器
直接基于黄色箭头标注的具体形态进行训练
"""

import os
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report
import joblib
from yellow_marker_detector import YellowMarkerDetector

class ImprovedNPatternTrainer:
    """改进的N字形态训练器"""
    
    def __init__(self):
        self.detector = YellowMarkerDetector()
        self.scaler = StandardScaler()
        self.model = None
        
    def extract_yellow_arrow_patterns(self, image_path: str):
        """提取黄色箭头标注的具体形态"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            
            # 检测黄色标识
            detection_result = self.detector.detect_yellow_markers(image_path)
            
            if not detection_result or not detection_result['yellow_detected']:
                return None
            
            # 分析黄色箭头连接的路径
            yellow_mask = detection_result['yellow_mask']
            
            # 找到黄色像素的坐标
            yellow_points = np.where(yellow_mask > 0)
            
            if len(yellow_points[0]) == 0:
                return None
            
            # 按x坐标排序，获得黄色线条的路径
            y_coords = yellow_points[0]
            x_coords = yellow_points[1]
            
            # 创建坐标对并按x排序
            coords = list(zip(x_coords, y_coords))
            coords.sort(key=lambda p: p[0])
            
            # 提取路径
            if len(coords) < 10:
                return None
            
            # 将y坐标转换为价格值（y越小价格越高）
            height = img.shape[0]
            path_prices = []
            path_x = []
            
            # 对x坐标进行分组，每组取y的中位数
            x_groups = {}
            for x, y in coords:
                if x not in x_groups:
                    x_groups[x] = []
                x_groups[x].append(y)
            
            for x in sorted(x_groups.keys()):
                y_median = np.median(x_groups[x])
                price_value = (height - y_median) / height  # 归一化到[0,1]
                path_prices.append(price_value)
                path_x.append(x)
            
            if len(path_prices) < 10:
                return None
            
            return {
                'prices': np.array(path_prices),
                'x_coords': np.array(path_x),
                'image_path': image_path
            }
            
        except Exception as e:
            print(f"提取黄色箭头形态失败 {image_path}: {e}")
            return None
    
    def calculate_n_pattern_features(self, prices: np.array):
        """计算N字形态的关键特征"""
        if len(prices) < 4:
            return None
        
        features = {}
        
        # 1. 寻找关键转折点
        turning_points = self._find_turning_points(prices)
        
        if len(turning_points) < 4:
            return None
        
        # 2. 分析N字形态的四个关键点
        # 取最重要的4个转折点
        key_points = turning_points[:4]
        
        # 3. 计算N字形态特征
        # 高度特征
        heights = [prices[point] for point in key_points]
        features['height_range'] = max(heights) - min(heights)
        features['height_std'] = np.std(heights)
        
        # 时间特征
        time_intervals = np.diff(key_points)
        features['avg_time_interval'] = np.mean(time_intervals)
        features['time_interval_std'] = np.std(time_intervals)
        
        # 斜率特征
        slopes = []
        for i in range(len(key_points) - 1):
            start_idx, end_idx = key_points[i], key_points[i + 1]
            slope = (prices[end_idx] - prices[start_idx]) / (end_idx - start_idx)
            slopes.append(slope)
        
        features['slope_changes'] = len([i for i in range(len(slopes)-1) 
                                       if slopes[i] * slopes[i+1] < 0])
        features['avg_slope_magnitude'] = np.mean(np.abs(slopes))
        features['slope_variation'] = np.std(slopes)
        
        # N字形态的方向模式
        directions = [1 if slope > 0 else -1 for slope in slopes]
        
        # 检查是否符合N字模式（上-下-上 或 下-上-下）
        if len(directions) >= 3:
            n_pattern_score = 0
            # 检查经典N字模式
            for i in range(len(directions) - 2):
                pattern = directions[i:i+3]
                if pattern == [1, -1, 1] or pattern == [-1, 1, -1]:
                    n_pattern_score += 1
            features['n_pattern_score'] = n_pattern_score
        else:
            features['n_pattern_score'] = 0
        
        # 4. 整体形态特征
        features['overall_trend'] = prices[-1] - prices[0]
        features['volatility'] = np.std(np.diff(prices))
        features['price_range'] = np.max(prices) - np.min(prices)
        
        # 5. 形态的对称性
        mid_point = len(prices) // 2
        left_half = prices[:mid_point]
        right_half = prices[mid_point:]
        
        if len(left_half) > 0 and len(right_half) > 0:
            features['symmetry'] = 1 - abs(np.mean(left_half) - np.mean(right_half))
        else:
            features['symmetry'] = 0
        
        return features
    
    def _find_turning_points(self, prices: np.array, min_change: float = 0.02):
        """寻找价格的转折点"""
        turning_points = []
        
        if len(prices) < 3:
            return turning_points
        
        for i in range(1, len(prices) - 1):
            # 检查是否是局部极值
            prev_price = prices[i - 1]
            curr_price = prices[i]
            next_price = prices[i + 1]
            
            # 局部最大值
            if curr_price > prev_price and curr_price > next_price:
                if abs(curr_price - prev_price) > min_change or abs(curr_price - next_price) > min_change:
                    turning_points.append(i)
            
            # 局部最小值
            elif curr_price < prev_price and curr_price < next_price:
                if abs(curr_price - prev_price) > min_change or abs(curr_price - next_price) > min_change:
                    turning_points.append(i)
        
        return turning_points
    
    def prepare_training_data(self, data_dir: str):
        """准备训练数据"""
        # 加载黄色检测结果
        summary_path = os.path.join(data_dir, "yellow_detection_summary.json")
        
        if not os.path.exists(summary_path):
            print("未找到黄色检测结果")
            return None, None
        
        with open(summary_path, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        positive_files = [result['image_path'] for result in summary['results']]
        
        print(f"处理 {len(positive_files)} 个包含黄色标注的图片...")
        
        # 提取正样本特征
        positive_features = []
        
        for i, img_path in enumerate(positive_files):
            print(f"处理 {i+1}/{len(positive_files)}: {os.path.basename(img_path)}")
            
            # 提取黄色箭头标注的形态
            pattern_data = self.extract_yellow_arrow_patterns(img_path)
            
            if pattern_data is not None:
                # 计算特征
                features = self.calculate_n_pattern_features(pattern_data['prices'])
                
                if features is not None:
                    positive_features.append(features)
        
        print(f"成功提取 {len(positive_features)} 个正样本特征")
        
        if len(positive_features) < 5:
            print("正样本太少，无法训练")
            return None, None
        
        # 生成负样本（随机价格序列）
        print("生成负样本...")
        negative_features = []
        
        for i in range(len(positive_features)):
            # 生成随机价格序列
            random_prices = self._generate_random_price_sequence()
            features = self.calculate_n_pattern_features(random_prices)
            
            if features is not None:
                # 修改特征使其不像N字形态
                features['n_pattern_score'] = 0
                features['slope_changes'] = np.random.randint(0, 2)
                negative_features.append(features)
        
        # 合并数据
        all_features = positive_features + negative_features
        labels = [1] * len(positive_features) + [0] * len(negative_features)
        
        # 转换为数组
        import pandas as pd
        df = pd.DataFrame(all_features)
        df = df.fillna(0)
        
        X = self.scaler.fit_transform(df.values)
        y = np.array(labels)
        
        print(f"训练数据准备完成: {len(X)} 个样本, {X.shape[1]} 个特征")
        print(f"正样本: {sum(y)} 个, 负样本: {len(y) - sum(y)} 个")
        
        return X, y
    
    def _generate_random_price_sequence(self, length: int = 50):
        """生成随机价格序列"""
        # 生成随机游走
        changes = np.random.normal(0, 0.02, length)
        prices = np.cumsum(changes)
        
        # 归一化到[0,1]
        prices = (prices - np.min(prices)) / (np.max(prices) - np.min(prices))
        
        return prices
    
    def train_model(self, X: np.array, y: np.array):
        """训练模型"""
        if len(X) < 10:
            print("训练数据太少")
            return None
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练随机森林
        self.model = RandomForestClassifier(n_estimators=200, random_state=42)
        self.model.fit(X_train, y_train)
        
        # 评估
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        print(f"\n训练结果:")
        print(f"训练准确率: {train_score:.3f}")
        print(f"测试准确率: {test_score:.3f}")
        
        # 详细报告
        y_pred = self.model.predict(X_test)
        print("\n分类报告:")
        print(classification_report(y_test, y_pred, target_names=['No N-Pattern', 'Has N-Pattern']))
        
        return self.model
    
    def save_model(self, model_path: str):
        """保存模型"""
        if self.model is None:
            print("没有训练好的模型")
            return
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'model_type': 'improved_n_pattern'
        }
        
        joblib.dump(model_data, model_path)
        print(f"改进模型已保存: {model_path}")

def main():
    """主函数"""
    print("=== 改进的N字形态训练器 ===\n")
    
    data_dir = "D:\\双回撤"
    
    # 创建训练器
    trainer = ImprovedNPatternTrainer()
    
    # 准备训练数据
    X, y = trainer.prepare_training_data(data_dir)
    
    if X is None:
        print("数据准备失败")
        return
    
    # 训练模型
    trainer.train_model(X, y)
    
    # 保存模型
    model_path = "improved_n_pattern_model.pkl"
    trainer.save_model(model_path)
    
    print(f"\n=== 训练完成 ===")
    print(f"改进模型已保存: {model_path}")

if __name__ == "__main__":
    main()
