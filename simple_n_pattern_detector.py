#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的N字形态检测器
不依赖机器学习模型，直接基于价格模式识别
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from typing import List, Dict

class SimpleNPatternDetector:
    """简单的N字形态检测器"""
    
    def __init__(self):
        pass
    
    def load_csv_data(self, csv_path: str) -> np.array:
        """加载CSV数据"""
        try:
            df = pd.read_csv(csv_path)
            
            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', '最高价', 
                           'low', 'Low', '最低价', 'price', 'Price', '价格']
            
            price_col = None
            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break
            
            if price_col is None:
                # 使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    raise ValueError("未找到价格数据列")
            
            prices = df[price_col].values
            prices = prices[~np.isnan(prices)]  # 移除NaN
            
            print(f"加载CSV数据: {len(prices)} 个价格点，使用列: {price_col}")
            return prices
            
        except Exception as e:
            print(f"加载CSV失败 {csv_path}: {e}")
            return None
    
    def find_peaks_and_troughs(self, prices: np.array, prominence: float = None):
        """寻找峰值和谷值"""
        if prominence is None:
            prominence = (np.max(prices) - np.min(prices)) * 0.02  # 2%的价格范围
        
        # 寻找峰值
        peaks, peak_properties = signal.find_peaks(prices, prominence=prominence, distance=5)
        
        # 寻找谷值（通过反转价格序列）
        troughs, trough_properties = signal.find_peaks(-prices, prominence=prominence, distance=5)
        
        return peaks, troughs
    
    def detect_n_patterns(self, prices: np.array, window_size: int = 50):
        """检测N字形态"""
        n_patterns = []
        
        print(f"在 {len(prices)} 个价格点中寻找N字形态...")
        
        # 滑动窗口分析
        for i in range(0, len(prices) - window_size + 1, 5):
            window_prices = prices[i:i + window_size]
            
            # 寻找峰值和谷值
            peaks, troughs = self.find_peaks_and_troughs(window_prices)
            
            # 合并并排序极值点
            extremes = []
            for peak in peaks:
                extremes.append((peak, window_prices[peak], 'peak'))
            for trough in troughs:
                extremes.append((trough, window_prices[trough], 'trough'))
            
            extremes.sort(key=lambda x: x[0])  # 按位置排序
            
            # 检查N字模式
            n_score = self._calculate_n_pattern_score(extremes, window_prices)
            
            if n_score > 0.5:  # 阈值
                pattern = {
                    'start_index': i,
                    'end_index': i + window_size - 1,
                    'window_prices': window_prices,
                    'extremes': extremes,
                    'n_score': n_score,
                    'peaks': peaks + i,  # 调整到全局坐标
                    'troughs': troughs + i
                }
                n_patterns.append(pattern)
        
        print(f"找到 {len(n_patterns)} 个可能的N字形态")
        return n_patterns
    
    def _calculate_n_pattern_score(self, extremes: List, prices: np.array) -> float:
        """计算N字形态得分"""
        if len(extremes) < 4:
            return 0
        
        score = 0
        
        # 检查连续的4个极值点是否形成N字
        for i in range(len(extremes) - 3):
            four_points = extremes[i:i+4]
            
            # 提取类型序列
            types = [point[2] for point in four_points]
            
            # 检查是否是N字模式
            if (types == ['peak', 'trough', 'peak', 'trough'] or 
                types == ['trough', 'peak', 'trough', 'peak']):
                
                # 计算形态的清晰度
                positions = [point[0] for point in four_points]
                heights = [point[1] for point in four_points]
                
                # 检查时间间隔是否合理
                time_intervals = np.diff(positions)
                if all(interval > 3 for interval in time_intervals):  # 最小间隔
                    
                    # 检查高度变化是否明显
                    height_range = max(heights) - min(heights)
                    price_range = np.max(prices) - np.min(prices)
                    
                    if height_range > price_range * 0.1:  # 至少10%的价格范围
                        # 计算得分
                        time_score = min(np.mean(time_intervals) / len(prices), 1.0)
                        height_score = min(height_range / price_range, 1.0)
                        pattern_score = time_score * height_score
                        
                        score = max(score, pattern_score)
        
        return score
    
    def visualize_patterns(self, csv_path: str, n_patterns: List[Dict], save_plot: bool = False):
        """可视化N字形态"""
        if not n_patterns:
            print("没有找到N字形态可视化")
            return
        
        # 加载完整数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(15, 8))
        
        # 绘制完整价格走势
        ax.plot(prices, 'b-', linewidth=1, alpha=0.7, label='Price')
        ax.set_title(f'Simple N-Pattern Detection: {os.path.basename(csv_path)}')
        ax.set_ylabel('Price')
        ax.set_xlabel('Time')
        ax.grid(True, alpha=0.3)
        
        # 标记N字形态
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        for i, pattern in enumerate(n_patterns[:5]):  # 最多显示5个
            start_idx = pattern['start_index']
            end_idx = pattern['end_index']
            color = colors[i % len(colors)]
            
            # 高亮显示N字形态区域
            ax.axvspan(start_idx, end_idx, alpha=0.2, color=color)
            ax.plot(range(start_idx, end_idx + 1), pattern['window_prices'], 
                    color=color, linewidth=3, alpha=0.8,
                    label=f'N-Pattern {i+1} (score: {pattern["n_score"]:.2f})')
            
            # 标记极值点
            for extreme in pattern['extremes']:
                pos, height, type_ = extreme
                global_pos = start_idx + pos
                marker = '^' if type_ == 'peak' else 'v'
                marker_color = 'red' if type_ == 'peak' else 'blue'
                ax.scatter(global_pos, height, marker=marker, color=marker_color, s=50, alpha=0.8)
        
        ax.legend()
        plt.tight_layout()
        
        if save_plot:
            plot_name = f"{os.path.splitext(os.path.basename(csv_path))[0]}_simple_n_patterns.png"
            plt.savefig(plot_name, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {plot_name}")
        
        plt.show()
    
    def analyze_csv(self, csv_path: str, window_size: int = 50):
        """分析CSV文件"""
        print(f"\n=== 分析文件: {os.path.basename(csv_path)} ===")
        
        # 加载数据
        prices = self.load_csv_data(csv_path)
        if prices is None:
            return []
        
        # 检测N字形态
        n_patterns = self.detect_n_patterns(prices, window_size)
        
        if n_patterns:
            print(f"\n找到 {len(n_patterns)} 个N字形态:")
            for i, pattern in enumerate(n_patterns):
                print(f"  N字形态 {i+1}: 位置 {pattern['start_index']}-{pattern['end_index']}, "
                      f"得分 {pattern['n_score']:.3f}")
            
            # 可视化
            self.visualize_patterns(csv_path, n_patterns, save_plot=True)
        else:
            print("未找到N字形态")
        
        return n_patterns

def main():
    """主函数"""
    print("=== 简单N字形态检测器 ===\n")
    print("这是一个基于价格模式的简单检测器，不依赖机器学习模型")
    
    # 创建检测器
    detector = SimpleNPatternDetector()
    
    # 输入文件路径
    csv_path = input("请输入CSV文件路径: ").strip()
    if not os.path.exists(csv_path):
        print("文件不存在!")
        return
    
    window_size = int(input("请输入分析窗口大小 (建议30-100): ").strip() or "50")
    
    # 分析文件
    n_patterns = detector.analyze_csv(csv_path, window_size)
    
    if n_patterns:
        print(f"\n检测完成! 找到 {len(n_patterns)} 个N字形态")
        print("图表已显示并保存")
    else:
        print("\n未找到明显的N字形态")
        print("建议:")
        print("1. 调整窗口大小")
        print("2. 检查数据质量")
        print("3. 确认数据中确实存在N字形态")

if __name__ == "__main__":
    main()
