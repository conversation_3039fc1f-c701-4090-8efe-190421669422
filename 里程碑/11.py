import sys
import os
import ctypes
import logging
import traceback
import math
import numpy as np
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QSlider, QFileDialog, QMessageBox,
    QDialog, QTextEdit, QGroupBox, QMenu, QProgressBar
)
from PyQt6.QtCore import Qt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.widgets import Cursor
import warnings

# 全局变量，用于跟踪六方图窗口
_hexagon_window = None

warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib.font_manager")
import matplotlib as mpl

# 设置 Matplotlib 的日志级别为 WARNING，抑制 DEBUG 信息
mpl.set_loglevel('WARNING')

# === Windows 特定修复 ===
os.environ['QT_QPA_PLATFORM'] = 'windows'  # 强制使用Windows平台
ctypes.CDLL('ucrtbase.dll')._configthreadlocale(-1)  # 修复内存管理

# === 日志配置 ===
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kline_debug.log'),
        logging.StreamHandler()
    ]
)

# 设置支持CJK字符的字体
plt.rcParams['font.family'] = ['SimHei', 'Microsoft YaHei']  # 多字体回退
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


class StableKLineApp(QMainWindow):
    def __init__(self):
        super().__init__()
        # 基本属性初始化
        self.data = None
        self.current_subset = None
        self.double_top_pairs = []
        self.figure = Figure(figsize=(20, 10))
        self.canvas = FigureCanvas(self.figure)
        self.ax = None
        self.cursor = None
        self.info_label = None
        self.code_label = None
        self.manual_points = {'high': [], 'low': []}
        self.is_weekly = False
        self.last_click_pos = None
        self.cursor_label = QLabel(self)
        self.status_label = None  # 添加状态标签
        self.selected_high_point = None  # 用于存储选中的高点
        self.trend_lines = []  # 用于存储趋势线
        self.is_drawing_line = False
        self.line_start_point = None
        self.high_point_buttons = []
        self.low_point_buttons = []
        self.points_panel = None
        self.hexagon_window = None  # 用于存储六方图窗口
        self.digits = []  # 用于存储六方图数据

        # 初始化UI
        self.init_ui()

        # 设置事件处理器
        self.setup_event_handlers()

        # 创建初始空白图表
        self.ax = self.figure.add_subplot(111)
        self.ax.set_title('请加载数据文件')
        self.canvas.draw()

    def init_ui(self):
        """初始化UI组件"""
        # 主窗口设置
        self.setWindowTitle('K线图分析工具')
        self.setGeometry(100, 100, 1600, 900)

        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)

        # 在最上方添加点位面板
        self.points_panel = QWidget()
        points_layout = QVBoxLayout(self.points_panel)
        points_layout.setSpacing(2)
        points_layout.setContentsMargins(2, 2, 2, 2)

        # 创建高点分组（水平布局）
        high_group = QGroupBox("高点")
        high_group.setObjectName("高点")
        high_group.setMaximumHeight(60)  # 限制高度
        high_layout = QHBoxLayout()  # 改为水平布局
        high_layout.setSpacing(1)
        high_layout.setContentsMargins(2, 2, 2, 2)
        high_group.setLayout(high_layout)
        points_layout.addWidget(high_group)

        # 创建低点分组（水平布局）
        low_group = QGroupBox("低点")
        low_group.setObjectName("低点")
        low_group.setMaximumHeight(60)  # 限制高度
        low_layout = QHBoxLayout()  # 改为水平布局
        low_layout.setSpacing(1)
        low_layout.setContentsMargins(2, 2, 2, 2)
        low_group.setLayout(low_layout)
        points_layout.addWidget(low_group)

        # 将点位面板添加到主布局的最上方
        layout.addWidget(self.points_panel)

        # 添加图表
        self.figure = Figure(figsize=(20, 10))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # === 信息显示区域 ===
        info_widget = QWidget()
        info_layout = QHBoxLayout(info_widget)

        # 添加信息标签
        self.info_label = QLabel("时间: N/A, 价格: N/A")
        info_layout.addWidget(self.info_label)

        # 添加代码标签
        self.code_label = QLabel("代码: N/A")
        info_layout.addWidget(self.code_label)

        layout.addWidget(info_widget)

        # === 控制区域 ===
        control_widget = QWidget()
        control_layout = QHBoxLayout(control_widget)

        # 添加加载数据按钮
        load_btn = QPushButton("加载数据")
        load_btn.clicked.connect(self.load_data)
        control_layout.addWidget(load_btn)

        # 添加切换周期按钮
        toggle_btn = QPushButton("切换周期")
        toggle_btn.clicked.connect(self.toggle_period)
        control_layout.addWidget(toggle_btn)

        # 添加滑块
        self.slider = QSlider(Qt.Orientation.Horizontal)
        self.slider.setMinimum(30)  # 最小显示30天
        self.slider.setMaximum(100)
        self.slider.setValue(100)
        self.slider.valueChanged.connect(lambda x: self.safe_update_view(x))
        control_layout.addWidget(self.slider)

        # 添加自动检测按钮
        self.detect_btn = QPushButton("自动检测高低点")
        self.detect_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.detect_btn.clicked.connect(lambda: self.detect_high_low_points(window_size=20, threshold=0.02))
        control_layout.addWidget(self.detect_btn)

        # 添加清除标记按钮
        self.clear_marks_btn = QPushButton("清除所有标记")
        self.clear_marks_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.clear_marks_btn.clicked.connect(self.clear_marked_points)
        control_layout.addWidget(self.clear_marks_btn)

        # 添加寻找最佳起点按钮
        self.find_start_btn = QPushButton("寻找最佳起点")
        self.find_start_btn.setStyleSheet("""
            QPushButton {
                background-color: #f1c40f;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #f39c12;
            }
        """)
        self.find_start_btn.clicked.connect(self.find_best_start_point)
        control_layout.addWidget(self.find_start_btn)

        # 添加显示六方图按钮
        self.show_hexagon_btn = QPushButton("显示六方图")
        self.show_hexagon_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.show_hexagon_btn.clicked.connect(self.show_hexagon_chart)
        control_layout.addWidget(self.show_hexagon_btn)

        # 添加均线匹配按钮
        self.match_ma_btn = QPushButton("匹配最优均线")
        self.match_ma_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.match_ma_btn.clicked.connect(self.match_optimal_ma)
        control_layout.addWidget(self.match_ma_btn)

        # 添加时间差计算按钮
        self.calc_time_btn = QPushButton("计算时间差")
        self.calc_time_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        self.calc_time_btn.clicked.connect(self.show_time_differences)
        control_layout.addWidget(self.calc_time_btn)

        # 添加画线工具按钮
        self.draw_line_btn = QPushButton("画线工具")
        self.draw_line_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.draw_line_btn.clicked.connect(self.toggle_draw_line_mode)
        control_layout.addWidget(self.draw_line_btn)

        # 在画线按钮旁边添加删除趋势线按钮
        self.delete_lines_btn = QPushButton("删除趋势线")
        self.delete_lines_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.delete_lines_btn.clicked.connect(self.show_delete_lines_dialog)
        control_layout.addWidget(self.delete_lines_btn)

        # 添加状态标签
        self.status_label = QLabel("请加载数据文件")
        control_layout.addWidget(self.status_label)

        # 在控制区域添加显示六方图按钮
        self.hexagon_btn = QPushButton("显示六方图")
        self.hexagon_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.hexagon_btn.clicked.connect(self.create_hexagon_chart)
        control_layout.addWidget(self.hexagon_btn)

        layout.addWidget(control_widget)

        # 添加时间尺按钮
        self.time_ruler_btn = QPushButton("时间尺")
        self.time_ruler_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.time_ruler_btn.clicked.connect(self.toggle_time_ruler_mode)
        control_layout.addWidget(self.time_ruler_btn)

        # 初始化时间尺相关变量
        self.is_time_ruler_mode = False
        self.time_ruler_start = None
        self.time_ruler_end = None
        self.time_ruler_line = None
        self.time_rulers = []  # 存储所有时间尺
        self.selected_ruler = None  # 当前选中的时间尺

        # 初始化按钮列表
        self.high_point_buttons = []
        self.low_point_buttons = []

    def setup_event_handlers(self):
        """设置所有事件处理器"""
        # 鼠标点击事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_click)
        # 鼠标释放事件
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        # 右键菜单
        self.canvas.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.canvas.customContextMenuRequested.connect(self.show_context_menu)
        # 鼠标移动事件
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

    def load_and_validate_data(self, path):
        """加载并验证数据"""
        logging.info(f"开始加载文件: {path}")

        try:
            # 读取数据文件
            raw_df = pd.read_csv(path, encoding='gbk')

            # 统一列名为标准格式（首字母大写）
            column_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume',
                'amount': 'Amount',
                'date': 'Date',
                '日期': 'Date',
                '开盘': 'Open',
                '最高': 'High',
                '最低': 'Low',
                '收盘': 'Close',
                '成交量': 'Volume',
                '成交额': 'Amount'
            }

            # 将所有列名转换为小写以进行匹配
            raw_df.columns = raw_df.columns.str.lower()

            # 重命名列
            raw_df.rename(columns=column_mapping, inplace=True)

            # 确保必要的列存在
            required_columns = ['Open', 'High', 'Low', 'Close']
            missing = [col for col in required_columns if col not in raw_df.columns]
            if missing:
                raise ValueError(f"缺少必要列: {', '.join(missing)}")

            # 如果没有Volume和Amount列，添加默认值
            if 'Volume' not in raw_df.columns:
                raw_df['Volume'] = 0
            if 'Amount' not in raw_df.columns:
                raw_df['Amount'] = 0

            # 转换日期列并设置索引
            if 'Date' in raw_df.columns:
                raw_df['Date'] = pd.to_datetime(raw_df['Date'])
                raw_df.set_index('Date', inplace=True)

            # 按日期排序
            self.data = raw_df.sort_index()

            # 设置滑块范围
            total_days = len(self.data)
            self.slider.setRange(30, total_days)  # 最小显示30天
            self.slider.setValue(min(300, total_days))  # 默认显示300天或全部数据
            self.current_subset = self.data.iloc[-self.slider.value():]

            logging.info(f"数据加载成功，共 {len(self.data)} 条记录")
            logging.info(f"数据列名: {', '.join(self.data.columns)}")

        except Exception as e:
            error_msg = f"数据加载失败: {str(e)}\n文件路径: {path}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            raise ValueError(error_msg)

    def load_data(self):
        """加载数据文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择数据文件",
                "",
                "CSV和TXT文件 (*.csv *.txt)"  # 修改这里，合并为一个过滤器
            )

            if file_path:
                self.load_and_validate_data(file_path)
                self.code_label.setText(f"代码: {os.path.basename(file_path).split('.')[0]}")
                self.safe_update_view(self.slider.value())

        except Exception as e:
            self.show_error_dialog(str(e))

    def analyze_weekly_data(self):
        """分析周数据 - 固定的周线划分方式"""
        try:
            if self.data is None:
                return None

            weekly_data = []
            df = self.data.copy()

            # 保持现有的周数划分方式
            df['week_num'] = df.index.isocalendar().week
            df['year'] = df.index.isocalendar().year

            # 按年和周数分组，保持现有的正确划分
            for (year, week), week_group in df.groupby(['year', 'week_num']):
                if not week_group.empty:
                    # 只处理工作日数据（周一到周五）
                    week_group = week_group[week_group.index.dayofweek < 5]
                    if len(week_group) > 0:
                        first_day = week_group.iloc[0]
                        last_day = week_group.iloc[-1]

                        # 计算该周的涨幅
                        change_pct = ((last_day['Close'] - first_day['Open']) / first_day['Open'] * 100)

                        weekly_row = {
                            'Date': week_group.index[0],  # 使用实际的第一个交易日作为索引
                            'Open': first_day['Open'],
                            'High': week_group['High'].max(),
                            'Low': week_group['Low'].min(),
                            'Close': last_day['Close'],
                            'TradingDays': len(week_group),
                            'FirstDay': week_group.index[0],
                            'LastDay': week_group.index[-1],
                            'Change': change_pct,
                            'WeekNum': week
                        }

                        # 记录周数据计算过程（保持现有的日志格式）
                        logging.info(
                            f"周数据计算 - 第{week}周 - "
                            f"起始日: {weekly_row['FirstDay'].strftime('%Y-%m-%d')} "
                            f"结束日: {weekly_row['LastDay'].strftime('%Y-%m-%d')} "
                            f"开盘: {weekly_row['Open']:.2f} "
                            f"收盘: {weekly_row['Close']:.2f} "
                            f"涨幅: {weekly_row['Change']:.2f}% "
                            f"交易天数: {weekly_row['TradingDays']}"
                        )

                        weekly_data.append(weekly_row)

            # 转换为DataFrame并按日期排序
            weekly_df = pd.DataFrame(weekly_data).set_index('Date').sort_index()
            return weekly_df

        except Exception as e:
            logging.error(f"周数据分析失败: {str(e)}")
            logging.error(traceback.format_exc())
            return None

    def safe_update_view(self, value):
        """安全更新视图"""
        try:
            if self.data is not None and len(self.data) > 0:
                # 确保value不超过数据长度
                value = min(value, len(self.data))

                # 从末尾开始取指定数量的数据
                self.current_subset = self.data.iloc[-value:].copy()

                if self.is_weekly:
                    # 使用新的周数据分析方法
                    weekly_data = self.analyze_weekly_data()
                    if weekly_data is not None:
                        # 只保留最近的value个周数据
                        self.current_subset = weekly_data.iloc[-min(value, len(weekly_data)):]

                # 如果是周线模式，计算涨幅大于20%的K线数量
                high_change_count = 0
                if self.is_weekly and 'Change' in self.current_subset.columns:
                    high_change_count = (self.current_subset['Change'] > 20).sum()
                    logging.info(f"周线模式: 发现{high_change_count}根涨幅大于20%的K线")

                self.safe_plot_chart(show_double_tops=self.is_weekly, calculate_ma=False)

                # 更新状态栏
                status_text = f"显示{'周' if self.is_weekly else '日'}K线 | 数据量: {len(self.current_subset)}条"

                # 如果是周线模式且有涨幅大于20%的K线，显示数量
                if self.is_weekly and high_change_count > 0:
                    status_text += f" | 涨幅>20%: {high_change_count}根"

                self.status_label.setText(status_text)

        except Exception as e:
            logging.error(f"视图更新失败: {str(e)}")
            self.show_error_dialog(f"更新视图失败: {str(e)}")

    def find_optimal_ma_periods(self):
        """寻找最优的均线周期组合"""
        if not self.manual_points['high'] and not self.manual_points['low']:
            return 20, 60  # 如果没有标记点，返回默认值

        try:
            # 获取所有标记点的日期
            marked_dates = []
            for point_type in ['high', 'low']:
                marked_dates.extend([point['date'] for point in self.manual_points[point_type]])
            marked_dates = sorted(marked_dates)

            best_score = 0
            best_periods = (20, 60)  # 默认值

            # 测试不同的均线组合
            for ma1 in range(5, 51, 1):  # 5到50，步长1
                for ma2 in range(51, 201, 1):  # 51到200，步长1
                    # 计算均线
                    ma1_series = self.current_subset['Close'].rolling(window=ma1).mean()
                    ma2_series = self.current_subset['Close'].rolling(window=ma2).mean()

                    # 找出均线交叉点
                    crosses = []
                    for i in range(1, len(self.current_subset)):
                        if ((ma1_series.iloc[i - 1] <= ma2_series.iloc[i - 1] and
                             ma1_series.iloc[i] > ma2_series.iloc[i]) or
                                (ma1_series.iloc[i - 1] >= ma2_series.iloc[i - 1] and
                                 ma1_series.iloc[i] < ma2_series.iloc[i])):
                            crosses.append(self.current_subset.index[i])

                    # 计算交叉点与标记点的匹配程度
                    score = 0
                    for cross in crosses:
                        for marked in marked_dates:
                            # 如果交叉点在标记点5个交易日内，增加分数
                            if abs((cross - marked).days) <= 5:
                                score += 1
                                break

                    if score > best_score:
                        best_score = score
                        best_periods = (ma1, ma2)

            logging.info(f"找到最优均线组合: MA{best_periods[0]}, MA{best_periods[1]}, 匹配分数: {best_score}")
            return best_periods

        except Exception as e:
            logging.error(f"寻找最优均线失败: {str(e)}")
            return 20, 60  # 发生错误时返回默认值

    def safe_plot_chart(self, show_double_tops=False, calculate_ma=False):
        """安全绘制图表"""
        try:
            if self.current_subset is None or len(self.current_subset) == 0:
                return

            self.figure.clear()
            self.ax = self.figure.add_subplot(111)

            # 绘制K线图
            mpf.plot(
                self.current_subset,
                type='candle',
                style=mpf.make_mpf_style(
                    marketcolors=mpf.make_marketcolors(
                        up='red',
                        down='green',
                        edge='inherit',
                        wick='inherit'
                    )
                ),
                ax=self.ax,
                volume=False
            )

            # 如果是周线模式，在K线图上标记涨幅大于20%的K线
            if self.is_weekly and 'Change' in self.current_subset.columns:
                # 找出涨幅大于20%的K线
                high_change_indices = self.current_subset[self.current_subset['Change'] > 20].index

                if len(high_change_indices) > 0:
                    # 为每个涨幅大于20%的K线添加橘红色背景
                    for date in high_change_indices:
                        idx = self.current_subset.index.get_loc(date)
                        row = self.current_subset.loc[date]

                        # 绘制橘红色矩形标记
                        self.ax.add_patch(
                            plt.Rectangle(
                                (idx-0.4, row['Low']*0.99),  # 左下角坐标
                                0.8,  # 宽度
                                row['High']*1.01 - row['Low']*0.99,  # 高度
                                facecolor='orange',  # 填充颜色
                                alpha=0.3,  # 透明度
                                zorder=0  # 确保在K线下层
                            )
                        )

                    # 添加图例说明
                    from matplotlib.patches import Patch
                    legend_elements = [Patch(facecolor='orange', edgecolor='orange', alpha=0.3, label='涨幅>20%')]
                    self.ax.legend(handles=legend_elements, loc='upper left')

            # 只在点击匹配按钮时计算均线
            if calculate_ma:
                # 计算并绘制最优均线
                ma1_period, ma2_period = self.find_optimal_ma_periods()
                ma1 = self.current_subset['Close'].rolling(window=ma1_period).mean()
                ma2 = self.current_subset['Close'].rolling(window=ma2_period).mean()

                # 绘制均线
                self.ax.plot(range(len(ma1)), ma1, label=f'MA{ma1_period}', color='blue', linewidth=1)
                self.ax.plot(range(len(ma2)), ma2, label=f'MA{ma2_period}', color='orange', linewidth=1)
                self.ax.legend()

            # 绘制标记点和标注
            for point_type, points in self.manual_points.items():
                if points:
                    for point in points:
                        if point['date'] in self.current_subset.index:
                            idx = self.current_subset.index.get_loc(point['date'])
                            marker = 'v' if point_type == 'high' else '^'  # 反转三角形方向
                            color = 'red' if point_type == 'high' else 'green'

                            # 计算标记点的位置
                            y_pos = point['price'] + (0.002 * point['price']) if point_type == 'high' else point[
                                                                                                               'price'] - (
                                                                                                                       0.002 *
                                                                                                                       point[
                                                                                                                           'price'])

                            # 绘制标记点
                            self.ax.plot(
                                idx,
                                y_pos,  # 使用调整后的位置
                                marker,
                                color=color,
                                markersize=4  # 从6改成4
                            )

                            # 添加价格标注（周线模式下只显示价格）
                            text = f"{point['price']:.2f}"  # 移除条件判断，始终只显示价格
                            y_offset = 10 if point_type == 'high' else -20
                            self.ax.annotate(
                                text,
                                xy=(idx, y_pos),  # 使用调整后的位置
                                xytext=(0, y_offset),
                                textcoords='offset points',
                                ha='center',
                                va='bottom' if point_type == 'high' else 'top',
                                fontsize=8,
                                bbox=dict(
                                    boxstyle='round,pad=0.3',
                                    fc='yellow',
                                    alpha=0.5
                                ),
                                arrowprops=dict(
                                    arrowstyle='->',
                                    connectionstyle='arc3,rad=0',
                                    color=color
                                )
                            )

            # 绘制趋势线
            for trend_line in self.trend_lines:
                try:
                    start_date = trend_line['start']['date']
                    end_date = trend_line['end']['date']

                    # 如果起点或终点不在当前数据范围内，跳过这条线
                    if start_date < self.current_subset.index[0] or \
                            start_date > self.current_subset.index[-1] or \
                            end_date < self.current_subset.index[0] or \
                            end_date > self.current_subset.index[-1]:
                        continue

                    start_idx = self.current_subset.index.get_loc(start_date)
                    end_idx = self.current_subset.index.get_loc(end_date)

                    # 判断是顶部还是底部趋势线
                    is_top_line = trend_line['start'] in self.manual_points['high']
                    line_color = 'red' if is_top_line else 'green'

                    # 计算斜率
                    slope = (trend_line['end']['price'] - trend_line['start']['price']) / (end_idx - start_idx)

                    # 绘制实线部分（从起点到终点）
                    self.ax.plot(
                        [start_idx, end_idx],
                        [trend_line['start']['price'], trend_line['end']['price']],
                        '-',
                        color=line_color,
                        linewidth=1,
                        alpha=0.8
                    )

                    # 绘制延长线部分（从终点到数据集末尾）
                    if 'extended_end' in trend_line and trend_line['extended_end']:
                        # 计算当前视图中的延长线终点
                        max_x = len(self.current_subset) - 1
                        extended_y = trend_line['end']['price'] + slope * (max_x - end_idx)

                        self.ax.plot(
                            [end_idx, max_x],
                            [trend_line['end']['price'], extended_y],
                            '--',
                            color=line_color,
                            linewidth=1,
                            alpha=0.5
                        )

                        # 在延长线末端添加斜率标注
                        self.ax.annotate(
                            f'斜率: {slope:.4f}',
                            xy=(max_x, extended_y),
                            xytext=(5, 5),
                            textcoords='offset points',
                            color=line_color,
                            bbox=dict(facecolor='white', edgecolor=line_color, alpha=0.7),
                            fontsize=8
                        )
                except Exception as e:
                    continue

            # 如果有选中的起始高点，给它特殊标记
            if self.selected_high_point and self.selected_high_point['date'] in self.current_subset.index:
                idx = self.current_subset.index.get_loc(self.selected_high_point['date'])
                self.ax.plot(
                    idx,
                    self.selected_high_point['price'],
                    'o',  # 圆形标记
                    color='yellow',
                    markersize=8,
                    markeredgecolor='black'
                )

            # 添加网格
            self.ax.grid(True, linestyle='--', alpha=0.3)

            # 更新图表
            self.canvas.draw()

            # 更新高低点按钮
            self.update_point_buttons()

        except Exception as e:
            logging.error(f"绘制图表失败: {str(e)}")
            self.show_error_dialog(f"绘制图表失败: {str(e)}")

    def toggle_period(self):
        """切换显示周期（日线/周线）"""
        if self.data is not None:
            self.is_weekly = not self.is_weekly
            self.safe_update_view(self.slider.value())

    def on_mouse_move(self, event):
        """处理鼠标移动事件"""
        if not event.inaxes or self.current_subset is None:
            return

        x, y = event.xdata, event.ydata
        if x is not None and y is not None:
            try:
                date_idx = int(x)
                if 0 <= date_idx < len(self.current_subset):
                    date = self.current_subset.index[date_idx].strftime('%Y-%m-%d')
                    row = self.current_subset.iloc[date_idx]
                    self.info_label.setText(
                        f"日期: {date} | "
                        f"开盘: {row['Open']:.2f} | "
                        f"最高: {row['High']:.2f} | "
                        f"最低: {row['Low']:.2f} | "
                        f"收盘: {row['Close']:.2f}"
                    )

                    # 如果在时间尺模式下且已经选择了起点，实时绘制时间尺
                    if self.is_time_ruler_mode and self.time_ruler_start is not None:
                        # 更新时间尺终点
                        self.time_ruler_end = {
                            'x': date_idx,
                            'y': y,
                            'date': self.current_subset.index[date_idx],
                            'price': y
                        }

                        # 重绘图表显示时间尺
                        self.draw_time_ruler()
            except Exception as e:
                logging.debug(f"鼠标移动事件处理错误: {str(e)}")

    def on_mouse_click(self, event):
        """处理鼠标点击事件"""
        if event.inaxes != self.ax:
            return

        if self.is_drawing_line:
            self.handle_draw_line_click(event)
        elif self.is_time_ruler_mode:
            # 处理时间尺模式下的点击
            self.handle_time_ruler_click(event)
        else:
            try:
                if event.button == 1:  # 左键点击
                    self.handle_left_click(event)
                elif event.button == 3:  # 右键点击
                    # 移除对 handle_trend_line_start 的调用
                    self.last_click_pos = event  # 仅保存位置用于显示右键菜单
            except Exception as e:
                logging.error(f"处理鼠标点击事件失败: {str(e)}")

        self.last_click_pos = event

    def handle_time_ruler_click(self, event):
        """处理时间尺模式下的点击事件"""
        try:
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                date_idx = int(x)
                if 0 <= date_idx < len(self.current_subset):
                    # 记录时间尺的起点
                    self.time_ruler_start = {
                        'x': date_idx,
                        'y': y,
                        'date': self.current_subset.index[date_idx],
                        'price': y
                    }
                    self.status_label.setText("时间尺模式：已选择起点，请拖动到终点并释放")
        except Exception as e:
            logging.error(f"处理时间尺点击事件失败: {str(e)}")

    def on_mouse_release(self, event):
        """处理鼠标释放事件"""
        try:
            # 如果在时间尺模式下且已经选择了起点和终点
            if self.is_time_ruler_mode and self.time_ruler_start is not None and self.time_ruler_end is not None:
                # 计算两点之间的K线数量（与最佳起点功能保持一致）
                start_date = self.time_ruler_start['date']
                end_date = self.time_ruler_end['date']

                # 使用与最佳起点功能相同的方法计算周数
                if self.is_weekly:
                    # 如果是周K线，使用与最佳起点功能相同的方法计算周数
                    k_line_count = len(pd.date_range(start=start_date, end=end_date, freq='W'))
                else:
                    # 如果是日K线，计算交易日数量
                    k_line_count = abs(self.time_ruler_end['x'] - self.time_ruler_start['x'])

                # 绘制最终的时间尺并显示K线数量
                self.draw_time_ruler(is_final=True, k_line_count=k_line_count)

                # 重置时间尺起点，但保留终点以便于显示
                self.time_ruler_start = None

                # 更新状态栏
                period_text = "周" if self.is_weekly else "日"
                self.status_label.setText(f"时间尺模式：测量完成，共 {k_line_count} 根{period_text}K线")
        except Exception as e:
            logging.error(f"处理鼠标释放事件失败: {str(e)}")

    def draw_time_ruler(self, is_final=False, k_line_count=None):
        """绘制时间尺"""
        try:
            if self.time_ruler_start is None and self.time_ruler_end is None and not self.time_rulers:
                return

            # 不重绘图表，只添加时间尺
            # 先清除之前的时间尺线条，但保留其他元素
            if hasattr(self, 'time_ruler_artists') and self.time_ruler_artists:
                for artist in self.time_ruler_artists:
                    if artist in self.ax.lines or artist in self.ax.texts:
                        artist.remove()
                self.time_ruler_artists = []
            else:
                self.time_ruler_artists = []

            # 绘制已保存的时间尺
            for ruler in self.time_rulers:
                # 绘制水平线
                line1 = self.ax.plot([ruler['start_x'], ruler['end_x']], [ruler['mid_y'], ruler['mid_y']], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line1)

                # 绘制垂直线
                line2 = self.ax.plot([ruler['start_x'], ruler['start_x']], [ruler['start_y'], ruler['mid_y']], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line2)
                line3 = self.ax.plot([ruler['end_x'], ruler['end_x']], [ruler['end_y'], ruler['mid_y']], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line3)

                # 显示K线数量
                period_text = "周" if self.is_weekly else "日"
                text = self.ax.annotate(
                    f"{ruler['k_line_count']} 根{period_text}K线",
                    xy=(ruler['mid_x'], ruler['mid_y']),
                    xytext=(0, 5),
                    textcoords='offset points',
                    ha='center',
                    va='bottom',
                    fontsize=10,
                    bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.7)
                )
                self.time_ruler_artists.append(text)

            # 绘制当前正在创建的时间尺
            if self.time_ruler_start is not None and self.time_ruler_end is not None:
                # 获取起点和终点的坐标
                start_x = self.time_ruler_start['x']
                start_y = self.time_ruler_start['y']
                end_x = self.time_ruler_end['x']
                end_y = self.time_ruler_end['y']

                # 计算中点坐标（用于显示K线数量）
                mid_x = (start_x + end_x) / 2
                mid_y = max(start_y, end_y) + (self.ax.get_ylim()[1] - self.ax.get_ylim()[0]) * 0.05  # 在上方显示

                # 绘制时间尺的水平线
                line1 = self.ax.plot([start_x, end_x], [mid_y, mid_y], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line1)

                # 绘制起点和终点的垂直线
                line2 = self.ax.plot([start_x, start_x], [start_y, mid_y], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line2)
                line3 = self.ax.plot([end_x, end_x], [end_y, mid_y], 'r-', linewidth=1.5)[0]
                self.time_ruler_artists.append(line3)

                # 如果是最终绘制，显示K线数量并保存时间尺
                if is_final and k_line_count is not None:
                    period_text = "周" if self.is_weekly else "日"
                    text = self.ax.annotate(
                        f"{k_line_count} 根{period_text}K线",
                        xy=(mid_x, mid_y),
                        xytext=(0, 5),
                        textcoords='offset points',
                        ha='center',
                        va='bottom',
                        fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.7)
                    )
                    self.time_ruler_artists.append(text)

                    # 保存时间尺
                    self.time_rulers.append({
                        'start_x': start_x,
                        'start_y': start_y,
                        'end_x': end_x,
                        'end_y': end_y,
                        'mid_x': mid_x,
                        'mid_y': mid_y,
                        'k_line_count': k_line_count,
                        'id': len(self.time_rulers)  # 为每个时间尺分配一个唯一ID
                    })

            # 刷新画布
            self.canvas.draw()
        except Exception as e:
            logging.error(f"绘制时间尺失败: {str(e)}")

    def handle_left_click(self, event):
        """处理左键点击事件"""
        try:
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                date_idx = int(x)
                if 0 <= date_idx < len(self.current_subset):
                    date = self.current_subset.index[date_idx]
                    row = self.current_subset.iloc[date_idx]

                    # 判断点击位置是高点还是低点
                    if event.ydata > row['Close']:  # 点击在收盘价上方
                        point_type = 'high'
                        # 吸附到当天的最高价
                        snap_price = row['High']
                    else:  # 点击在收盘价下方
                        point_type = 'low'
                        # 吸附到当天的最低价
                        snap_price = row['Low']

                    # 添加新的标记点（使用吸附后的价格）
                    self.manual_points[point_type].append({
                        'date': date,
                        'date_dt': date_idx,  # 添加 date_dt 字段
                        'price': snap_price
                    })

                    # 重绘图表
                    self.safe_plot_chart(show_double_tops=self.is_weekly)

                    # 不再自动显示六方图，用户需要点击"显示六方图"按钮
                    # 如果六方图窗口已经打开，可以提示用户点击按钮更新
                    if hasattr(self, 'hexagon_window') and self.hexagon_window is not None:
                        QMessageBox.information(self, "提示", "已添加新的标记点，请点击'显示六方图'按钮更新显示。")

                    logging.info(f"添加{point_type}点: 日期={date}, 价格={snap_price:.2f}")
        except Exception as e:
            logging.error(f"处理左键点击事件失败: {str(e)}")

    def handle_trend_line_start(self, event):
        """处理趋势线起始点的选择"""
        try:
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                date_idx = int(x)
                if 0 <= date_idx < len(self.current_subset):
                    # 查找最接近点击位置的点（可以是高点或低点）
                    closest_point = None
                    min_distance = float('inf')
                    point_type = None

                    # 检查高点和低点
                    for type_name in ['high', 'low']:
                        for point in self.manual_points[type_name]:
                            point_idx = self.current_subset.index.get_loc(point['date'])
                            distance = abs(point_idx - date_idx)
                            if distance < min_distance:
                                min_distance = distance
                                closest_point = point
                                point_type = type_name

                    if closest_point and min_distance < 5:  # 允许5个K线的误差范围
                        self.selected_high_point = closest_point  # 保持变量名不变，但实际上可以存储低点
                        self.trend_lines = []  # 清除之前的趋势线
                        self.draw_trend_lines_from_point(closest_point)
                        logging.info(
                            f"选择起始{point_type}点并绘制趋势线: 日期={closest_point['date']}, 价格={closest_point['price']:.2f}")
        except Exception as e:
            logging.error(f"处理趋势线起始点时出错: {str(e)}")

    def extend_trend_line(self, start_point, end_point):
        """计算趋势线的延长线"""
        try:
            # 获取起点和终点在数据中的位置
            start_idx = self.current_subset.index.get_loc(start_point['date'])
            end_idx = self.current_subset.index.get_loc(end_point['date'])

            # 计算斜率
            x_diff = end_idx - start_idx
            y_diff = end_point['price'] - start_point['price']
            slope = y_diff / x_diff if x_diff != 0 else 0

            # 计算延长线的终点（延长到数据集末尾）
            extend_x = len(self.current_subset) - 1
            extend_y = start_point['price'] + slope * (extend_x - start_idx)

            # 如果延长线终点为负值，则不延长趋势线
            if extend_y <= 0:
                return None

            return {
                'x': extend_x,
                'y': extend_y
            }
        except Exception as e:
            logging.error(f"计算趋势线延长线时出错: {str(e)}")
            return None

    def draw_trend_lines_from_point(self, start_point):
        """从选定点开始向之后所有高点或低点画趋势线"""
        try:
            start_date = start_point['date']
            # 判断起始点是高点还是低点
            point_type = 'high' if start_point in self.manual_points['high'] else 'low'
            # 获取所有在起始点之后的同类型点
            later_points = [p for p in self.manual_points[point_type]
                            if p['date'] > start_point['date']]

            # 如果是高点，清除之前的高点趋势线
            # 如果是低点，清除之前的低点趋势线
            self.trend_lines = [line for line in self.trend_lines
                                if ('high' in self.manual_points and start_point in self.manual_points['high'] and
                                    line['start'] not in self.manual_points['high']) or
                                ('low' in self.manual_points and start_point in self.manual_points['low'] and
                                 line['start'] not in self.manual_points['low'])]

            # 向每个后续点画趋势线
            for end_point in later_points:
                # 计算延长线终点
                extended_end = self.extend_trend_line(start_point, end_point)

                self.trend_lines.append({
                    'start': start_point,
                    'end': end_point,
                    'extended_end': extended_end
                })

            # 重新绘制图表
            self.safe_plot_chart(show_double_tops=self.is_weekly)
        except Exception as e:
            logging.error(f"绘制趋势线时出错: {str(e)}")

    def show_context_menu(self, pos):
        """显示右键菜单"""
        try:
            menu = QMenu(self)

            # 只在有last_click_pos时才尝试查找附近点
            if hasattr(self, 'last_click_pos') and self.last_click_pos is not None:
                nearby_point = self.find_nearby_point(self.last_click_pos)
                if nearby_point:
                    delete_action = menu.addAction("删除此标记点")
                    menu.addSeparator()

                # 检查是否点击在时间尺上
                clicked_ruler = self.find_clicked_ruler(self.last_click_pos)
                if clicked_ruler is not None:
                    period_text = "周" if self.is_weekly else "日"
                    delete_ruler_action = menu.addAction(f"删除此时间尺 ({clicked_ruler['k_line_count']} 根{period_text}K线)")
                    menu.addSeparator()

            # 添加趋势线相关的菜单项
            if self.trend_lines:
                delete_last_line = menu.addAction("删除最后一条趋势线")
                delete_all_lines = menu.addAction("删除所有趋势线")

            # 添加时间尺相关的菜单项
            if self.time_rulers:
                menu.addSeparator()
                time_ruler_menu = menu.addMenu("时间尺操作")
                clear_all_rulers_action = time_ruler_menu.addAction("清除所有时间尺")

            # 只有当菜单有动作时才显示
            if not menu.isEmpty():
                action = menu.exec(self.canvas.mapToGlobal(pos))
                if action:
                    if action.text() == "删除此标记点":
                        self.delete_point(nearby_point)
                    elif action.text() == "删除最后一条趋势线":
                        self.trend_lines.pop()
                        self.safe_plot_chart(show_double_tops=self.is_weekly)
                    elif action.text() == "删除所有趋势线":
                        self.trend_lines = []
                        self.safe_plot_chart(show_double_tops=self.is_weekly)
                    elif action.text() == "清除所有时间尺":
                        self.clear_all_time_rulers()
                    elif clicked_ruler is not None and "删除此时间尺" in action.text():
                        self.remove_time_ruler(clicked_ruler['id'])

        except Exception as e:
            logging.error(f"显示右键菜单时出错: {str(e)}")

    def find_clicked_ruler(self, event):
        """查找点击的时间尺"""
        try:
            if event.inaxes != self.ax or not self.time_rulers:
                return None

            x, y = event.xdata, event.ydata
            if x is None or y is None:
                return None

            # 检查是否点击在时间尺上
            for ruler in self.time_rulers:
                # 检查是否点击在水平线上
                if (abs(y - ruler['mid_y']) < 0.5 and
                    ruler['start_x'] <= x <= ruler['end_x']):
                    return ruler

                # 检查是否点击在左侧垂直线上
                if (abs(x - ruler['start_x']) < 0.5 and
                    ruler['start_y'] <= y <= ruler['mid_y']):
                    return ruler

                # 检查是否点击在右侧垂直线上
                if (abs(x - ruler['end_x']) < 0.5 and
                    ruler['end_y'] <= y <= ruler['mid_y']):
                    return ruler

            return None
        except Exception as e:
            logging.error(f"查找点击的时间尺失败: {str(e)}")
            return None

    def find_nearby_point(self, event):
        """查找点击位置附近的标记点"""
        if not hasattr(self, 'ax') or not self.ax:
            return None

        data_x, data_y = event.xdata, event.ydata
        if data_x is None or data_y is None:
            return None

        for point_type in ['high', 'low']:
            for i, point in enumerate(self.manual_points[point_type]):
                try:
                    # 直接使用 date_dt 字段
                    idx = point['date_dt'] if 'date_dt' in point else self.current_subset.index.get_loc(point['date'])
                    price_range = (self.ax.get_ylim()[1] - self.ax.get_ylim()[0]) * 0.02
                    if abs(idx - data_x) < 2 and abs(point['price'] - data_y) < price_range:
                        return {'type': point_type, 'index': i}
                except Exception as e:
                    logging.error(f"查找标记点时出错: {str(e)}")
        return None

    def delete_point(self, point_info):
        """删除指定的标记点"""
        try:
            point_type = point_info['type']
            index = point_info['index']

            deleted_point = self.manual_points[point_type].pop(index)
            logging.info(f"删除标记点: {point_type}, 日期={deleted_point['date']}, 价格={deleted_point['price']:.2f}")

            self.safe_plot_chart(show_double_tops=self.is_weekly)
        except Exception as e:
            logging.error(f"删除标记点时出错: {str(e)}")
            self.show_error_dialog(f"删除标记点失败: {str(e)}")

    def show_error_dialog(self, message):
        """显示错误对话框"""
        QMessageBox.critical(self, "错误", message)

    def detect_high_low_points(self, window_size=20, threshold=0.02):
        """自动检测高低点

        Args:
            window_size (int): 检测窗口大小
            threshold (float): 价格变化阈值（百分比）
        """
        if self.current_subset is None:
            return

        try:
            # 清除现有的标记点
            self.manual_points = {'high': [], 'low': []}

            highs = self.current_subset['High']
            lows = self.current_subset['Low']

            for i in range(window_size, len(self.current_subset) - window_size):
                # 检测高点
                if all(highs.iloc[i] > highs.iloc[i - j] for j in range(1, window_size)) and \
                        all(highs.iloc[i] > highs.iloc[i + j] for j in range(1, window_size)):
                    self.manual_points['high'].append({
                        'date': self.current_subset.index[i],
                        'price': highs.iloc[i]
                    })

                # 检测低点
                if all(lows.iloc[i] < lows.iloc[i - j] for j in range(1, window_size)) and \
                        all(lows.iloc[i] < lows.iloc[i + j] for j in range(1, window_size)):
                    self.manual_points['low'].append({
                        'date': self.current_subset.index[i],
                        'price': lows.iloc[i]
                    })

            self.safe_plot_chart(show_double_tops=self.is_weekly)

            # 自动检测完成，不再自动显示六方图
            logging.info("自动检测完成，可以点击'显示六方图'按钮查看结果")

        except Exception as e:
            logging.error(f"自动检测高低点失败: {str(e)}")

    def clear_marked_points(self):
        """清除所有标记点"""
        if self.manual_points['high'] or self.manual_points['low']:
            reply = QMessageBox.question(
                self,
                "确认清除",
                "是否要清除所有标记点？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.manual_points = {'high': [], 'low': []}
                self.safe_plot_chart(show_double_tops=self.is_weekly)
                logging.info("所有标记点已清除")

    def match_optimal_ma(self):
        """手动触发均线匹配"""
        if not self.manual_points['high'] and not self.manual_points['low']:
            QMessageBox.warning(self, "提示", "请先标记一些高低点")
            return
        self.safe_plot_chart(show_double_tops=self.is_weekly, calculate_ma=True)

    def show_time_differences(self):
        """显示标记点之间的时间差"""
        try:
            # 收集所有标记点
            all_points = []
            for point_type in ['high', 'low']:
                for point in self.manual_points[point_type]:
                    all_points.append({
                        'type': point_type,
                        'date': point['date'],
                        'price': point['price']
                    })

            # 按时间排序
            all_points.sort(key=lambda x: x['date'])

            if len(all_points) < 2:
                QMessageBox.warning(self, "提示", "需要至少两个标记点")
                return

            # 创建结果窗口
            dialog = QDialog(self)
            dialog.setWindowTitle("时间差矩阵")
            dialog.setMinimumWidth(800)
            dialog.setMinimumHeight(600)

            # 创建表格
            layout = QVBoxLayout(dialog)
            text_area = QTextEdit()
            text_area.setReadOnly(True)
            layout.addWidget(text_area)

            # 计算并显示时间差
            result_text = "时间差矩阵 (单位: K线数量)\n\n"
            result_text += "格式: [起点类型]价格 -> [终点类型]价格: K线数量\n\n"

            all_differences = []

            # 计算所有点对之间的时间差
            for i in range(len(all_points)):
                for j in range(i + 1, len(all_points)):
                    start_point = all_points[i]
                    end_point = all_points[j]

                    # 计算两点之间的K线数量
                    if self.is_weekly:
                        # 使用当前显示的周线数据计算
                        start_idx = self.current_subset.index.get_loc(start_point['date'])
                        end_idx = self.current_subset.index.get_loc(end_point['date'])
                    else:
                        # 使用日线数据计算
                        start_idx = self.data.index.get_loc(start_point['date'])
                        end_idx = self.data.index.get_loc(end_point['date'])

                    diff = abs(end_idx - start_idx)

                    difference_info = {
                        'start': start_point,
                        'end': end_point,
                        'diff': diff
                    }
                    all_differences.append(difference_info)

            # 按时间差排序
            all_differences.sort(key=lambda x: x['diff'])

            # 格式化输出
            for diff_info in all_differences:
                start = diff_info['start']
                end = diff_info['end']
                result_text += (
                    f"[{start['type']}]{start['price']:.2f} -> "
                    f"[{end['type']}]{end['price']:.2f}: "
                    f"{diff_info['diff']} 根{'周' if self.is_weekly else '日'}K线\n"
                )

            text_area.setText(result_text)

            # 添加关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            logging.error(f"计算时间差失败: {str(e)}")
            self.show_error_dialog(f"计算时间差失败: {str(e)}")

    def toggle_time_ruler_mode(self):
        """切换时间尺模式"""
        self.is_time_ruler_mode = not self.is_time_ruler_mode

        # 如果开启时间尺模式，关闭其他模式
        if self.is_time_ruler_mode:
            self.is_drawing_line = False
            self.draw_line_btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            self.draw_line_btn.setText("画线工具")

            # 更新时间尺按钮样式
            self.time_ruler_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self.status_label.setText("时间尺模式：点击并拖动测量两个K线之间的距离")

            # 重置当前时间尺变量，但保留已绘制的时间尺
            self.time_ruler_start = None
            self.time_ruler_end = None
        else:
            # 恢复时间尺按钮样式
            self.time_ruler_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #d35400;
                }
            """)
            self.status_label.setText("就绪")

            # 清除当前时间尺，但保留已绘制的时间尺
            self.clear_time_ruler()

    def clear_time_ruler(self):
        """清除当前时间尺"""
        self.time_ruler_start = None
        self.time_ruler_end = None

        # 重绘时间尺，不重绘整个图表
        if hasattr(self, 'current_subset') and self.current_subset is not None:
            self.draw_time_ruler()

    def remove_time_ruler(self, ruler_id):
        """删除指定的时间尺"""
        try:
            # 找到并删除指定的时间尺
            self.time_rulers = [ruler for ruler in self.time_rulers if ruler['id'] != ruler_id]

            # 重绘时间尺，不重绘整个图表
            if hasattr(self, 'current_subset') and self.current_subset is not None:
                self.draw_time_ruler()
        except Exception as e:
            logging.error(f"删除时间尺失败: {str(e)}")

    def clear_all_time_rulers(self):
        """清除所有时间尺"""
        # 清除时间尺列表
        self.time_rulers = []
        self.time_ruler_start = None
        self.time_ruler_end = None

        # 清除时间尺图形元素
        if hasattr(self, 'time_ruler_artists') and self.time_ruler_artists:
            for artist in self.time_ruler_artists:
                if artist in self.ax.lines or artist in self.ax.texts:
                    artist.remove()
            self.time_ruler_artists = []

            # 刷新画布，不重绘整个图表
            if hasattr(self, 'canvas'):
                self.canvas.draw()

    def toggle_draw_line_mode(self):
        """切换画线模式"""
        self.is_drawing_line = not self.is_drawing_line

        # 如果开启画线模式，关闭时间尺模式
        if self.is_drawing_line:
            self.is_time_ruler_mode = False
            self.time_ruler_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #d35400;
                }
            """)

            # 清除当前时间尺，但保留已绘制的时间尺
            self.time_ruler_start = None
            self.time_ruler_end = None

            self.draw_line_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self.draw_line_btn.setText("退出画线")
            self.status_label.setText("画线模式：点击两个点以绘制趋势线")
        else:
            self.draw_line_btn.setStyleSheet("""
                QPushButton {
                    background-color: #9b59b6;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #8e44ad;
                }
            """)
            self.draw_line_btn.setText("画线工具")
            self.line_start_point = None
            self.status_label.setText("退出画线模式")

    def handle_draw_line_click(self, event):
        """处理画线模式下的点击事件"""
        try:
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                date_idx = int(x)
                if 0 <= date_idx < len(self.current_subset):
                    # 查找最近的高点或低点
                    closest_point = None
                    min_distance = float('inf')

                    # 检查高点和低点
                    for point_type in ['high', 'low']:
                        for point in self.manual_points[point_type]:
                            if point['date'] in self.current_subset.index:
                                point_idx = self.current_subset.index.get_loc(point['date'])
                                distance = abs(point_idx - date_idx)
                                price_distance = abs(point['price'] - y)
                                # 综合考虑时间和价格距离
                                total_distance = distance + price_distance * 0.1  # 价格距离权重较小
                                if total_distance < min_distance and distance < 5:  # 允许5个K线的误差范围
                                    min_distance = total_distance
                                    closest_point = point

                    # 如果找到最近的点，使用该点的坐标
                    if closest_point:
                        point_to_use = closest_point
                    else:
                        point_to_use = {
                            'date': self.current_subset.index[date_idx],
                            'price': y
                        }

                    if self.line_start_point is None:
                        # 记录第一个点
                        self.line_start_point = point_to_use
                        self.status_label.setText("已选择起始点，请选择终点")
                    else:
                        # 记录第二个点并画线
                        end_point = point_to_use
                        # 添加新的趋势线
                        extended_end = self.extend_trend_line(self.line_start_point, end_point)
                        self.trend_lines.append({
                            'start': self.line_start_point,
                            'end': end_point,
                            'extended_end': extended_end
                        })
                        # 重置起始点和画线模式
                        self.line_start_point = None
                        self.toggle_draw_line_mode()
                        # 重绘图表
                        self.safe_plot_chart(show_double_tops=self.is_weekly)
        except Exception as e:
            logging.error(f"画线过程中出错: {str(e)}")

    def update_point_buttons(self):
        """更新高低点按钮"""
        try:
            # 清除现有按钮
            for btn in self.high_point_buttons + self.low_point_buttons:
                btn.deleteLater()
            self.high_point_buttons.clear()
            self.low_point_buttons.clear()

            # 获取所有高点和低点
            high_points = sorted(self.manual_points['high'],
                                 key=lambda x: x['date'])[-10:]  # 最近10个高点
            low_points = sorted(self.manual_points['low'],
                                key=lambda x: x['date'])[-10:]  # 最近10个低点

            # 创建高点按钮
            high_layout = self.points_panel.findChild(QGroupBox, "高点").layout()
            for point in high_points:
                btn = QPushButton(f"{point['price']:.2f}")
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        padding: 5px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                btn.clicked.connect(lambda checked, p=point: self.draw_trend_lines_from_point(p))
                high_layout.addWidget(btn)
                self.high_point_buttons.append(btn)

            # 创建低点按钮
            low_layout = self.points_panel.findChild(QGroupBox, "低点").layout()
            for point in low_points:
                btn = QPushButton(f"{point['price']:.2f}")
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        padding: 5px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                btn.clicked.connect(lambda checked, p=point: self.draw_trend_lines_from_point(p))
                low_layout.addWidget(btn)
                self.low_point_buttons.append(btn)

        except Exception as e:
            logging.error(f"更新点位按钮失败: {str(e)}")

    def show_delete_lines_dialog(self):
        """显示删除趋势线对话框"""
        if not self.trend_lines:
            self.show_error_dialog("当前没有趋势线可删除")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("删除趋势线")
        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # 添加按钮
        delete_last = QPushButton("删除最后一条趋势线")
        delete_all = QPushButton("删除所有趋势线")
        cancel = QPushButton("取消")

        delete_last.clicked.connect(lambda: self.delete_last_line(dialog))
        delete_all.clicked.connect(lambda: self.delete_all_lines(dialog))
        cancel.clicked.connect(dialog.close)

        layout.addWidget(delete_last)
        layout.addWidget(delete_all)
        layout.addWidget(cancel)

        dialog.exec()

    def delete_last_line(self, dialog):
        """删除最后一条趋势线"""
        if self.trend_lines:
            self.trend_lines.pop()
            self.safe_plot_chart(show_double_tops=self.is_weekly)
            logging.info("删除最后一条趋势线")
        dialog.close()

    def delete_all_lines(self, dialog):
        """删除所有趋势线"""
        self.trend_lines = []
        self.safe_plot_chart(show_double_tops=self.is_weekly)
        logging.info("删除所有趋势线")
        dialog.close()

    def find_lowest_points(self, prices, n=2):
        """找出最低的n个点"""
        lows = []
        try:
            # 使用 iloc 替代直接索引
            sorted_prices = prices.sort_values()
            for i in range(min(n, len(sorted_prices))):
                lows.append({
                    'date': sorted_prices.index[i],
                    'price': sorted_prices.iloc[i]
                })
        except Exception as e:
            logging.error(f"查找最低点时出错: {str(e)}")
        return lows

    def create_hexagon_chart(self):
        """创建六方图窗口"""
        global _hexagon_window

        try:
            logging.info("开始创建六方图窗口")

            # 如果已经有六方图窗口，先关闭它
            if _hexagon_window is not None:
                try:
                    if hasattr(_hexagon_window, 'window'):
                        _hexagon_window.window.close()
                        logging.info("关闭了现有的六方图窗口")
                except Exception as e:
                    logging.error(f"关闭现有六方图窗口失败: {str(e)}")

            # 直接导入六方图模块
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            sys.path.append(current_dir)

            # 尝试使用绝对路径导入
            hexagon_path = os.path.join(current_dir, "六方图.py")
            logging.info(f"六方图模块路径: {hexagon_path}")

            if os.path.exists(hexagon_path):
                # 使用importlib动态导入模块
                import importlib.util
                spec = importlib.util.spec_from_file_location("六方图", hexagon_path)
                hexagon_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(hexagon_module)

                # 使用动态导入的模块创建对象
                self.hexagon_window = hexagon_module.HexagonChart(self)
                _hexagon_window = self.hexagon_window  # 设置全局变量
                logging.info("六方图对象创建成功")

                # 传入手工添加的点位
                if hasattr(self.hexagon_window, 'plot_hexagon'):
                    self.hexagon_window.plot_hexagon(manual_points=self.manual_points)
                    logging.info("六方图绘制成功")

                # 显示窗口
                if hasattr(self.hexagon_window, 'window'):
                    self.hexagon_window.window.show()
                    logging.info("六方图窗口显示成功")
            else:
                logging.error(f"六方图模块文件不存在: {hexagon_path}")

        except Exception as e:
            logging.error(f"创建六方图时出错: {str(e)}")
            logging.error(traceback.format_exc())

    def update_hexagon_chart(self):
        """更新六方图"""
        try:
            # 如果六方图窗口存在并且有plot_hexagon方法，则更新它
            if (hasattr(self, 'hexagon_window') and self.hexagon_window is not None and
                hasattr(self.hexagon_window, 'plot_hexagon')):
                # 直接重新调用plot_hexagon方法，传入最新的手工添加点位
                self.hexagon_window.plot_hexagon(manual_points=self.manual_points)
                logging.info("六方图更新成功")
            else:
                # 如果六方图窗口不存在或者无法更新，则创建新的
                # 不尝试关闭旧窗口，直接创建新窗口

                # 创建新的六方图窗口
                self.hexagon_window = None  # 确保清除旧的引用
                self.create_hexagon_chart()
        except Exception as e:
            logging.error(f"更新六方图时出错: {str(e)}")
            # 如果更新失败，尝试创建新的
            self.hexagon_window = None
            self.create_hexagon_chart()

    def show_hexagon_chart(self):
        """显示六方图"""
        try:
            logging.info("点击显示六方图按钮")

            # 如果没有标记高低点，显示提示但仍然显示六方图
            if not self.manual_points['high'] and not self.manual_points['low']:
                QMessageBox.information(self, "提示", "当前没有标记高低点，六方图上不会显示标记。")

            # 直接创建新的六方图窗口，全局变量会处理关闭旧窗口
            self.create_hexagon_chart()
            logging.info("六方图显示成功")
        except Exception as e:
            logging.error(f"显示六方图失败: {str(e)}")
            logging.error(traceback.format_exc())

    def create_hexagon_chart(self):
        """创建六方图窗口"""
        try:
            # 确保有高点和低点数据
            if not self.manual_points['high'] and not self.manual_points['low']:
                QMessageBox.warning(self, "警告", "请先标记或检测高低点")
                return

            # 如果已经有六方图窗口，先关闭它
            if hasattr(self, 'hexagon_window') and self.hexagon_window is not None:
                try:
                    self.hexagon_window.close()
                    logging.info("关闭现有六方图窗口")
                except Exception as e:
                    logging.error(f"关闭六方图窗口失败: {str(e)}")

            # 创建新窗口显示六方图
            # 使用QMainWindow而不是QDialog，因为QMainWindow有最小化按钮
            self.hexagon_window = QMainWindow(self)
            self.hexagon_window.setWindowTitle("六方图分析")
            self.hexagon_window.setGeometry(100, 100, 1600, 1600)  # 显著增大窗口尺寸

            # 设置窗口标志，确保有最小化、最大化和关闭按钮
            self.hexagon_window.setWindowFlags(
                Qt.WindowType.Window |
                Qt.WindowType.WindowMinimizeButtonHint |
                Qt.WindowType.WindowMaximizeButtonHint |
                Qt.WindowType.WindowCloseButtonHint
            )

            # 创建中心部件并设置布局
            central_widget = QWidget()
            self.hexagon_window.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)

            # 创建标注说明区域
            info_text = QTextEdit()
            info_text.setMaximumHeight(100)
            info_text.setReadOnly(True)

            # 收集所有点的价格并转换为六方图数字
            high_numbers = []
            high_point_numbers = set()  # 高点数字集合
            for point in self.manual_points['high']:
                price = point['price']
                hex_number = int(price * 10)  # 转换为六方图数字
                high_numbers.append(f"高点 {price:.2f} -> {hex_number}")
                high_point_numbers.add(hex_number)  # 添加到高点集合

            low_numbers = []
            low_point_numbers = set()  # 低点数字集合
            for point in self.manual_points['low']:
                price = point['price']
                hex_number = int(price * 10)  # 转换为六方图数字
                low_numbers.append(f"低点 {price:.2f} -> {hex_number}")
                low_point_numbers.add(hex_number)  # 添加到低点集合

            # 设置标注文本
            info_text.setText("\n".join(high_numbers + low_numbers))
            layout.addWidget(info_text)

            # 创建六方图画布
            canvas = FigureCanvas(Figure(figsize=(30, 30)))  # 显著增大图形尺寸，使数字排列更分散
            layout.addWidget(canvas)

            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            hexagon_path = os.path.join(current_dir, "六方图.py")

            # 导入六方图模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("六方图", hexagon_path)
            hexagon_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(hexagon_module)

            # 使用六方图的配置
            ax = canvas.figure.add_subplot(111, polar=True)

            # 设置30度间隔的刻度
            angles_30deg = np.arange(0, 360, 30)
            ax.set_xticks(np.deg2rad(angles_30deg))
            ax.set_xticklabels([f'{int(deg)}°' for deg in angles_30deg])

            # 从六方图模块获取数据并添加我们的高亮数字
            all_circles = hexagon_module.all_circles
            digits = hexagon_module.digits
            highlight_digits_red = hexagon_module.highlight_digits_red.union(high_point_numbers)  # 合并高亮数字
            highlight_angles_red = hexagon_module.highlight_angles_red
            highlight_angles_green = hexagon_module.highlight_angles_green
            highlight_digits_green = low_point_numbers  # 低点数字使用绿色高亮

            # 绘制每个数字的位置
            for d in digits:
                # 判断是否在红色高亮方向
                is_highlight_red = d[0] in highlight_digits_red or \
                                any(math.isclose(d[1] % 360, angle, abs_tol=1.0)
                                    for angle in highlight_angles_red)

                # 判断是否在绿色高亮方向
                is_highlight_green = any(math.isclose(d[1] % 360, angle, abs_tol=1.0)
                                    for angle in highlight_angles_green)

                # 判断是否是高点或低点
                is_high_point = d[0] in high_point_numbers
                is_low_point = d[0] in low_point_numbers

                # 设置颜色和字体属性
                # 设置文本颜色
                if is_highlight_red:
                    text_color = 'red'
                elif is_highlight_green:
                    text_color = 'green'
                else:
                    text_color = 'black'

                font_weight = 'bold' if is_highlight_red or is_highlight_green else 'normal'

                # 在数字位置绘制小白点
                ax.scatter(np.radians(d[1]), d[2], s=30, color='white')

                # 如果是高点，在数字下方绘制橙色正方形
                if is_high_point:
                    ax.scatter(np.radians(d[1]), d[2], s=100, marker='s', color='orange', alpha=0.5, zorder=5)
                # 如果是低点，在数字下方绘制蓝色正方形
                elif is_low_point:
                    ax.scatter(np.radians(d[1]), d[2], s=100, marker='s', color='blue', alpha=0.5, zorder=5)

                # 所有数字使用相同的字体大小
                font_size = 8

                # 绘制数字
                ax.annotate(f"{d[0]}",
                            xy=(np.radians(d[1]), d[2] + 0.8),
                            fontsize=font_size,
                            color=text_color,
                            weight=font_weight,
                            ha='center',
                            va='center',
                            zorder=10)  # 确保数字显示在最上层

            # 设置图形范围 - 使用最大圈层的半径的两倍作为显示范围
            max_circle_radius = all_circles[-1]["r"]  # 最大圈层的半径
            display_radius = max_circle_radius * 1.3  # 留出更多空间
            logging.info(f"设置图形显示范围: 0 - {display_radius}")
            ax.set_ylim(0, display_radius)

            # 设置网格和坐标轴
            ax.grid(True, alpha=0.3)
            ax.set_axisbelow(True)  # 网格线显示在数据点下面

            # 设置标题和隐藏径向标签
            ax.set_title("主要方向数字高亮显示", pad=20, fontsize=20)
            ax.set_yticklabels([])

            # 增大角度刻度的字体大小
            ax.tick_params(axis='x', labelsize=16)

            # 显示窗口
            self.hexagon_window.show()
            canvas.draw()

        except Exception as e:
            logging.error(f"创建六方图时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"创建六方图时出错: {str(e)}")

    @staticmethod
    def calculate_angle(n):
        """计算数字n在六分仪上对应的角度"""
        # 如果n小于1，返回0度
        if n < 1:
            return 0

        # 使用公式直接计算圈层，避免无限循环
        # 解方程：3*ring^2 - 3*ring + 1 <= n <= 3*ring^2 + 3*ring
        # 近似解：ring ≈ sqrt(n/3)
        ring_estimate = max(1, int(math.sqrt(n / 3)))

        # 从估计值开始检查几个圈层
        for ring in range(max(1, ring_estimate - 1), ring_estimate + 2):
            start = 3 * ring * ring - 3 * ring + 1
            end = 3 * ring * ring + 3 * ring
            if start <= n <= end:
                # 计算在当前圈层中的位置和角度
                position = n - start + 1
                angle_step = 60 / ring  # 每个数字之间的角度
                angle = position * angle_step
                return angle % 360  # 规范化到 [0°, 360°)

        # 如果没有找到匹配的圈层（这种情况应该不会发生，但为了安全）
        # 使用最大圈层的公式计算一个近似值
        ring = ring_estimate + 1
        start = 3 * ring * ring - 3 * ring + 1
        position = n - start + 1
        angle_step = 60 / ring
        angle = position * angle_step
        return angle % 360

    def find_best_start_point(self):
        """查找最佳起点"""
        try:
            # 获取所有高低点并排序
            all_points = []
            for point_type in ['high', 'low']:
                for point in self.manual_points[point_type]:
                    all_points.append(point)
            all_points.sort(key=lambda x: x['date'])

            if len(all_points) < 2:
                QMessageBox.warning(self, "提示", "至少需要2个高低点")
                return

            # 限制点的数量，防止计算量过大
            if len(all_points) > 10:
                reply = QMessageBox.question(
                    self,
                    "确认",
                    f"检测到{len(all_points)}个点，计算量可能很大。是否继续？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return

            # 找出最早的高低点日期
            earliest_point_date = all_points[0]['date']
            # 尝试在数据集中找到这个日期，如果不存在，则找最接近的日期
            try:
                earliest_idx = self.current_subset.index.get_loc(earliest_point_date)
            except KeyError:
                # 如果日期不存在，找最接近的日期
                closest_dates = self.current_subset.index[self.current_subset.index >= earliest_point_date]
                if len(closest_dates) > 0:
                    earliest_idx = self.current_subset.index.get_loc(closest_dates[0])
                else:
                    closest_dates = self.current_subset.index[self.current_subset.index <= earliest_point_date]
                    if len(closest_dates) > 0:
                        earliest_idx = self.current_subset.index.get_loc(closest_dates[-1])
                    else:
                        # 如果找不到接近的日期，使用第一个日期
                        earliest_idx = 0

            # 从第一个高低点左20根K线开始
            start_idx = max(0, earliest_idx - 20)

            # 向右延伸到最后一个高低点之后
            latest_point_date = all_points[-1]['date']
            # 尝试在数据集中找到这个日期，如果不存在，则找最接近的日期
            try:
                latest_idx = self.current_subset.index.get_loc(latest_point_date)
            except KeyError:
                # 如果日期不存在，找最接近的日期
                closest_dates = self.current_subset.index[self.current_subset.index <= latest_point_date]
                if len(closest_dates) > 0:
                    latest_idx = self.current_subset.index.get_loc(closest_dates[-1])
                else:
                    closest_dates = self.current_subset.index[self.current_subset.index >= latest_point_date]
                    if len(closest_dates) > 0:
                        latest_idx = self.current_subset.index.get_loc(closest_dates[0])
                    else:
                        # 如果找不到接近的日期，使用最后一个日期
                        latest_idx = len(self.current_subset) - 1
            # 设置结束索引，可以向右多看一些点
            end_idx = min(len(self.current_subset) - 1, latest_idx + 20)

            best_score = -1
            best_start = None
            best_weeks = None

            # 计算总迭代次数
            total_iterations = end_idx - start_idx + 1

            # 创建进度对话框
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("计算中")
            progress_dialog.setFixedSize(300, 100)
            layout = QVBoxLayout(progress_dialog)

            progress_label = QLabel(f"正在计算最佳起点 (0/{total_iterations})")
            layout.addWidget(progress_label)

            progress_bar = QProgressBar()
            progress_bar.setRange(0, total_iterations)
            progress_bar.setValue(0)
            layout.addWidget(progress_bar)

            # 非模态显示进度对话框
            progress_dialog.show()
            QApplication.processEvents()

            # 遍历所有可能的起点
            for i in range(start_idx, end_idx + 1):


                start_date = self.current_subset.index[i]
                print(f"\n检查起点: {start_date.strftime('%Y-%m-%d')}")

                point_weeks = []
                zero_weeks_count = 0
                for point in all_points:
                    weeks = len(pd.date_range(start=start_date, end=point['date'], freq='W'))
                    # 记录零周的数量，但不跳过该起点
                    if weeks == 0:
                        zero_weeks_count += 1
                        print(f"  到点 {point['date'].strftime('%Y-%m-%d')} 的周数为0")
                        # 不添加零周的点
                    else:
                        point_weeks.append(weeks)
                        print(f"  到点 {point['date'].strftime('%Y-%m-%d')} 的周数: {weeks}")

                # 如果所有点都是零周或者只有一个非零周的点，跳过该起点
                if len(point_weeks) <= 1:
                    # 更新进度条
                    progress_count = i - start_idx + 1
                    progress_bar.setValue(progress_count)
                    progress_label.setText(f"正在计算最佳起点 ({progress_count}/{total_iterations})")
                    QApplication.processEvents()  # 刷新UI
                    continue

                # 计算角度匹配度
                score = 0
                print("\n角度计算详情:")

                # 计算所有点的角度（提前计算，避免重复计算）
                angles = [self.calculate_angle(weeks) for weeks in point_weeks]

                # 找出所有角度差接近90度倍数的点对，并计算误差
                angle_pairs = []
                for i in range(len(angles)):
                    for j in range(i+1, len(angles)):
                        angle_i = angles[i]
                        angle_j = angles[j]
                        angle_diff = abs(angle_i - angle_j)
                        angle_diff = min(angle_diff, 360 - angle_diff)

                        # 如果角度差接近90度的倍数（允许5度误差）
                        for multiple in [0, 90, 180, 270]:
                            error = abs(angle_diff - multiple)
                            if error <= 5:
                                # 存储点对、角度差和误差
                                angle_pairs.append((i, j, angle_diff, multiple, error))
                                print(f"  角度 {angle_i:.1f}° 和 {angle_j:.1f}° 相差 {angle_diff:.1f}°，接近 {multiple}°，误差 {error:.1f}°")
                                break

                # 特别处理角度相同的点（角度差为0度）
                for i in range(len(angles)):
                    for j in range(i+1, len(angles)):
                        angle_i = angles[i]
                        angle_j = angles[j]
                        if abs(angle_i - angle_j) < 5 or abs(angle_i - angle_j - 360) < 5 or abs(angle_i - angle_j + 360) < 5:
                            # 存储点对、角度差和误差
                            error = abs(angle_i - angle_j) if abs(angle_i - angle_j) < 5 else min(abs(angle_i - angle_j - 360), abs(angle_i - angle_j + 360))
                            angle_pairs.append((i, j, 0, 0, error))
                            print(f"  角度 {angle_i:.1f}° 和 {angle_j:.1f}° 相差小于5度，视为相同角度，误差 {error:.1f}°")

                # 按误差从小到大排序
                angle_pairs.sort(key=lambda x: x[4])

                # 使用贪心算法构建十字架，从误差最小的点对开始
                crosses = []

                # 如果有点对，开始构建十字架
                if angle_pairs:
                    # 初始化十字架集合
                    all_crosses = []

                        # 首先，我们将所有点对按照误差从小到大排序
                    # 然后，我们尝试所有可能的十字架组合

                    # 创建一个图，其中点之间的边表示角度差接近90度的倍数
                    graph = {}
                    for i in range(len(angles)):
                        graph[i] = []

                    # 添加边
                    for i, j, _, _, _ in angle_pairs:
                        graph[i].append(j)
                        graph[j].append(i)

                    # 尝试所有可能的起始点
                    for start_node in range(len(angles)):
                        # 从这个点开始构建十字架
                        current_cross = set([start_node])

                        # 尝试添加所有可能的点
                        for i in range(len(angles)):
                            if i == start_node:
                                continue  # 跳过起始点

                            # 检查该点是否可以添加到十字架中
                            can_add = True
                            for k in current_cross:
                                # 检查i和十字架中每个点的角度差
                                angle_i = angles[i]
                                angle_k = angles[k]
                                diff = abs(angle_i - angle_k)
                                diff = min(diff, 360 - diff)

                                # 检查是否接近90度的倍数
                                is_valid = False
                                for multiple in [0, 90, 180, 270]:
                                    if abs(diff - multiple) <= 5:
                                        is_valid = True
                                        break

                                if not is_valid:
                                    can_add = False
                                    break

                            if can_add:
                                current_cross.add(i)

                        # 将构建的十字架添加到列表中
                        if len(current_cross) > 1:  # 只添加至少有2个点的十字架
                            all_crosses.append(current_cross)

                    # 尝试使用传统的方法构建十字架
                    for i, j, _, _, _ in angle_pairs:
                        # 检查这两个点是否已经在某个十字架中
                        found_cross = None
                        for cross in all_crosses:
                            if i in cross and j in cross:
                                found_cross = cross
                                break

                        if found_cross is None:
                            # 如果这两个点都不在任何十字架中，创建新的十字架
                            new_cross = set([i, j])
                            all_crosses.append(new_cross)
                        elif i not in found_cross:
                            # 如果j在十字架中但i不在，检查i是否可以加入
                            can_add = True
                            for k in found_cross:
                                # 检查i和十字架中每个点的角度差
                                angle_i = angles[i]
                                angle_k = angles[k]
                                diff = abs(angle_i - angle_k)
                                diff = min(diff, 360 - diff)

                                # 检查是否接近90度的倍数
                                is_valid = False
                                for multiple in [0, 90, 180, 270]:
                                    if abs(diff - multiple) <= 5:
                                        is_valid = True
                                        break

                                if not is_valid:
                                    can_add = False
                                    break

                            if can_add:
                                found_cross.add(i)
                        elif j not in found_cross:
                            # 如果i在十字架中但j不在，检查j是否可以加入
                            can_add = True
                            for k in found_cross:
                                # 检查j和十字架中每个点的角度差
                                angle_j = angles[j]
                                angle_k = angles[k]
                                diff = abs(angle_j - angle_k)
                                diff = min(diff, 360 - diff)

                                # 检查是否接近90度的倍数
                                is_valid = False
                                for multiple in [0, 90, 180, 270]:
                                    if abs(diff - multiple) <= 5:
                                        is_valid = True
                                        break

                                if not is_valid:
                                    can_add = False
                                    break

                            if can_add:
                                found_cross.add(j)

                    # 找出最大的十字架
                    max_cross = max(all_crosses, key=len) if all_crosses else set()
                    max_cross_count = len(max_cross)
                    max_cross_component = list(max_cross)

                    # 打印每个十字架的信息
                    print(f"  找到 {len(all_crosses)} 个十字架")
                    for i, cross in enumerate(all_crosses):
                        cross_list = list(cross)
                        component_angles = [angles[idx] for idx in cross_list]
                        component_weeks = [point_weeks[idx] for idx in cross_list]
                        print(f"  十字架 {i+1}: {len(cross)} 个点")
                        print(f"    角度: {[f'{angle:.1f}°' for angle in component_angles]}")
                        print(f"    周数: {component_weeks}")

                        # 打印该十字架中所有点对的角度差
                        print(f"    点对角度差:")
                        for m in range(len(cross_list)):
                            for n in range(m+1, len(cross_list)):
                                idx1 = cross_list[m]
                                idx2 = cross_list[n]
                                angle1 = angles[idx1]
                                angle2 = angles[idx2]
                                diff = abs(angle1 - angle2)
                                diff = min(diff, 360 - diff)

                                # 找出最接近的倍数
                                closest_multiple = min([0, 90, 180, 270], key=lambda x: abs(diff - x))
                                error = abs(diff - closest_multiple)

                                print(f"      {point_weeks[idx1]}周({angle1:.1f}°) 和 {point_weeks[idx2]}周({angle2:.1f}°): 差{diff:.1f}°, 接近{closest_multiple}°, 误差{error:.1f}°")
                else:
                    max_cross_count = 0
                    max_cross_component = []

                print(f"  最大十字架有 {max_cross_count} 个点")

                # 不再需要计算传统得分

                # 最终得分：最大十字架上的点数
                score = max_cross_count
                print(f"  最大十字架得分: {max_cross_count}, 总得分: {score}")

                # 打印最大十字架上的时间点
                if max_cross_count > 0:
                    max_cross_weeks = [point_weeks[idx] for idx in max_cross_component]
                    max_cross_angles = [angles[idx] for idx in max_cross_component]

                    print("\n分布最多的十字架上的时间点:")
                    for i in range(len(max_cross_component)):
                        week = max_cross_weeks[i]
                        angle = max_cross_angles[i]
                        # 找出对应的时间点
                        for j, point in enumerate(all_points):
                            weeks_to_point = len(pd.date_range(start=start_date, end=point['date'], freq='W'))
                            if weeks_to_point == week:
                                point_date = point['date']
                                print(f"  {point_date.strftime('%Y-%m-%d')} - {week}周 - {angle:.1f}°")
                                break

                print(f"\n  当前起点得分: {score}")

                if score > best_score:
                    best_score = score
                    best_start = {'date': start_date, 'price': self.current_subset.loc[start_date, 'Close']}
                    best_weeks = point_weeks.copy()

                    # 保存最大十字架的信息
                    if max_cross_count > 0:
                        best_cross_weeks = [point_weeks[idx] for idx in max_cross_component]
                        best_cross_angles = [angles[idx] for idx in max_cross_component]
                        best_cross_points = []

                        for week in best_cross_weeks:
                            for j, point in enumerate(all_points):
                                weeks_to_point = len(pd.date_range(start=start_date, end=point['date'], freq='W'))
                                if weeks_to_point == week:
                                    best_cross_points.append(point)
                                    break

                    print(f"  ★ 更新最佳起点")

                # 更新进度条
                progress_count = i - start_idx + 1
                progress_bar.setValue(progress_count)
                progress_label.setText(f"正在计算最佳起点 ({progress_count}/{total_iterations})")
                QApplication.processEvents()  # 刷新UI

            # 关闭进度对话框
            progress_dialog.close()

            if best_start:
                print("\n" + "="*50)
                print(f"最终选择的起点: {best_start['date'].strftime('%Y-%m-%d')}")
                print(f"最终得分: {best_score}")
                print("各点的周数及其角度:")
                for point, weeks in zip(all_points, best_weeks):
                    angle = self.calculate_angle(weeks)
                    print(f"  到点 {point['date'].strftime('%Y-%m-%d')} 的周数: {weeks} (角度: {angle:.1f}°)")

                # 打印最大十字架上的所有时间点
                if 'best_cross_points' in locals() and best_cross_points:
                    print("\n最大十字架上的时间点:")

                    # 按角度排序，便于查看
                    sorted_indices = sorted(range(len(best_cross_angles)), key=lambda i: best_cross_angles[i])

                    for idx in sorted_indices:
                        point = best_cross_points[idx]
                        week = best_cross_weeks[idx]
                        angle = best_cross_angles[idx]
                        print(f"  {point['date'].strftime('%Y-%m-%d')} - {week}周 - {angle:.1f}°")

                    # 打印十字架中所有点对的角度差
                    print("  点对角度差:")
                    for i in range(len(best_cross_points)):
                        for j in range(i+1, len(best_cross_points)):
                            angle1 = best_cross_angles[i]
                            angle2 = best_cross_angles[j]
                            diff = abs(angle1 - angle2)
                            diff = min(diff, 360 - diff)

                            # 找出最接近的倍数
                            closest_multiple = min([0, 90, 180, 270], key=lambda x: abs(diff - x))
                            error = abs(diff - closest_multiple)

                            print(f"    {best_cross_weeks[i]}周({angle1:.1f}°) 和 {best_cross_weeks[j]}周({angle2:.1f}°): 差{diff:.1f}°, 接近{closest_multiple}°, 误差{error:.1f}°")

                    # 额外检查是否有角度相同的点没有被包含
                    print("\n最大十字架上的点索引: " + str(list(max_cross)))

                    # 检查是否有角度相同的点
                    for i, angle_i in enumerate(angles):
                        if i in max_cross:
                            continue  # 跳过已经在十字架中的点

                        # 检查该点是否与十字架中的某个点角度相同
                        for j in max_cross:
                            if j >= len(angles):
                                continue  # 跳过无效的索引

                            angle_j = angles[j]
                            if abs(angle_i - angle_j) < 5 or abs(angle_i - angle_j - 360) < 5 or abs(angle_i - angle_j + 360) < 5:
                                print(f"  发现角度相同的点: {i}号点 - {point_weeks[i]}周 - {angle_i:.1f}°")
                                print(f"    与 {j}号点 - {point_weeks[j]}周 - {angle_j:.1f}° 角度相同")
                                break

                    # 检查是否有点可以添加到十字架中
                    for i, angle_i in enumerate(angles):
                        if i in max_cross:
                            continue  # 跳过已经在十字架中的点

                        # 检查该点是否与十字架中的所有点都有角度关系
                        can_add = True
                        for j in max_cross:
                            if j >= len(angles):
                                continue  # 跳过无效的索引

                            angle_j = angles[j]
                            diff = abs(angle_i - angle_j)
                            diff = min(diff, 360 - diff)

                            # 检查是否接近90度的倍数
                            is_valid = False
                            for multiple in [0, 90, 180, 270]:
                                if abs(diff - multiple) <= 5:
                                    is_valid = True
                                    break

                            if not is_valid:
                                can_add = False
                                break

                        if can_add:
                            print(f"  发现可以添加到十字架的点: {i}号点 - {point_weeks[i]}周 - {angle_i:.1f}°")
                            # 打印与十字架中每个点的角度差
                            for j in max_cross:
                                if j >= len(angles):
                                    continue  # 跳过无效的索引

                                angle_j = angles[j]
                                diff = abs(angle_i - angle_j)
                                diff = min(diff, 360 - diff)
                                closest_multiple = min([0, 90, 180, 270], key=lambda x: abs(diff - x))
                                error = abs(diff - closest_multiple)
                                print(f"    与 {j}号点 - {point_weeks[j]}周({angle_j:.1f}°) 相差 {diff:.1f}°, 接近 {closest_multiple}°, 误差 {error:.1f}°")

                # 找出第二佳起点
                second_best_start = None
                second_best_score = -1
                second_best_weeks = None
                second_best_date = None

                # 遍历所有起点找出第二佳起点
                for i in range(start_idx, earliest_idx + 1):
                    start_date = self.current_subset.index[i]
                    # 跳过最佳起点
                    if best_start and start_date == best_start['date']:
                        continue

                    # 计算该起点的得分
                    current_point_weeks = []
                    for point in all_points:
                        weeks = len(pd.date_range(start=start_date, end=point['date'], freq='W'))
                        current_point_weeks.append(weeks)

                    # 计算角度匹配度
                    current_score = 0
                    current_angles = [self.calculate_angle(weeks) for weeks in current_point_weeks]

                    # 计算得分
                    for j in range(len(current_point_weeks)):
                        for k in range(j + 1, len(current_point_weeks)):
                            angle1 = current_angles[j]
                            angle2 = current_angles[k]
                            angle_diff = abs(angle1 - angle2)
                            angle_diff = min(angle_diff, 360 - angle_diff)

                            for multiple in [90, 180, 270]:
                                if abs(angle_diff - multiple) <= 5:
                                    current_score += 1
                                    break

                    # 如果当前得分是第二高的
                    if current_score > second_best_score and (best_score == -1 or current_score < best_score):
                        second_best_score = current_score
                        second_best_start = {'date': start_date, 'price': self.current_subset.loc[start_date, 'Close']}
                        second_best_weeks = current_point_weeks.copy()
                        second_best_date = start_date

                # 显示第二佳起点
                if second_best_start and second_best_score > -1:
                    print("\n" + "="*50)
                    print(f"第二佳起点: {second_best_date.strftime('%Y-%m-%d')}")
                    print(f"得分: {second_best_score}")
                    print("各点的周数及其角度:")
                    for point, weeks in zip(all_points, second_best_weeks):
                        angle = self.calculate_angle(weeks)
                        print(f"  到点 {point['date'].strftime('%Y-%m-%d')} 的周数: {weeks} (角度: {angle:.1f}°)")

                print("="*50)

                # 在图表上标记
                idx = self.current_subset.index.get_loc(best_start['date'])
                self.ax.plot(idx, best_start['price'], 'D', color='purple', markersize=6)

                # 标记现有的拐点
                for point, weeks in zip(all_points, best_weeks):
                    try:
                        # 尝试在数据集中找到这个日期，如果不存在，则找最接近的日期
                        try:
                            point_idx = self.current_subset.index.get_loc(point['date'])
                        except KeyError:
                            # 如果日期不存在，找最接近的日期
                            closest_dates = self.current_subset.index[self.current_subset.index >= point['date']]
                            if len(closest_dates) > 0:
                                point_idx = self.current_subset.index.get_loc(closest_dates[0])
                            else:
                                closest_dates = self.current_subset.index[self.current_subset.index <= point['date']]
                                if len(closest_dates) > 0:
                                    point_idx = self.current_subset.index.get_loc(closest_dates[-1])
                                else:
                                    # 如果找不到接近的日期，跳过这个点
                                    continue

                        angle = self.calculate_angle(weeks)
                        self.ax.annotate(f'{weeks}周\n{angle:.1f}°',
                                       xy=(point_idx, point['price']),
                                       xytext=(10, 10),
                                       textcoords='offset points')
                    except Exception as e:
                        print(f"  标记拐点时出错: {str(e)}")


                # 删除了标记未来时间点的代码



                self.canvas.draw()
            else:
                QMessageBox.warning(self, "提示", "未找到合适的起点")

        except Exception as e:
            print(f"错误: {str(e)}")
            print(traceback.format_exc())  # 打印完整的错误堆栈
            QMessageBox.critical(self, "错误", f"查找起点时出错：{str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = StableKLineApp()
    window.show()
    sys.exit(app.exec())
