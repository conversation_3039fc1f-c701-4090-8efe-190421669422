#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于黄色标识自动训练CNN模型
使用检测到的黄色标识作为正样本，生成负样本进行训练
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
from yellow_marker_detector import YellowMarkerDetector

class AutoNPatternTrainer:
    """基于黄色标识的自动N字形态训练器"""
    
    def __init__(self, img_size=(224, 224)):
        self.img_size = img_size
        self.detector = YellowMarkerDetector()
        self.model = None
        self.history = None
        
    def load_yellow_detection_results(self, data_dir: str):
        """加载黄色检测结果"""
        summary_path = os.path.join(data_dir, "yellow_detection_summary.json")
        
        if not os.path.exists(summary_path):
            print("未找到黄色检测结果，请先运行 yellow_marker_detector.py")
            return None
        
        with open(summary_path, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        print(f"加载检测结果: {summary['detected_images']}/{summary['total_images']} 个图片包含黄色标识")
        
        # 获取包含黄色标识的图片路径
        positive_files = [result['image_path'] for result in summary['results']]
        
        return positive_files
    
    def preprocess_image(self, img_path: str) -> np.ndarray:
        """预处理图片"""
        try:
            # 使用PIL读取图片
            with Image.open(img_path) as img:
                # 转换为RGB
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 调整大小
                img = img.resize(self.img_size, Image.Resampling.LANCZOS)
                
                # 转换为numpy数组并归一化
                img_array = np.array(img, dtype=np.float32) / 255.0
                
                return img_array
                
        except Exception as e:
            print(f"预处理图片失败 {img_path}: {e}")
            return None
    
    def create_negative_samples(self, positive_files: list, negative_ratio: float = 1.0):
        """创建负样本"""
        print("创建负样本...")
        
        negative_samples = []
        target_count = int(len(positive_files) * negative_ratio)
        
        for i in range(target_count):
            # 随机选择一个正样本作为基础
            base_img_path = np.random.choice(positive_files)
            base_img = self.preprocess_image(base_img_path)
            
            if base_img is not None:
                # 应用变换创建负样本
                negative_img = self._apply_negative_transforms(base_img)
                negative_samples.append(negative_img)
        
        print(f"创建了 {len(negative_samples)} 个负样本")
        return negative_samples
    
    def _apply_negative_transforms(self, img: np.ndarray) -> np.ndarray:
        """应用变换创建负样本"""
        # 随机选择变换类型
        transform_type = np.random.choice(['flip', 'rotate', 'noise', 'blur', 'crop', 'color'])
        
        if transform_type == 'flip':
            # 翻转
            if np.random.random() > 0.5:
                img = np.fliplr(img)
            else:
                img = np.flipud(img)
                
        elif transform_type == 'rotate':
            # 旋转
            angle = np.random.uniform(-45, 45)
            h, w = img.shape[:2]
            center = (w//2, h//2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            img = cv2.warpAffine(img, M, (w, h))
            
        elif transform_type == 'noise':
            # 添加噪声
            noise = np.random.normal(0, 0.05, img.shape)
            img = np.clip(img + noise, 0, 1)
            
        elif transform_type == 'blur':
            # 模糊
            img = cv2.GaussianBlur(img, (5, 5), 0)
            
        elif transform_type == 'crop':
            # 随机裁剪
            h, w = img.shape[:2]
            crop_size = int(min(h, w) * 0.8)
            start_h = np.random.randint(0, h - crop_size)
            start_w = np.random.randint(0, w - crop_size)
            cropped = img[start_h:start_h+crop_size, start_w:start_w+crop_size]
            img = cv2.resize(cropped, (w, h))
            
        elif transform_type == 'color':
            # 颜色变换
            # 调整亮度
            brightness = np.random.uniform(0.7, 1.3)
            img = np.clip(img * brightness, 0, 1)
            
            # 调整对比度
            contrast = np.random.uniform(0.8, 1.2)
            img = np.clip((img - 0.5) * contrast + 0.5, 0, 1)
        
        return img
    
    def prepare_training_data(self, data_dir: str):
        """准备训练数据"""
        # 加载黄色检测结果
        positive_files = self.load_yellow_detection_results(data_dir)
        
        if not positive_files:
            return None, None
        
        print(f"加载 {len(positive_files)} 个正样本...")
        
        # 加载正样本
        positive_images = []
        for img_path in positive_files:
            img = self.preprocess_image(img_path)
            if img is not None:
                positive_images.append(img)
        
        print(f"成功加载 {len(positive_images)} 个正样本")
        
        # 创建负样本
        negative_images = self.create_negative_samples(positive_files, negative_ratio=1.0)
        
        # 合并数据
        all_images = positive_images + negative_images
        all_labels = [1] * len(positive_images) + [0] * len(negative_images)
        
        # 转换为numpy数组
        X = np.array(all_images)
        y = keras.utils.to_categorical(all_labels, 2)
        
        print(f"训练数据准备完成: {len(X)} 个样本")
        print(f"正样本: {len(positive_images)} 个, 负样本: {len(negative_images)} 个")
        
        return X, y
    
    def create_cnn_model(self, input_shape=(224, 224, 3)):
        """创建CNN模型"""
        model = keras.Sequential([
            # 第一个卷积块
            keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
            keras.layers.BatchNormalization(),
            keras.layers.MaxPooling2D((2, 2)),
            keras.layers.Dropout(0.25),
            
            # 第二个卷积块
            keras.layers.Conv2D(64, (3, 3), activation='relu'),
            keras.layers.BatchNormalization(),
            keras.layers.MaxPooling2D((2, 2)),
            keras.layers.Dropout(0.25),
            
            # 第三个卷积块
            keras.layers.Conv2D(128, (3, 3), activation='relu'),
            keras.layers.BatchNormalization(),
            keras.layers.MaxPooling2D((2, 2)),
            keras.layers.Dropout(0.25),
            
            # 第四个卷积块
            keras.layers.Conv2D(256, (3, 3), activation='relu'),
            keras.layers.BatchNormalization(),
            keras.layers.MaxPooling2D((2, 2)),
            keras.layers.Dropout(0.25),
            
            # 全连接层
            keras.layers.Flatten(),
            keras.layers.Dense(512, activation='relu'),
            keras.layers.BatchNormalization(),
            keras.layers.Dropout(0.5),
            keras.layers.Dense(256, activation='relu'),
            keras.layers.BatchNormalization(),
            keras.layers.Dropout(0.5),
            keras.layers.Dense(2, activation='softmax')
        ])
        
        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, X: np.ndarray, y: np.ndarray, epochs: int = 50, batch_size: int = 16):
        """训练模型"""
        print("开始训练CNN模型...")
        
        # 分割数据
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y.argmax(axis=1)
        )
        
        print(f"训练集: {len(X_train)} 个样本")
        print(f"验证集: {len(X_val)} 个样本")
        
        # 创建模型
        self.model = self.create_cnn_model(X.shape[1:])
        
        # 显示模型结构
        print("\nCNN模型结构:")
        self.model.summary()
        
        # 设置回调
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7
            )
        ]
        
        # 训练模型
        self.history = self.model.fit(
            X_train, y_train,
            batch_size=batch_size,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        # 评估模型
        train_loss, train_acc = self.model.evaluate(X_train, y_train, verbose=0)
        val_loss, val_acc = self.model.evaluate(X_val, y_val, verbose=0)
        
        print(f"\n=== 训练结果 ===")
        print(f"训练准确率: {train_acc:.4f}")
        print(f"验证准确率: {val_acc:.4f}")
        
        # 生成详细报告
        y_pred = self.model.predict(X_val)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(y_val, axis=1)
        
        print("\n分类报告:")
        print(classification_report(y_true_classes, y_pred_classes, 
                                  target_names=['No N-Pattern', 'Has N-Pattern']))
        
        return self.history
    
    def save_model(self, model_path: str):
        """保存模型"""
        if self.model is None:
            print("没有训练好的模型可以保存")
            return
        
        self.model.save(model_path)
        
        # 保存元数据
        metadata = {
            'model_type': 'Yellow Marker Based CNN',
            'img_size': self.img_size,
            'created_at': str(np.datetime64('now')),
            'description': 'CNN model trained on images with yellow markers indicating N-patterns'
        }
        
        metadata_path = model_path.replace('.h5', '_metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"模型已保存: {model_path}")
        print(f"元数据已保存: {metadata_path}")
    
    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可以绘制")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 准确率
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        
        # 损失
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        
        plt.tight_layout()
        plt.show()

def main():
    """主函数"""
    print("=== 基于黄色标识的自动CNN训练器 ===\n")
    
    data_dir = "D:\\双回撤"
    
    # 创建训练器
    trainer = AutoNPatternTrainer()
    
    # 准备训练数据
    print("准备训练数据...")
    X, y = trainer.prepare_training_data(data_dir)
    
    if X is None:
        print("数据准备失败")
        return
    
    # 训练模型
    print("\n开始训练...")
    trainer.train_model(X, y, epochs=50, batch_size=16)
    
    # 保存模型
    model_path = "yellow_marker_cnn_model.h5"
    trainer.save_model(model_path)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    print(f"\n=== 训练完成 ===")
    print(f"模型已保存: {model_path}")
    print("现在可以使用这个模型来预测新的图片或CSV数据了!")

if __name__ == "__main__":
    main()
