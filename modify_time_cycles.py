import re

# 读取文件内容
with open('simple_mat_fx_bi_analyzer.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找计算时间间隔的代码
pattern = r'(# 计算高点之间的时间间隔\n\s+high_intervals = \[\]\n\s+for i in range\(1, len\(highs\)\):\n\s+interval = highs\[i\]\[0\] - highs\[i-1\]\[0\]\n\s+high_intervals\.append\(interval\)\n\n\s+# 计算低点之间的时间间隔\n\s+low_intervals = \[\]\n\s+for i in range\(1, len\(lows\)\):\n\s+interval = lows\[i\]\[0\] - lows\[i-1\]\[0\]\n\s+low_intervals\.append\(interval\))'

# 替换为新的代码
replacement = """# 计算所有高点之间的时间间隔（不仅仅是相邻高点）
            high_intervals = []
            for i in range(len(highs)):
                for j in range(i+1, len(highs)):
                    interval = highs[j][0] - highs[i][0]
                    if interval <= 100:  # 限制时间范围在100以内
                        high_intervals.append(interval)
            
            # 计算所有低点之间的时间间隔（不仅仅是相邻低点）
            low_intervals = []
            for i in range(len(lows)):
                for j in range(i+1, len(lows)):
                    interval = lows[j][0] - lows[i][0]
                    if interval <= 100:  # 限制时间范围在100以内
                        low_intervals.append(interval)"""

# 替换代码
modified_content = re.sub(pattern, replacement, content)

# 写入修改后的内容
with open('simple_mat_fx_bi_analyzer.py', 'w', encoding='utf-8') as f:
    f.write(modified_content)

print("修改完成！")
