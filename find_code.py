import re

with open('simple_mat_fx_bi_analyzer.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找计算时间间隔的代码
pattern = r'# 计算.*时间间隔.*\n.*high_intervals.*\n.*for'
matches = re.findall(pattern, content, re.DOTALL)
for i, match in enumerate(matches):
    print(f"Match {i+1}:")
    print(match)
    print("-" * 50)

# 查找analyze_time_cycles方法
pattern = r'def analyze_time_cycles.*?def'
matches = re.findall(pattern, content, re.DOTALL)
if matches:
    # 只打印前500个字符，避免输出过多
    print("Found analyze_time_cycles method:")
    print(matches[0][:500])
    print("...")
