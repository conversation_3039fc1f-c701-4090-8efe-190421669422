#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式图片标注工具
帮助用户标注哪些图片包含N字形态
"""

import os
import glob
import json
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import Button
import numpy as np

class InteractiveLabeler:
    """交互式标注工具"""
    
    def __init__(self, image_dir: str):
        self.image_dir = image_dir
        self.image_files = []
        self.current_index = 0
        self.labels = {}  # 存储标注结果
        self.fig = None
        self.ax = None
        
        # 收集图片文件
        self._collect_images()
        
        # 尝试加载已有的标注
        self._load_existing_labels()
    
    def _collect_images(self):
        """收集图片文件"""
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
        for ext in extensions:
            self.image_files.extend(glob.glob(os.path.join(self.image_dir, ext)))
            self.image_files.extend(glob.glob(os.path.join(self.image_dir, ext.upper())))
        
        print(f"找到 {len(self.image_files)} 个图片文件")
    
    def _load_existing_labels(self):
        """加载已有的标注"""
        label_file = os.path.join(self.image_dir, "labels.json")
        if os.path.exists(label_file):
            try:
                with open(label_file, 'r', encoding='utf-8') as f:
                    self.labels = json.load(f)
                print(f"加载了 {len(self.labels)} 个已有标注")
            except Exception as e:
                print(f"加载标注文件失败: {e}")
    
    def _save_labels(self):
        """保存标注结果"""
        label_file = os.path.join(self.image_dir, "labels.json")
        try:
            with open(label_file, 'w', encoding='utf-8') as f:
                json.dump(self.labels, f, ensure_ascii=False, indent=2)
            print(f"标注已保存到: {label_file}")
        except Exception as e:
            print(f"保存标注失败: {e}")
    
    def start_labeling(self):
        """开始标注"""
        if not self.image_files:
            print("没有找到图片文件")
            return
        
        # 创建图形界面
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        plt.subplots_adjust(bottom=0.2)
        
        # 添加按钮
        self._create_buttons()
        
        # 显示第一张图片
        self._show_current_image()
        
        plt.show()
    
    def _create_buttons(self):
        """创建控制按钮"""
        # 按钮位置
        button_height = 0.04
        button_width = 0.1
        button_y = 0.05
        
        # 上一张按钮
        ax_prev = plt.axes([0.1, button_y, button_width, button_height])
        self.btn_prev = Button(ax_prev, '上一张')
        self.btn_prev.on_clicked(self._prev_image)
        
        # 下一张按钮
        ax_next = plt.axes([0.25, button_y, button_width, button_height])
        self.btn_next = Button(ax_next, '下一张')
        self.btn_next.on_clicked(self._next_image)
        
        # 包含N字形态按钮
        ax_yes = plt.axes([0.4, button_y, button_width, button_height])
        self.btn_yes = Button(ax_yes, '有N字形态')
        self.btn_yes.on_clicked(self._label_positive)
        
        # 不包含N字形态按钮
        ax_no = plt.axes([0.55, button_y, button_width, button_height])
        self.btn_no = Button(ax_no, '无N字形态')
        self.btn_no.on_clicked(self._label_negative)
        
        # 保存按钮
        ax_save = plt.axes([0.7, button_y, button_width, button_height])
        self.btn_save = Button(ax_save, '保存标注')
        self.btn_save.on_clicked(self._save_labels_callback)
        
        # 完成按钮
        ax_done = plt.axes([0.85, button_y, button_width, button_height])
        self.btn_done = Button(ax_done, '完成')
        self.btn_done.on_clicked(self._finish_labeling)
    
    def _show_current_image(self):
        """显示当前图片"""
        if not self.image_files:
            return
        
        current_file = self.image_files[self.current_index]
        filename = os.path.basename(current_file)
        
        try:
            # 读取图片
            img = Image.open(current_file)
            
            # 清除之前的显示
            self.ax.clear()
            
            # 显示图片
            self.ax.imshow(img)
            self.ax.axis('off')
            
            # 获取当前标注状态
            current_label = self.labels.get(filename, "未标注")
            label_color = {'positive': 'green', 'negative': 'red', '未标注': 'black'}
            label_text = {'positive': '包含N字形态', 'negative': '不包含N字形态', '未标注': '未标注'}
            
            # 设置标题
            title = f"图片 {self.current_index + 1}/{len(self.image_files)}: {filename}\n"
            title += f"当前标注: {label_text[current_label]}"
            
            self.ax.set_title(title, color=label_color[current_label], fontsize=12)
            
            # 如果有标注，添加边框
            if current_label != "未标注":
                rect = patches.Rectangle((0, 0), img.width, img.height, 
                                       linewidth=5, edgecolor=label_color[current_label], 
                                       facecolor='none')
                self.ax.add_patch(rect)
            
            plt.draw()
            
        except Exception as e:
            print(f"显示图片失败 {current_file}: {e}")
    
    def _prev_image(self, event):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self._show_current_image()
    
    def _next_image(self, event):
        """下一张图片"""
        if self.current_index < len(self.image_files) - 1:
            self.current_index += 1
            self._show_current_image()
    
    def _label_positive(self, event):
        """标注为包含N字形态"""
        filename = os.path.basename(self.image_files[self.current_index])
        self.labels[filename] = "positive"
        print(f"标注 {filename} 为: 包含N字形态")
        self._show_current_image()
        
        # 自动跳到下一张
        if self.current_index < len(self.image_files) - 1:
            self.current_index += 1
            self._show_current_image()
    
    def _label_negative(self, event):
        """标注为不包含N字形态"""
        filename = os.path.basename(self.image_files[self.current_index])
        self.labels[filename] = "negative"
        print(f"标注 {filename} 为: 不包含N字形态")
        self._show_current_image()
        
        # 自动跳到下一张
        if self.current_index < len(self.image_files) - 1:
            self.current_index += 1
            self._show_current_image()
    
    def _save_labels_callback(self, event):
        """保存标注回调"""
        self._save_labels()
    
    def _finish_labeling(self, event):
        """完成标注"""
        self._save_labels()
        
        # 统计标注结果
        positive_count = sum(1 for label in self.labels.values() if label == "positive")
        negative_count = sum(1 for label in self.labels.values() if label == "negative")
        unlabeled_count = len(self.image_files) - len(self.labels)
        
        print(f"\n=== 标注统计 ===")
        print(f"包含N字形态: {positive_count}")
        print(f"不包含N字形态: {negative_count}")
        print(f"未标注: {unlabeled_count}")
        print(f"总计: {len(self.image_files)}")
        
        plt.close()
    
    def get_labeled_files(self):
        """获取标注好的文件列表"""
        positive_files = []
        negative_files = []
        
        for filename, label in self.labels.items():
            file_path = os.path.join(self.image_dir, filename)
            if os.path.exists(file_path):
                if label == "positive":
                    positive_files.append(file_path)
                elif label == "negative":
                    negative_files.append(file_path)
        
        return positive_files, negative_files

def main():
    """主函数"""
    print("=== 交互式图片标注工具 ===\n")
    
    image_dir = "D:\\双回撤"
    
    if not os.path.exists(image_dir):
        print(f"目录不存在: {image_dir}")
        return
    
    print("使用说明:")
    print("1. 查看每张图片")
    print("2. 如果图片中的黄色箭头指向的是您要的N字形态，点击'有N字形态'")
    print("3. 如果不是您要的形态，点击'无N字形态'")
    print("4. 使用'上一张'/'下一张'浏览图片")
    print("5. 点击'保存标注'保存进度")
    print("6. 完成后点击'完成'")
    print("\n开始标注...")
    
    labeler = InteractiveLabeler(image_dir)
    labeler.start_labeling()
    
    # 获取标注结果
    positive_files, negative_files = labeler.get_labeled_files()
    
    print(f"\n标注完成!")
    print(f"正样本: {len(positive_files)} 个")
    print(f"负样本: {len(negative_files)} 个")
    
    if len(positive_files) > 0 and len(negative_files) > 0:
        print("\n现在可以使用这些标注数据训练CNN模型了!")
        print("运行: python n_pattern_cnn_trainer.py")
    else:
        print("\n需要至少有一些正样本和负样本才能训练模型")

if __name__ == "__main__":
    main()
