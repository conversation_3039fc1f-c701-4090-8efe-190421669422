# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
from czsc.traders.base import CzscTrader
from czsc.objects import RawBar, Position
from czsc.enum import Freq, Operate, Direction
from czsc.utils.bar_generator import BarGenerator
from datetime import datetime

# 创建示例数据
def create_sample_data(n=1000):
    """创建示例K线数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2020-01-01', periods=n, freq='D')
    close = np.random.normal(loc=100, scale=5, size=n).cumsum() + 1000
    open_prices = close - np.random.normal(loc=0, scale=1, size=n)
    high = np.maximum(close, open_prices) + np.random.normal(loc=1, scale=0.5, size=n)
    low = np.minimum(close, open_prices) - np.random.normal(loc=1, scale=0.5, size=n)
    
    bars = []
    for i in range(n):
        bar = RawBar(symbol="000001.SH", id=i, dt=dates[i], freq=Freq.D,
                    open=open_prices[i], close=close[i], high=high[i], low=low[i],
                    vol=10000, amount=close[i] * 10000)
        bars.append(bar)
    return bars

# 创建一个简单的策略
class SimpleStrategy(CzscTrader):
    def __init__(self, bars):
        # 初始化K线合成器
        bg = BarGenerator(base_freq='日线', freqs=['日线', '周线'])
        for bar in bars:
            bg.update(bar)
        
        # 初始化信号配置
        signals_config = [
            {"name": "均线多空", "params": {"ma_type": "SMA", "ma_period": 20}},
        ]
        
        # 初始化持仓
        positions = [
            Position(name="多头", symbol="000001.SH", pos_type="多头")
        ]
        
        super().__init__(bg=bg, positions=positions, signals_config=signals_config)
    
    # 自定义信号计算
    def on_bar(self, bar):
        # 这里简化处理，实际应用中应该根据信号配置计算信号
        close = self.kas['日线'].bars_raw[-1].close
        ma20 = np.mean([x.close for x in self.kas['日线'].bars_raw[-20:]])
        
        if close > ma20:
            self.positions[0].operates.append(Operate.LO)  # 开多
        elif close < ma20 and self.positions[0].pos > 0:
            self.positions[0].operates.append(Operate.LE)  # 平多
        else:
            self.positions[0].operates.append(Operate.HO)  # 持币

# 执行回测
bars = create_sample_data(500)
strategy = SimpleStrategy(bars)

# 获取回测结果
trades = strategy.get_trades()
if trades:
    print(f"总交易次数: {len(trades)}")
    print(f"盈利次数: {sum(1 for t in trades if t['profit'] > 0)}")
    print(f"亏损次数: {sum(1 for t in trades if t['profit'] < 0)}")
    
    total_profit = sum(t['profit'] for t in trades)
    print(f"总盈亏: {total_profit:.2f}")
else:
    print("没有产生交易")
