#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新保存模型，避免序列化问题
"""

import joblib
import pickle

def resave_model():
    """重新保存模型，只保存必要的组件"""
    try:
        # 加载原始模型
        model_data = joblib.load("n_pattern_feature_model.pkl")
        
        # 只保存模型和缩放器
        simplified_model = {
            'model': model_data['model'],
            'model_name': model_data['model_name'],
            'scaler': model_data['scaler']
        }
        
        # 保存简化版本
        joblib.dump(simplified_model, "n_pattern_model_simple.pkl")
        
        print("简化模型保存成功: n_pattern_model_simple.pkl")
        
        # 验证加载
        test_load = joblib.load("n_pattern_model_simple.pkl")
        print(f"验证加载成功: {test_load['model_name']}")
        
    except Exception as e:
        print(f"重新保存失败: {e}")

if __name__ == "__main__":
    resave_model()
