# 基于CNN的N字形态识别系统

这是一个专门为您的标注数据设计的CNN（卷积神经网络）N字形态识别系统。系统会首先分析您在"D:\双回撤"目录中标注的图片，然后训练一个深度学习模型来识别相似的N字形态。

## 🎯 系统特点

- **基于您的实际标注数据**：直接使用您标识的形态进行训练
- **CNN深度学习**：使用卷积神经网络，比传统方法更准确
- **图像和CSV双支持**：既能识别图片，也能分析CSV数据
- **自动数据分析**：先分析您的标注数据特征
- **端到端解决方案**：从数据分析到模型训练到预测应用

## 📁 文件说明

- `n_pattern_cnn_trainer.py` - 主要的CNN训练器
- `cnn_predict_csv.py` - 使用训练好的模型预测CSV数据
- `CNN_N_Pattern_README.md` - 说明文档

## 🚀 安装依赖

```bash
pip install tensorflow opencv-python matplotlib pandas numpy scikit-learn pillow
```

## 📊 使用流程

### 第1步：分析您的标注数据

```bash
python n_pattern_cnn_trainer.py
```

系统会自动：
1. 扫描"D:\双回撤"目录中的所有图片
2. 分析图片的尺寸、亮度、对比度等特征
3. 生成详细的数据分析报告
4. 保存分析结果到JSON文件

### 第2步：训练CNN模型

运行后选择"1. 使用所有图片作为正样本训练模型"

系统会：
1. 将您的标注图片作为正样本（包含N字形态）
2. 通过数据增强生成负样本（不包含N字形态）
3. 训练CNN模型
4. 显示训练过程和准确率
5. 保存训练好的模型

### 第3步：使用模型预测

#### 预测图片：
```bash
python n_pattern_cnn_trainer.py
# 选择 "2. 手动指定正负样本目录" 然后进行预测
```

#### 预测CSV数据：
```bash
python cnn_predict_csv.py
```

## 🔧 详细功能

### 数据分析功能

系统会分析您的标注数据：
- 图片数量和格式
- 平均尺寸和文件大小
- 亮度和对比度分布
- 边缘密度和线条特征
- 生成分析报告

### CNN模型架构

```
输入层 (224x224x3)
    ↓
卷积层1 (32个3x3卷积核) + BatchNorm + MaxPool + Dropout
    ↓
卷积层2 (64个3x3卷积核) + BatchNorm + MaxPool + Dropout
    ↓
卷积层3 (128个3x3卷积核) + BatchNorm + MaxPool + Dropout
    ↓
卷积层4 (256个3x3卷积核) + BatchNorm + MaxPool + Dropout
    ↓
全连接层1 (512神经元) + BatchNorm + Dropout
    ↓
全连接层2 (256神经元) + BatchNorm + Dropout
    ↓
输出层 (2分类：N字形态/非N字形态)
```

### 数据增强策略

为了生成负样本，系统使用以下变换：
- 水平/垂直翻转
- 随机旋转（-30°到30°）
- 添加高斯噪声
- 高斯模糊
- 随机裁剪和缩放

### CSV转图像功能

`cnn_predict_csv.py`可以：
1. 读取CSV文件中的价格数据
2. 自动识别价格列（支持中英文列名）
3. 生成K线图图像
4. 使用CNN模型预测N字形态
5. 返回预测结果和置信度

## 📈 预测结果格式

```json
{
  "file_path": "path/to/file",
  "filename": "example.jpg",
  "probability_negative": 0.15,
  "probability_positive": 0.85,
  "confidence": 0.85,
  "prediction": "N字形态",
  "is_n_pattern": true
}
```

## 🎛️ 高级配置

### 调整图像尺寸
```python
trainer = NPatternCNNTrainer(img_size=(256, 256))
```

### 调整训练参数
```python
trainer.train_model(X, y, epochs=50, batch_size=16, validation_split=0.3)
```

### 调整预测阈值
```python
result = trainer.predict_image(img_path, threshold=0.7)  # 更严格的判断
```

## 📊 模型评估

训练完成后会显示：
- 训练准确率和验证准确率
- 损失函数变化曲线
- 分类报告（精确率、召回率、F1分数）
- 混淆矩阵

## 🔍 使用技巧

1. **数据质量**：确保您的标注图片清晰，N字形态明显
2. **样本数量**：建议至少有20-30张标注图片
3. **图片一致性**：尽量使用相似风格的图片（相同的图表类型、颜色方案等）
4. **阈值调整**：根据实际需要调整预测阈值，平衡精确率和召回率

## 🚨 注意事项

1. **GPU加速**：如果有GPU，训练会更快
2. **内存需求**：大量图片可能需要较多内存
3. **训练时间**：根据数据量，训练可能需要几分钟到几小时
4. **模型保存**：训练好的模型会保存为.h5文件，可重复使用

## 🔧 故障排除

### 常见问题：

1. **TensorFlow安装问题**
   ```bash
   pip install tensorflow==2.12.0
   ```

2. **CUDA版本不兼容**
   - 使用CPU版本：`pip install tensorflow-cpu`

3. **内存不足**
   - 减少batch_size
   - 减少图片尺寸

4. **图片读取失败**
   - 检查图片格式和路径
   - 确保图片没有损坏

## 📞 扩展功能

系统设计为可扩展的，您可以：
- 添加更多的数据增强方法
- 调整CNN架构
- 集成其他深度学习模型
- 添加可视化功能
- 支持更多文件格式

这个系统专门为您的需求设计，能够学习您标注的N字形态特征，并应用到新的数据中进行识别。
