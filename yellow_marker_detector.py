#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黄色标识检测器
自动检测图片中的黄色线条/箭头，提取标识的形态区域
"""

import os
import glob
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import json

class YellowMarkerDetector:
    """黄色标识检测器"""
    
    def __init__(self):
        # 黄色的HSV范围
        self.yellow_lower = np.array([20, 100, 100])  # 黄色下限
        self.yellow_upper = np.array([30, 255, 255])  # 黄色上限
        
    def detect_yellow_markers(self, image_path: str):
        """检测图片中的黄色标识"""
        try:
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                # 尝试用不同编码读取
                img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                if img is None:
                    return None
            
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 创建黄色掩码
            yellow_mask = cv2.inRange(hsv, self.yellow_lower, self.yellow_upper)
            
            # 形态学操作，去除噪声
            kernel = np.ones((3,3), np.uint8)
            yellow_mask = cv2.morphologyEx(yellow_mask, cv2.MORPH_OPEN, kernel)
            yellow_mask = cv2.morphologyEx(yellow_mask, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(yellow_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓，保留较大的
            min_area = 50  # 最小面积
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            if not valid_contours:
                return None
            
            # 分析检测结果
            result = {
                'image_path': image_path,
                'yellow_detected': True,
                'contour_count': len(valid_contours),
                'total_yellow_area': sum(cv2.contourArea(c) for c in valid_contours),
                'contours': valid_contours,
                'yellow_mask': yellow_mask
            }
            
            return result
            
        except Exception as e:
            print(f"检测黄色标识时出错 {image_path}: {e}")
            return None
    
    def extract_marked_regions(self, image_path: str, expand_ratio: float = 0.2):
        """提取黄色标识指向的区域"""
        detection_result = self.detect_yellow_markers(image_path)
        
        if not detection_result or not detection_result['yellow_detected']:
            return None
        
        try:
            # 读取原图
            img = cv2.imread(image_path)
            if img is None:
                img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            
            height, width = img.shape[:2]
            
            # 分析所有黄色区域
            regions = []
            
            for contour in detection_result['contours']:
                # 获取轮廓的边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 扩展区域
                expand_w = int(w * expand_ratio)
                expand_h = int(h * expand_ratio)
                
                # 计算扩展后的区域
                x1 = max(0, x - expand_w)
                y1 = max(0, y - expand_h)
                x2 = min(width, x + w + expand_w)
                y2 = min(height, y + h + expand_h)
                
                # 提取区域
                region = img[y1:y2, x1:x2]
                
                region_info = {
                    'bbox': (x1, y1, x2, y2),
                    'original_bbox': (x, y, x+w, y+h),
                    'region': region,
                    'area': cv2.contourArea(contour)
                }
                
                regions.append(region_info)
            
            return {
                'image_path': image_path,
                'regions': regions,
                'detection_result': detection_result
            }
            
        except Exception as e:
            print(f"提取标识区域时出错 {image_path}: {e}")
            return None
    
    def visualize_detection(self, image_path: str, save_path: str = None):
        """可视化检测结果"""
        detection_result = self.detect_yellow_markers(image_path)
        
        if not detection_result:
            print(f"未检测到黄色标识: {image_path}")
            return
        
        try:
            # 读取原图
            img = cv2.imread(image_path)
            if img is None:
                img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            
            # 转换为RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # 创建可视化图像
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # 原图
            axes[0].imshow(img_rgb)
            axes[0].set_title('Original Image')
            axes[0].axis('off')
            
            # 黄色掩码
            axes[1].imshow(detection_result['yellow_mask'], cmap='gray')
            axes[1].set_title('Yellow Mask')
            axes[1].axis('off')
            
            # 检测结果
            result_img = img_rgb.copy()
            for contour in detection_result['contours']:
                # 绘制轮廓
                cv2.drawContours(result_img, [contour], -1, (255, 0, 0), 2)
                
                # 绘制边界框
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(result_img, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            axes[2].imshow(result_img)
            axes[2].set_title(f'Detection Result ({len(detection_result["contours"])} markers)')
            axes[2].axis('off')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"可视化结果已保存: {save_path}")
            
            plt.show()
            
        except Exception as e:
            print(f"可视化时出错: {e}")
    
    def batch_detect_directory(self, directory: str):
        """批量检测目录中的图片"""
        print(f"批量检测目录: {directory}")
        
        # 收集图片文件
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(glob.glob(os.path.join(directory, ext)))
            image_files.extend(glob.glob(os.path.join(directory, ext.upper())))
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        results = []
        detected_count = 0
        
        for i, img_path in enumerate(image_files):
            print(f"检测 {i+1}/{len(image_files)}: {os.path.basename(img_path)}")
            
            result = self.detect_yellow_markers(img_path)
            
            if result and result['yellow_detected']:
                detected_count += 1
                print(f"  ✅ 检测到 {result['contour_count']} 个黄色标识")
                results.append(result)
            else:
                print(f"  ❌ 未检测到黄色标识")
        
        print(f"\n检测完成: {detected_count}/{len(image_files)} 个图片包含黄色标识")
        
        # 保存检测结果
        summary = {
            'total_images': len(image_files),
            'detected_images': detected_count,
            'detection_rate': detected_count / len(image_files) if image_files else 0,
            'results': [
                {
                    'image_path': r['image_path'],
                    'contour_count': r['contour_count'],
                    'total_yellow_area': r['total_yellow_area']
                }
                for r in results
            ]
        }
        
        summary_path = os.path.join(directory, "yellow_detection_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"检测摘要已保存: {summary_path}")
        
        return results

def main():
    """主函数"""
    print("=== 黄色标识检测器 ===\n")
    
    detector = YellowMarkerDetector()
    
    # 检测用户目录
    user_dir = "D:\\双回撤"
    
    if not os.path.exists(user_dir):
        print(f"目录不存在: {user_dir}")
        return
    
    print("选择操作:")
    print("1. 批量检测所有图片中的黄色标识")
    print("2. 检测单个图片并可视化")
    print("3. 提取黄色标识指向的区域")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 批量检测
        results = detector.batch_detect_directory(user_dir)
        
        if results:
            print(f"\n发现 {len(results)} 个包含黄色标识的图片")
            print("这些图片可以作为正样本用于训练")
        
    elif choice == "2":
        # 单个图片检测和可视化
        image_files = glob.glob(os.path.join(user_dir, "*.png"))[:5]  # 取前5个作为示例
        
        for img_path in image_files:
            print(f"\n检测图片: {os.path.basename(img_path)}")
            detector.visualize_detection(img_path)
            
            cont = input("继续下一张? (y/n): ").strip().lower()
            if cont != 'y':
                break
    
    elif choice == "3":
        # 提取标识区域
        print("提取黄色标识指向的区域...")
        
        image_files = glob.glob(os.path.join(user_dir, "*.png"))
        extracted_count = 0
        
        # 创建输出目录
        output_dir = os.path.join(user_dir, "extracted_regions")
        os.makedirs(output_dir, exist_ok=True)
        
        for img_path in image_files:
            result = detector.extract_marked_regions(img_path)
            
            if result and result['regions']:
                filename = os.path.splitext(os.path.basename(img_path))[0]
                
                for j, region_info in enumerate(result['regions']):
                    region_filename = f"{filename}_region_{j+1}.png"
                    region_path = os.path.join(output_dir, region_filename)
                    
                    # 保存区域图片
                    cv2.imwrite(region_path, region_info['region'])
                    extracted_count += 1
        
        print(f"提取了 {extracted_count} 个标识区域，保存在: {output_dir}")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
