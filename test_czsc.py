# -*- coding: utf-8 -*-
import pandas as pd
from czsc.analyze import CZSC
from czsc.objects import RawBar
from czsc.enum import Freq
from datetime import datetime

# 创建一些示例K线数据
bars = []
for i in range(100):
    dt = datetime(2023, 1, 1) + pd.Timedelta(days=i)
    bar = RawBar(symbol="000001.SH", id=i, dt=dt, freq=Freq.D, 
                open=100+i, close=101+i, high=102+i, low=99+i, 
                vol=1000, amount=100000)
    bars.append(bar)

# 创建CZSC对象进行分析
czsc_obj = CZSC(bars)

# 打印分析结果
print(f"分型数量: {len(czsc_obj.fx_list)}")
print(f"笔数量: {len(czsc_obj.bi_list)}")

# 打印最后一个分型
if czsc_obj.fx_list:
    last_fx = czsc_obj.fx_list[-1]
    print(f"最后一个分型: 类型={last_fx.mark}, 时间={last_fx.dt}, 价格={last_fx.fx}")

# 打印最后一笔
if czsc_obj.bi_list:
    last_bi = czsc_obj.bi_list[-1]
    print(f"最后一笔: 方向={last_bi.direction}, 起始={last_bi.fx_a.dt}, 结束={last_bi.fx_b.dt}, 价格区间=[{last_bi.low}, {last_bi.high}]")
