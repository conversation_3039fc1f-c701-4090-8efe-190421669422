# -*- coding: utf-8 -*-
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.append("D:/czsc-master")

# 直接导入enum.py文件
sys.path.append("D:/czsc-master/czsc")
from enum import Enum

# 手动定义需要的枚举类
class Freq(Enum):
    Tick = "Tick"
    F1 = "1分钟"
    F5 = "5分钟"
    F15 = "15分钟"
    F30 = "30分钟"
    F60 = "60分钟"
    D = "日线"
    W = "周线"
    M = "月线"

class Direction(Enum):
    Up = "向上"
    Down = "向下"

# 定义一个简单的K线类
class SimpleBar:
    def __init__(self, symbol, dt, open_price, close, high, low, vol):
        self.symbol = symbol
        self.dt = dt
        self.open = open_price
        self.close = close
        self.high = high
        self.low = low
        self.vol = vol
    
    @property
    def upper(self):
        """上影线"""
        return self.high - max(self.open, self.close)
    
    @property
    def lower(self):
        """下影线"""
        return min(self.open, self.close) - self.low
    
    @property
    def solid(self):
        """实体"""
        return self.close - self.open

# 打印一些基本信息
print("基本枚举类型:")
print(f"频率类型: {[f.value for f in Freq]}")
print(f"方向类型: {[d.value for d in Direction]}")

# 创建一些示例K线数据
print("\n创建示例K线数据:")
bar = SimpleBar(
    symbol="000001.SH", 
    dt=datetime(2023, 1, 1), 
    open_price=100, 
    close=101, 
    high=102, 
    low=99, 
    vol=1000
)

print(f"K线信息: 股票={bar.symbol}, 日期={bar.dt}, 开盘={bar.open}, 收盘={bar.close}, 最高={bar.high}, 最低={bar.low}")
print(f"上影线长度: {bar.upper}")
print(f"下影线长度: {bar.lower}")
print(f"实体长度: {bar.solid}")
print(f"K线颜色: {'红色' if bar.solid > 0 else '绿色'}")
