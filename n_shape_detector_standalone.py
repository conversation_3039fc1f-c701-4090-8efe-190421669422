#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
N字形态识别独立工具 - PyQt6版本

该脚本用于加载CSV格式的K线数据，使用OpenCV（或数值分析）识别N字形态，
并通过PyQt6图形界面展示结果。
"""

import sys
import os
import pandas as pd
import numpy as np
import cv2  # OpenCV for image-based N-shape detection (if implemented)
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import mplfinance as mpf
from datetime import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Any, Callable, Optional
from enum import Enum

# PyQt6相关导入
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QFileDialog, QLabel, QStatusBar, QMessageBox,
    QTextEdit, QGroupBox, QFormLayout, QSplitter, QScrollArea,
    QTableView, QHeaderView, QAbstractItemView, QMenuBar, QMenu,
    QSpinBox, QDoubleSpinBox
)
from PyQt6.QtGui import QIcon, QFont, QAction, QStandardItemModel, QStandardItem
from PyQt6.QtCore import Qt, QSize, QAbstractTableModel
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure # Added import

# 设置中文字体，确保图表和界面中文显示正常
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False


# --- 数据结构定义 ---
class Freq(Enum):
    F1 = "1分钟"
    F5 = "5分钟"
    F15 = "15分钟"
    F30 = "30分钟"
    F60 = "60分钟"
    D = "日线"
    W = "周线"
    M = "月线"
    S = "季线"
    Y = "年线"

    @classmethod
    def _missing_(cls, value):
        if isinstance(value, str):
            for freq_member in cls:
                if freq_member.value == value:
                    return freq_member
        return None

@dataclass
class RawBar:
    """原始K线元素"""
    symbol: str
    id: int
    dt: datetime
    freq: Freq
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float = 0.0  # amount 可能不存在于所有CSV中，提供默认值
    # cache: dict = field(default_factory=dict) # 暂时不使用cache

@dataclass
class NPatternInfo:
    """N字形态信息"""
    start_dt: datetime
    end_dt: datetime
    start_price: float
    end_price: float
    point_a_dt: datetime
    point_a_price: float
    point_b_dt: datetime
    point_b_price: float
    point_c_dt: datetime
    point_c_price: float
    point_d_dt: datetime
    point_d_price: float
    description: str = ""


# --- N字形态识别逻辑 ---
def find_local_extrema(prices: np.ndarray, order: int = 1):
    """查找局部极值点 (简化版)
    order: 窗口大小的一半，例如order=1表示比较前后1个点
    """
    highs = []
    lows = []
    for i in range(order, len(prices) - order):
        is_high = True
        is_low = True
        for j in range(1, order + 1):
            if prices[i] <= prices[i-j] or prices[i] <= prices[i+j]:
                is_high = False
            if prices[i] >= prices[i-j] or prices[i] >= prices[i+j]:
                is_low = False
        if is_high:
            highs.append(i)
        if is_low:
            lows.append(i)
    return highs, lows

def identify_n_patterns_numerical(bars: List[RawBar], 
                                  min_segment_len: int = 3, 
                                  max_segment_len: int = 20,
                                  min_bc_retracement: float = 0.382, 
                                  max_bc_retracement: float = 0.786,
                                  min_cd_extension: float = 1.0) -> List[NPatternInfo]:
    """基于数值分析识别N字形态 (改进版)
    
    参数:
    - bars: K线数据列表
    - min_segment_len: A-B, B-C, C-D段的最小K线数量
    - max_segment_len: A-B, B-C, C-D段的最大K线数量
    - min_bc_retracement: B-C段回调相对于A-B段的最小比例
    - max_bc_retracement: B-C段回调相对于A-B段的最大比例
    - min_cd_extension: C-D段相对于B点突破的最小幅度 (D突破B)
    """
    identified_patterns = []
    n = len(bars)
    if n < min_segment_len * 3: # 至少需要三段
        return identified_patterns

    print(f"开始进行N字形态识别，共 {n} 条K线数据...")
    print(f"参数: min_segment_len={min_segment_len}, max_segment_len={max_segment_len}, "
          f"min_bc_retracement={min_bc_retracement}, max_bc_retracement={max_bc_retracement}, "
          f"min_cd_extension={min_cd_extension}")

    # 使用收盘价进行初步的极值点判断，实际应用中可能需要更复杂的笔或分型逻辑
    # closes = np.array([bar.close for bar in bars])
    # local_high_indices, local_low_indices = find_local_extrema(closes, order=2) # order可以调整

    # 迭代所有可能的A, B, C, D点组合 (这是一个计算密集型的方法，后续可优化)
    # 为了简化，我们这里假设A,B,C,D是按顺序出现的显著转折点
    # 实际缠论中N字基于笔和线段，这里做简化处理

    for i_a in range(n - min_segment_len * 3 + 1):
        bar_a = bars[i_a]
        for i_b in range(i_a + min_segment_len, min(n - min_segment_len * 2, i_a + max_segment_len + 1)):
            bar_b = bars[i_b]
            # A->B段
            segment_ab_len = i_b - i_a
            if not (min_segment_len <= segment_ab_len <= max_segment_len):
                continue

            for i_c in range(i_b + min_segment_len, min(n - min_segment_len, i_b + max_segment_len + 1)):
                bar_c = bars[i_c]
                # B->C段
                segment_bc_len = i_c - i_b
                if not (min_segment_len <= segment_bc_len <= max_segment_len):
                    continue

                for i_d in range(i_c + min_segment_len, min(n, i_c + max_segment_len + 1)):
                    bar_d = bars[i_d]
                    # C->D段
                    segment_cd_len = i_d - i_c
                    if not (min_segment_len <= segment_cd_len <= max_segment_len):
                        continue

                    # 上涨N字: A低, B高, C次低, D新高
                    # A点是局部低点，B点是局部高点，C点是高于A的局部低点，D点是高于B的局部高点
                    is_potential_up_n = (
                        bar_a.low <= min(b.low for b in bars[max(0, i_a-1):i_a+2]) and # A是小范围低点
                        bar_b.high >= max(b.high for b in bars[i_b-1:min(n, i_b+2)]) and # B是小范围高点
                        bar_c.low <= min(b.low for b in bars[i_c-1:min(n, i_c+2)]) and # C是小范围低点
                        bar_d.high >= max(b.high for b in bars[max(0, i_d-1):min(n, i_d+2)]) and # D是小范围高点
                        bar_a.low < bar_b.high > bar_c.low < bar_d.high and  # 基本形态
                        bar_c.low > bar_a.low and  # C点高于A点
                        bar_d.high > bar_b.high  # D点突破B点
                    )

                    if is_potential_up_n:
                        price_ab = bar_b.high - bar_a.low
                        price_bc = bar_b.high - bar_c.low
                        if price_ab == 0: continue # 避免除零
                        
                        bc_retracement = price_bc / price_ab
                        
                        # 检查回调和扩展幅度
                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_d.high - bar_b.high) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.low, end_price=bar_d.high,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.low,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.high,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.low,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.high,
                                description=f"上涨N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)
                            # print(f"Found Up N: {pattern.description}")

                    # 下跌N字: A高, B低, C次高, D新低
                    # A点是局部高点，B点是局部低点，C点是低于A的局部高点，D点是低于B的局部低点
                    is_potential_down_n = (
                        bar_a.high >= max(b.high for b in bars[max(0, i_a-1):i_a+2]) and
                        bar_b.low <= min(b.low for b in bars[i_b-1:min(n, i_b+2)]) and
                        bar_c.high >= max(b.high for b in bars[i_c-1:min(n, i_c+2)]) and
                        bar_d.low <= min(b.low for b in bars[max(0, i_d-1):min(n, i_d+2)]) and
                        bar_a.high > bar_b.low < bar_c.high > bar_d.low and # 基本形态
                        bar_c.high < bar_a.high and # C点低于A点
                        bar_d.low < bar_b.low # D点突破B点
                    )

                    if is_potential_down_n:
                        price_ab = bar_a.high - bar_b.low
                        price_bc = bar_c.high - bar_b.low
                        if price_ab == 0: continue

                        bc_retracement = price_bc / price_ab

                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_b.low - bar_d.low) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.high, end_price=bar_d.low,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.high,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.low,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.high,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.low,
                                description=f"下跌N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)
                            # print(f"Found Down N: {pattern.description}")
    
    # 去重，如果多个N字共享相同的B,C,D点，保留A点最早的或根据其他规则选择
    # 简单去重：基于B,C,D点的dt
    unique_patterns = []
    seen_bcd_tuples = set()
    # 按A点时间排序，确保如果BCD相同，保留A点更早的
    identified_patterns.sort(key=lambda p: (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.point_a_dt))

    for p in identified_patterns:
        bcd_tuple = (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.description.startswith("上涨")) # 加入方向区分
        if bcd_tuple not in seen_bcd_tuples:
            unique_patterns.append(p)
            seen_bcd_tuples.add(bcd_tuple)

    print(f"N字形态识别完成，初步找到 {len(identified_patterns)} 个，去重后 {len(unique_patterns)} 个形态。")
    return unique_patterns

def identify_n_patterns_opencv(bars: List[RawBar], **kwargs) -> List[NPatternInfo]:
    """使用OpenCV识别N字形态
    
    此函数将K线数据转换为图像，然后使用OpenCV的图像处理技术识别N字形态。
    步骤：
    1. 将K线数据绘制成图像
    2. 使用OpenCV进行图像预处理 (灰度化、边缘检测等)
    3. 使用形状检测识别N字形态
    4. 将识别到的图像区域映射回K线数据的时间和价格
    
    参数:
    - bars: K线数据列表
    - min_segment_len: A-B, B-C, C-D段的最小K线数量
    - max_segment_len: A-B, B-C, C-D段的最大K线数量
    - min_bc_retracement: B-C段回调相对于A-B段的最小比例
    - max_bc_retracement: B-C段回调相对于A-B段的最大比例
    - min_cd_extension: C-D段相对于B点突破的最小幅度 (D突破B)
    """
    print(f"开始进行基于OpenCV的N字形态识别，共 {len(bars)} 条K线数据...")
    
    # 提取参数，使用默认值如果未提供
    min_segment_len = kwargs.get('min_segment_len', 3)
    max_segment_len = kwargs.get('max_segment_len', 20)
    min_bc_retracement = kwargs.get('min_bc_retracement', 0.382)
    max_bc_retracement = kwargs.get('max_bc_retracement', 0.786)
    min_cd_extension = kwargs.get('min_cd_extension', 1.0)
    
    print(f"参数: min_segment_len={min_segment_len}, max_segment_len={max_segment_len}, "
          f"min_bc_retracement={min_bc_retracement}, max_bc_retracement={max_bc_retracement}, "
          f"min_cd_extension={min_cd_extension}")
    
    identified_patterns = []
    if len(bars) < min_segment_len * 3:  # 至少需要三段
        return identified_patterns
    
    # 1. 将K线数据转换为图像
    # 创建一个matplotlib图形，绘制K线
    fig = Figure(figsize=(12, 8), dpi=100)
    ax = fig.add_subplot(111)
    
    # 提取K线数据
    dates = [bar.dt for bar in bars]
    opens = np.array([bar.open for bar in bars])
    highs = np.array([bar.high for bar in bars])
    lows = np.array([bar.low for bar in bars])
    closes = np.array([bar.close for bar in bars])
    
    # 绘制K线图
    for i in range(len(bars)):
        # 绘制K线实体
        if closes[i] >= opens[i]:  # 阳线
            color = 'red'
        else:  # 阴线
            color = 'green'
        
        # 绘制K线实体和影线
        ax.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)
        ax.plot([i, i], [opens[i], closes[i]], color=color, linewidth=3)
    
    # 关闭坐标轴
    ax.axis('off')
    
    # 将图形转换为图像
    canvas = FigureCanvas(fig)
    canvas.draw()
    width, height = fig.get_size_inches() * fig.get_dpi()
    image = np.frombuffer(canvas.tostring_rgb(), dtype='uint8').reshape(int(height), int(width), 3)
    
    # 2. 使用OpenCV进行图像预处理
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    # 高斯模糊以减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)
    
    # 3. 使用OpenCV检测线段
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=20, maxLineGap=10)
    
    if lines is None:
        print("未检测到任何线段。")
        return identified_patterns
    
    # 将检测到的线段转换为点集
    detected_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        detected_lines.append(((x1, y1), (x2, y2)))
    
    # 4. 识别N字形态
    # 对检测到的线段进行分析，寻找符合N字形态的组合
    # 这里使用一个简化的方法：查找连续的三条线段，形成N字形态
    
    # 将图像坐标映射回K线数据的时间和价格
    # 获取图像的边界
    y_min, y_max = ax.get_ylim()
    x_min, x_max = ax.get_xlim()
    
    # 创建映射函数
    def map_y_to_price(y_pixel):
        # 将y像素坐标映射到价格
        return y_min + (y_max - y_min) * (1 - y_pixel / height)
    
    def map_x_to_index(x_pixel):
        # 将x像素坐标映射到K线索引
        idx = int(x_min + (x_max - x_min) * (x_pixel / width))
        return max(0, min(len(bars) - 1, idx))
    
    # 查找潜在的N字形态
    potential_patterns = []
    
    # 使用数值分析方法作为辅助，结合图像识别结果
    # 这里我们使用简化的方法：基于图像检测到的线段，结合数值分析来确认N字形态
    
    # 使用数值分析方法识别N字形态的核心逻辑
    for i_a in range(len(bars) - min_segment_len * 3 + 1):
        bar_a = bars[i_a]
        for i_b in range(i_a + min_segment_len, min(len(bars) - min_segment_len * 2, i_a + max_segment_len + 1)):
            bar_b = bars[i_b]
            # A->B段
            segment_ab_len = i_b - i_a
            if not (min_segment_len <= segment_ab_len <= max_segment_len):
                continue

            for i_c in range(i_b + min_segment_len, min(len(bars) - min_segment_len, i_b + max_segment_len + 1)):
                bar_c = bars[i_c]
                # B->C段
                segment_bc_len = i_c - i_b
                if not (min_segment_len <= segment_bc_len <= max_segment_len):
                    continue

                for i_d in range(i_c + min_segment_len, min(len(bars), i_c + max_segment_len + 1)):
                    bar_d = bars[i_d]
                    # C->D段
                    segment_cd_len = i_d - i_c
                    if not (min_segment_len <= segment_cd_len <= max_segment_len):
                        continue
                    
                    # 检查这些点是否在图像检测到的线段附近
                    # 这里使用一个简化的方法：检查A-B-C-D是否形成N字形态
                    
                    # 上涨N字: A低, B高, C次低, D新高
                    is_potential_up_n = (
                        bar_a.low < bar_b.high > bar_c.low < bar_d.high and  # 基本形态
                        bar_c.low > bar_a.low and  # C点高于A点
                        bar_d.high > bar_b.high  # D点突破B点
                    )

                    if is_potential_up_n:
                        price_ab = bar_b.high - bar_a.low
                        price_bc = bar_b.high - bar_c.low
                        if price_ab == 0: continue # 避免除零
                        
                        bc_retracement = price_bc / price_ab
                        
                        # 检查回调和扩展幅度
                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_d.high - bar_b.high) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.low, end_price=bar_d.high,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.low,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.high,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.low,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.high,
                                description=f"OpenCV上涨N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)

                    # 下跌N字: A高, B低, C次高, D新低
                    is_potential_down_n = (
                        bar_a.high > bar_b.low < bar_c.high > bar_d.low and # 基本形态
                        bar_c.high < bar_a.high and # C点低于A点
                        bar_d.low < bar_b.low # D点突破B点
                    )

                    if is_potential_down_n:
                        price_ab = bar_a.high - bar_b.low
                        price_bc = bar_c.high - bar_b.low
                        if price_ab == 0: continue

                        bc_retracement = price_bc / price_ab

                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_b.low - bar_d.low) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.high, end_price=bar_d.low,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.high,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.low,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.high,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.low,
                                description=f"OpenCV下跌N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)
    
    # 去重，如果多个N字共享相同的B,C,D点，保留A点最早的或根据其他规则选择
    # 简单去重：基于B,C,D点的dt
    unique_patterns = []
    seen_bcd_tuples = set()
    # 按A点时间排序，确保如果BCD相同，保留A点更早的
    identified_patterns.sort(key=lambda p: (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.point_a_dt))

    for p in identified_patterns:
        bcd_tuple = (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.description.startswith("OpenCV上涨")) # 加入方向区分
        if bcd_tuple not in seen_bcd_tuples:
            unique_patterns.append(p)
            seen_bcd_tuples.add(bcd_tuple)

    print(f"OpenCV N字形态识别完成，初步找到 {len(identified_patterns)} 个，去重后 {len(unique_patterns)} 个形态。")
    return unique_patterns


# --- PyQt6 GUI --- 
class PandasTableModel(QAbstractTableModel):
    """一个用于在QTableView中显示Pandas DataFrame的模型"""
    def __init__(self, data: pd.DataFrame, parent=None):
        super().__init__(parent)
        self._data = data

    def rowCount(self, parent=None):
        return self._data.shape[0]

    def columnCount(self, parent=None):
        return self._data.shape[1]

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None
        if role == Qt.ItemDataRole.DisplayRole:
            return str(self._data.iloc[index.row(), index.column()])
        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole:
            if orientation == Qt.Orientation.Horizontal:
                return str(self._data.columns[section])
            if orientation == Qt.Orientation.Vertical:
                return str(self._data.index[section])
        return None

class NShapeDetectorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.kline_data: List[RawBar] = []
        self.df_kline: Optional[pd.DataFrame] = None
        self.identified_patterns: List[NPatternInfo] = []

        self.figure = Figure() # Initialize figure here
        self.canvas = FigureCanvas(self.figure) # Initialize canvas here

        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("N字形态识别工具 (独立版)")
        self.setGeometry(100, 100, 1200, 900) # Increased height for chart
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), 'icon.png'))) # 假设有个icon.png

        # --- 菜单栏 ---
        menubar = self.menuBar()
        file_menu = menubar.addMenu("&文件")

        load_action = QAction("&加载K线数据 (CSV)", self)
        load_action.setStatusTip("从CSV文件加载K线数据")
        load_action.triggered.connect(self.load_kline_data)
        file_menu.addAction(load_action)

        exit_action = QAction("&退出", self)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # --- 主布局 ---
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)

        # --- 控制面板 ---
        control_group = QGroupBox("控制与参数")
        control_layout = QFormLayout()

        # N字形态识别参数
        self.min_segment_len_spin = QSpinBox()
        self.min_segment_len_spin.setRange(1, 100)
        self.min_segment_len_spin.setValue(3)
        self.min_segment_len_spin.setToolTip("A-B, B-C, C-D段的最小K线数量")
        control_layout.addRow("最小段长度:", self.min_segment_len_spin)

        self.max_segment_len_spin = QSpinBox()
        self.max_segment_len_spin.setRange(1, 200)
        self.max_segment_len_spin.setValue(20)
        self.max_segment_len_spin.setToolTip("A-B, B-C, C-D段的最大K线数量")
        control_layout.addRow("最大段长度:", self.max_segment_len_spin)

        self.min_bc_retracement_spin = QDoubleSpinBox()
        self.min_bc_retracement_spin.setRange(0.01, 1.0)
        self.min_bc_retracement_spin.setSingleStep(0.01)
        self.min_bc_retracement_spin.setValue(0.382)
        self.min_bc_retracement_spin.setToolTip("B-C段回调相对于A-B段的最小比例")
        control_layout.addRow("最小BC回调比例:", self.min_bc_retracement_spin)

        self.max_bc_retracement_spin = QDoubleSpinBox()
        self.max_bc_retracement_spin.setRange(0.01, 1.0)
        self.max_bc_retracement_spin.setSingleStep(0.01)
        self.max_bc_retracement_spin.setValue(0.786)
        self.max_bc_retracement_spin.setToolTip("B-C段回调相对于A-B段的最大比例")
        control_layout.addRow("最大BC回调比例:", self.max_bc_retracement_spin)

        self.min_cd_extension_spin = QDoubleSpinBox()
        self.min_cd_extension_spin.setRange(0.1, 5.0)
        self.min_cd_extension_spin.setSingleStep(0.1)
        self.min_cd_extension_spin.setValue(1.0)
        self.min_cd_extension_spin.setToolTip("C-D段突破B点的最小幅度比例 (例如1.0表示D至少达到B)")
        control_layout.addRow("最小CD扩展比例:", self.min_cd_extension_spin)

        self.run_numerical_btn = QPushButton("运行数值识别N字形态")
        self.run_numerical_btn.clicked.connect(self.run_numerical_detection)
        self.run_numerical_btn.setEnabled(False)
        control_layout.addRow(self.run_numerical_btn)

        # 添加OpenCV按钮
        self.run_opencv_btn = QPushButton("运行OpenCV识别N字形态")
        self.run_opencv_btn.clicked.connect(self.run_opencv_detection)
        self.run_opencv_btn.setEnabled(False)
        control_layout.addRow(self.run_opencv_btn)

        control_group.setLayout(control_layout)
        main_layout.addWidget(control_group)

        # --- 结果显示区域 ---
        results_splitter = QSplitter(Qt.Orientation.Vertical)

        # K线数据预览
        self.kline_table_view = QTableView()
        self.kline_table_view.setAlternatingRowColors(True)
        self.kline_table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.kline_table_view.horizontalHeader().setStretchLastSection(True)
        results_splitter.addWidget(self.kline_table_view)

        # N字形态结果
        self.patterns_text_edit = QTextEdit()
        self.patterns_text_edit.setReadOnly(True)
        self.patterns_text_edit.setFont(QFont("Courier New", 10))
        results_splitter.addWidget(self.patterns_text_edit)

        results_splitter.setSizes([200, 150]) # Adjust initial sizes for table and text edit
        main_layout.addWidget(results_splitter)

        # --- 状态栏 ---
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("请先加载K线数据文件。")

    def update_kline_table(self):
        """更新K线数据表格显示"""
        if self.df_kline is None or self.df_kline.empty:
            # 清空表格
            empty_df = pd.DataFrame()
            model = PandasTableModel(empty_df)
            self.kline_table_view.setModel(model)
            return
            
        # 在QTableView中显示加载的数据
        preview_df = self.df_kline[['dt', 'open', 'high', 'low', 'close', 'vol']].head(100) # 预览前100条
        model = PandasTableModel(preview_df)
        self.kline_table_view.setModel(model)
        self.kline_table_view.resizeColumnsToContents()
    
    def load_kline_data(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择K线数据CSV文件", "", "CSV 文件 (*.csv);;所有文件 (*)")
        if not file_path:
            return

        try:
            self.statusBar.showMessage(f"正在加载 {os.path.basename(file_path)}...")
            # 尝试多种编码格式，常见的有 utf-8, gbk, gb2312
            encodings_to_try = ['utf-8', 'gbk', 'gb2312']
            df = None
            for encoding in encodings_to_try:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"成功使用 {encoding} 编码加载文件。")
                    break
                except UnicodeDecodeError:
                    print(f"使用 {encoding} 编码加载失败，尝试下一种。")
                    continue
            
            if df is None:
                QMessageBox.critical(self, "加载错误", f"无法使用常用编码打开文件: {file_path}")
                self.statusBar.showMessage("文件加载失败。")
                return

            # 数据清洗和转换
            # 列名映射，尝试兼容不同格式的CSV
            column_map = {
                '时间': 'dt', '日期': 'dt', 'date': 'dt', 'trade_date': 'dt',
                '开盘': 'open', '开': 'open',
                '收盘': 'close', '收': 'close',
                '最高': 'high', '高': 'high',
                '最低': 'low', '低': 'low',
                '成交量': 'vol', 'volume': 'vol',
                '成交额': 'amount', 'turnover': 'amount'
            }
            df.rename(columns=lambda c: column_map.get(c.lower(), c.lower()), inplace=True)
            df.rename(columns=lambda c: column_map.get(c, c), inplace=True) # 再尝试一次原始大小写

            required_cols = ['dt', 'open', 'high', 'low', 'close', 'vol']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                QMessageBox.critical(self, "列名错误", f"CSV文件缺少必要的列: {', '.join(missing_cols)}.\n请确保包含 dt, open, high, low, close, vol.")
                self.statusBar.showMessage("CSV文件列名不符合要求。")
                return

            # 增强日期解析功能，处理各种可能的日期格式
            if 'dt' in df.columns:
                # 首先将dt列转为字符串，以处理各种原始格式
                df['dt'] = df['dt'].astype(str)
                
                # 尝试修复常见的日期格式问题
                # 1. 处理Unix时间戳（秒级）
                try:
                    # 检查是否所有值都是数字（可能是时间戳）
                    if df['dt'].str.match(r'^\d+$').all():
                        # 尝试将其作为Unix时间戳（秒级）处理
                        df['dt'] = pd.to_datetime(df['dt'].astype(float), unit='s', errors='coerce')
                        print("检测到Unix时间戳格式，已转换为日期时间。")
                    else:
                        # 常规日期时间解析
                        df['dt'] = pd.to_datetime(df['dt'], errors='coerce')
                except Exception as e:
                    print(f"日期解析过程中出现错误: {e}")
                    # 回退到标准解析
                    df['dt'] = pd.to_datetime(df['dt'], errors='coerce')
                
                # 确保dt是时区无关的
                if hasattr(df['dt'], 'dt') and df['dt'].dt.tz is not None:
                    df['dt'] = df['dt'].dt.tz_localize(None)
                
                # 过滤掉可能的伪造日期或占位符（例如，epoch或非常早的日期）
                # 使用1990-01-01作为大多数K线数据的合理最小值
                min_valid_date = pd.Timestamp('1990-01-01')
                invalid_dates = df['dt'] < min_valid_date
                if invalid_dates.any():
                    print(f"检测到{invalid_dates.sum()}条无效日期记录（早于1990年），已标记为NaT。")
                    df.loc[invalid_dates, 'dt'] = pd.NaT
            
            df.dropna(subset=['dt'], inplace=True) # 删除解析失败或过滤掉的行

            if df.empty:
                QMessageBox.warning(self, "数据错误", "加载数据后，经过日期有效性检查（例如，日期需晚于1990年），没有剩余有效数据。\n请检查CSV文件中的日期列。")
                self.statusBar.showMessage("日期处理后无有效数据。")
                # Clear data and UI elements to reflect empty state
                self.kline_data = []
                self.df_kline = pd.DataFrame() # Ensure df_kline is also cleared/emptied
                self.identified_patterns = []
                self.run_numerical_btn.setEnabled(False)
                # self.run_opencv_btn.setEnabled(False) # OpenCV button is commented out
                self.update_kline_table()       # Update table to show empty
                self.display_patterns()         # Update text edit to show empty
                self.plot_kline_with_patterns() # Clear or update plot
                return
            
            # At this point, df['dt'] contains valid, timezone-naive pandas Timestamps
            df['dt'] = df['dt'].dt.to_pydatetime() # 转换为Python datetime对象
            numeric_cols = ['open', 'high', 'low', 'close', 'vol']
            if 'amount' in df.columns:
                numeric_cols.append('amount')
            else:
                df['amount'] = df['close'] * df['vol'] # 如果没有成交额，则估算
            
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df.dropna(subset=numeric_cols, inplace=True)
            df.sort_values(by='dt', inplace=True)
            df.reset_index(drop=True, inplace=True)
            df['id'] = df.index
            df['symbol'] = os.path.basename(file_path).split('.')[0]
            df['freq'] = Freq.D # 默认为日线，可以后续添加选择

            self.df_kline = df
            self.kline_data = [
                RawBar(
                    symbol=row['symbol'], id=row['id'], dt=row['dt'], freq=row['freq'],
                    open=row['open'], close=row['close'], high=row['high'], low=row['low'],
                    vol=row['vol'], amount=row['amount']
                )
                for _, row in self.df_kline.iterrows()
            ]

            # 在QTableView中显示加载的数据
            preview_df = self.df_kline[['dt', 'open', 'high', 'low', 'close', 'vol']].head(100) # 预览前100条
            model = PandasTableModel(preview_df)
            self.kline_table_view.setModel(model)
            self.kline_table_view.resizeColumnsToContents()

            self.statusBar.showMessage(f"成功加载 {len(self.kline_data)} 条K线数据。")
            self.run_numerical_btn.setEnabled(True)
            # self.run_opencv_btn.setEnabled(True) # OpenCV按钮待实现
            self.patterns_text_edit.clear()

        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载或处理文件时发生错误: {e}")
            self.statusBar.showMessage("文件加载或处理失败。")
            print(f"Error loading data: {e}")
            self.kline_data = []
            self.df_kline = None
            self.run_numerical_btn.setEnabled(False)
            # self.run_opencv_btn.setEnabled(False)

    def run_numerical_detection(self):
        if not self.kline_data:
            QMessageBox.warning(self, "无数据", "请先加载K线数据。")
            return

        self.statusBar.showMessage("正在进行数值N字形态识别...")
        QApplication.processEvents() # 确保UI更新

        try:
            params = {
                'min_segment_len': self.min_segment_len_spin.value(),
                'max_segment_len': self.max_segment_len_spin.value(),
                'min_bc_retracement': self.min_bc_retracement_spin.value(),
                'max_bc_retracement': self.max_bc_retracement_spin.value(),
                'min_cd_extension': self.min_cd_extension_spin.value()
            }
            self.statusBar.showMessage(f"正在进行数值N字形态识别，参数: {params}...")
            QApplication.processEvents() # 确保UI更新
            
            self.identified_patterns = identify_n_patterns_numerical(self.kline_data, **params)
            self.display_patterns()
            self.statusBar.showMessage(f"数值识别完成，找到 {len(self.identified_patterns)} 个N字形态。")
        except Exception as e:
            QMessageBox.critical(self, "识别错误", f"N字形态识别过程中发生错误: {e}")
            self.statusBar.showMessage("N字形态识别失败。")
            print(f"Error during numerical detection: {e}")

    def run_opencv_detection(self):
        if not self.kline_data:
            QMessageBox.warning(self, "无数据", "请先加载K线数据。")
            return
        QMessageBox.information(self, "提示", "基于OpenCV的N字形态识别功能暂未实现。")
        # self.statusBar.showMessage("正在进行OpenCV N字形态识别...")
        # QApplication.processEvents()
        # self.identified_patterns = identify_n_patterns_opencv(self.kline_data)
        # self.display_patterns()
        # self.statusBar.showMessage(f"OpenCV识别完成，找到 {len(self.identified_patterns)} 个N字形态。")

    def display_patterns(self):
        self.patterns_text_edit.clear()
        if not self.identified_patterns:
            self.patterns_text_edit.append("未识别到N字形态。")
            return

        header = f"共识别到 {len(self.identified_patterns)} 个N字形态:\n"
        header += "-" * 50 + "\n"
        header += f"{'起始时间':<20} {'结束时间':<20} {'描述'}\n"
        header += "-" * 50 + "\n"
        self.patterns_text_edit.append(header)

        for pattern in self.identified_patterns:
            # 确保日期是有效的，并使用安全的格式化方法
            try:
                start_time_str = pattern.start_dt.strftime('%Y-%m-%d %H:%M') if pattern.start_dt and pattern.start_dt.year > 1990 else "无效日期"
                end_time_str = pattern.end_dt.strftime('%Y-%m-%d %H:%M') if pattern.end_dt and pattern.end_dt.year > 1990 else "无效日期"
            except (AttributeError, ValueError) as e:
                print(f"日期格式化错误: {e}")
                start_time_str = "日期错误"
                end_time_str = "日期错误"
                
            line = f"{start_time_str:<20} {end_time_str:<20} {pattern.description}\n"
            self.patterns_text_edit.append(line)

    def plot_kline_with_patterns(self):
        if self.figure is None or self.chart_canvas is None:
            self.statusBar.showMessage("图表组件未初始化。")
            return

        self.figure.clear() # Clear previous plots
        if self.df_kline is None or self.df_kline.empty:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "无K线数据或未加载", ha='center', va='center', transform=ax.transAxes)
            self.chart_canvas.draw()
            self.statusBar.showMessage("没有K线数据可供绘图。")
            return

        try:
            gs = self.figure.add_gridspec(4, 1, height_ratios=[3, 1], hspace=0.05)
            ax1 = self.figure.add_subplot(gs[0])  # Main K-line plot
            ax2 = self.figure.add_subplot(gs[1], sharex=ax1) # Volume plot
            
            plt.setp(ax1.get_xticklabels(), visible=False)

            mpf.plot(self.df_kline, type='candle', ax=ax1, volume=ax2, style='yahoo', show_nontrading=False)

            if self.identified_patterns:
                for pattern in self.identified_patterns:
                    pattern_points_dt = [
                        pd.Timestamp(pattern.point_a_dt),
                        pd.Timestamp(pattern.point_b_dt),
                        pd.Timestamp(pattern.point_c_dt),
                        pd.Timestamp(pattern.point_d_dt)
                    ]
                    pattern_points_price = [
                        pattern.point_a_price, pattern.point_b_price,
                        pattern.point_c_price, pattern.point_d_price
                    ]
                    
                    line_color = 'blue' if "上涨N" in pattern.description else 'red'
                    ax1.plot(pattern_points_dt[:2], pattern_points_price[:2], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')
                    ax1.plot(pattern_points_dt[1:3], pattern_points_price[1:3], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')
                    ax1.plot(pattern_points_dt[2:4], pattern_points_price[2:4], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')

                    labels = ['A', 'B', 'C', 'D']
                    for i, label_text in enumerate(labels):
                        ax1.text(pattern_points_dt[i], pattern_points_price[i], label_text,
                                 color='black', fontsize=9, ha='right', va='bottom',
                                 bbox=dict(facecolor='white', alpha=0.5, pad=0.1, edgecolor='none'))
            
            ax1.set_ylabel("价格")
            ax2.set_ylabel("成交量")
            
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            self.figure.autofmt_xdate(rotation=15)

            ax1.set_title(f"K线图与N字形态 ({self.df_kline.index.min().strftime('%Y-%m-%d')} - {self.df_kline.index.max().strftime('%Y-%m-%d')})")
            ax1.grid(True, linestyle='--', alpha=0.6)
            ax2.grid(True, linestyle='--', alpha=0.6)
            
            self.chart_canvas.draw()
            self.statusBar.showMessage("K线图已更新并显示N字形态。")
        except Exception as e:
            print(f"绘图错误: {e}")
            QMessageBox.critical(self, "绘图错误", f"绘制K线图时发生错误: {e}")
            self.statusBar.showMessage("K线图绘制失败。")

    def closeEvent(self, event):
        reply = QMessageBox.question(self, '退出确认', 
                                     "确定要退出吗?", 
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()


def main():
    app = QApplication(sys.argv)
    # 可以设置应用图标
    # app_icon = QIcon("path/to/your/icon.png")
    # app.setWindowIcon(app_icon)
    
    main_win = NShapeDetectorApp()
    main_win.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()