#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
N字形态识别独立工具 - PyQt6版本

该脚本用于加载CSV格式的K线数据，使用OpenCV（或数值分析）识别N字形态，
并通过PyQt6图形界面展示结果。
"""

import sys
import os
import pandas as pd
import numpy as np
import cv2  # OpenCV for image-based N-shape detection (if implemented)
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import mplfinance as mpf
from datetime import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Any, Callable, Optional
from enum import Enum

# PyQt6相关导入
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QFileDialog, QLabel, QStatusBar, QMessageBox,
    QTextEdit, QGroupBox, QFormLayout, QSplitter, QScrollArea,
    QTableView, QHeaderView, QAbstractItemView, QMenuBar, QMenu,
    QSpinBox, QDoubleSpinBox
)
from PyQt6.QtGui import QIcon, QFont, QAction, QStandardItemModel, QStandardItem
from PyQt6.QtCore import Qt, QSize, QAbstractTableModel
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure # Added import

# 设置中文字体，确保图表和界面中文显示正常
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False


# --- 数据结构定义 ---
class Freq(Enum):
    F1 = "1分钟"
    F5 = "5分钟"
    F15 = "15分钟"
    F30 = "30分钟"
    F60 = "60分钟"
    D = "日线"
    W = "周线"
    M = "月线"
    S = "季线"
    Y = "年线"

    @classmethod
    def _missing_(cls, value):
        if isinstance(value, str):
            for freq_member in cls:
                if freq_member.value == value:
                    return freq_member
        return None

@dataclass
class RawBar:
    """原始K线元素"""
    symbol: str
    id: int
    dt: datetime
    freq: Freq
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float = 0.0  # amount 可能不存在于所有CSV中，提供默认值
    # cache: dict = field(default_factory=dict) # 暂时不使用cache

@dataclass
class NPatternInfo:
    """N字形态信息"""
    start_dt: datetime
    end_dt: datetime
    start_price: float
    end_price: float
    point_a_dt: datetime
    point_a_price: float
    point_b_dt: datetime
    point_b_price: float
    point_c_dt: datetime
    point_c_price: float
    point_d_dt: datetime
    point_d_price: float
    description: str = ""

@dataclass
class FivePointPatternInfo:
    """特定五点形态信息 (三低两高)"""
    l1_dt: datetime
    l1_price: float
    h1_dt: datetime
    h1_price: float
    l2_dt: datetime
    l2_price: float
    h2_dt: datetime
    h2_price: float
    l3_dt: datetime
    l3_price: float
    ma_at_l1: Optional[float] = None
    ma_at_l2: Optional[float] = None
    ma_at_l3: Optional[float] = None
    description: str = ""



# --- 辅助函数 ---
def calculate_moving_average(prices: np.ndarray, period: int) -> np.ndarray:
    """计算移动平均线"""
    if len(prices) < period:
        # 如果数据点少于周期，返回NaN数组，长度与prices一致
        return np.full(len(prices), np.nan)
    # 使用min_periods=1可以在数据初期也产生MA值，但标准的MA需要period个数据点
    # 这里我们坚持min_periods=period，以确保MA的有效性
    return pd.Series(prices).rolling(window=period, min_periods=period).mean().to_numpy()

def is_local_low(bars: List[RawBar], index: int, window: int = 1) -> bool:
    """检查指定索引处的K线是否为局部低点"""
    n = len(bars)
    if index < 0 or index >= n:
        return False
    current_low = bars[index].low
    # 检查左侧
    for i in range(1, window + 1):
        if index - i >= 0 and bars[index - i].low < current_low:
            return False
    # 检查右侧
    for i in range(1, window + 1):
        if index + i < n and bars[index + i].low < current_low:
            return False
    # 确保它确实是一个转折点 (至少比一个邻居低，或者在边缘)
    # 一个更简单的检查是它是否是窗口内的最低点
    start_idx = max(0, index - window)
    end_idx = min(n, index + window + 1)
    if not bars[start_idx:end_idx]: return False
    return bars[index].low == min(b.low for b in bars[start_idx:end_idx])

def is_local_high(bars: List[RawBar], index: int, window: int = 1) -> bool:
    """检查指定索引处的K线是否为局部高点"""
    n = len(bars)
    if index < 0 or index >= n:
        return False
    current_high = bars[index].high
    # 检查左侧
    for i in range(1, window + 1):
        if index - i >= 0 and bars[index - i].high > current_high:
            return False
    # 检查右侧
    for i in range(1, window + 1):
        if index + i < n and bars[index + i].high > current_high:
            return False
    start_idx = max(0, index - window)
    end_idx = min(n, index + window + 1)
    if not bars[start_idx:end_idx]: return False
    return bars[index].high == max(b.high for b in bars[start_idx:end_idx])

# --- N字形态识别逻辑 ---
def find_local_extrema(prices: np.ndarray, order: int = 1):
    """查找局部极值点 (简化版)
    order: 窗口大小的一半，例如order=1表示比较前后1个点
    """
    highs = []
    lows = []
    for i in range(order, len(prices) - order):
        is_high = True
        is_low = True
        for j in range(1, order + 1):
            if prices[i] <= prices[i-j] or prices[i] <= prices[i+j]:
                is_high = False
            if prices[i] >= prices[i-j] or prices[i] >= prices[i+j]:
                is_low = False
        if is_high:
            highs.append(i)
        if is_low:
            lows.append(i)
    return highs, lows

def identify_n_patterns_numerical(bars: List[RawBar], 
                                  min_segment_len: int = 3, 
                                  max_segment_len: int = 20,
                                  min_bc_retracement: float = 0.382, 
                                  max_bc_retracement: float = 0.786,
                                  min_cd_extension: float = 1.0) -> List[NPatternInfo]:
    """基于数值分析识别N字形态 (改进版)
    
    参数:
    - bars: K线数据列表
    - min_segment_len: A-B, B-C, C-D段的最小K线数量
    - max_segment_len: A-B, B-C, C-D段的最大K线数量
    - min_bc_retracement: B-C段回调相对于A-B段的最小比例
    - max_bc_retracement: B-C段回调相对于A-B段的最大比例
    - min_cd_extension: C-D段相对于B点突破的最小幅度 (D突破B)
    """
    identified_patterns = []
    n = len(bars)
    if n < min_segment_len * 3: # 至少需要三段
        return identified_patterns

    print(f"开始进行N字形态识别，共 {n} 条K线数据...")
    print(f"参数: min_segment_len={min_segment_len}, max_segment_len={max_segment_len}, "
          f"min_bc_retracement={min_bc_retracement}, max_bc_retracement={max_bc_retracement}, "
          f"min_cd_extension={min_cd_extension}")

    # 使用收盘价进行初步的极值点判断，实际应用中可能需要更复杂的笔或分型逻辑
    # closes = np.array([bar.close for bar in bars])
    # local_high_indices, local_low_indices = find_local_extrema(closes, order=2) # order可以调整

    # 迭代所有可能的A, B, C, D点组合 (这是一个计算密集型的方法，后续可优化)
    # 为了简化，我们这里假设A,B,C,D是按顺序出现的显著转折点
    # 实际缠论中N字基于笔和线段，这里做简化处理

    for i_a in range(n - min_segment_len * 3 + 1):
        bar_a = bars[i_a]
        for i_b in range(i_a + min_segment_len, min(n - min_segment_len * 2, i_a + max_segment_len + 1)):
            bar_b = bars[i_b]
            # A->B段
            segment_ab_len = i_b - i_a
            if not (min_segment_len <= segment_ab_len <= max_segment_len):
                continue

            for i_c in range(i_b + min_segment_len, min(n - min_segment_len, i_b + max_segment_len + 1)):
                bar_c = bars[i_c]
                # B->C段
                segment_bc_len = i_c - i_b
                if not (min_segment_len <= segment_bc_len <= max_segment_len):
                    continue

                for i_d in range(i_c + min_segment_len, min(n, i_c + max_segment_len + 1)):
                    bar_d = bars[i_d]
                    # C->D段
                    segment_cd_len = i_d - i_c
                    if not (min_segment_len <= segment_cd_len <= max_segment_len):
                        continue

                    # 上涨N字: A低, B高, C次低, D新高
                    # A点是局部低点，B点是局部高点，C点是高于A的局部低点，D点是高于B的局部高点
                    is_potential_up_n = (
                        bar_a.low <= min(b.low for b in bars[max(0, i_a-1):i_a+2]) and # A是小范围低点
                        bar_b.high >= max(b.high for b in bars[i_b-1:min(n, i_b+2)]) and # B是小范围高点
                        bar_c.low <= min(b.low for b in bars[i_c-1:min(n, i_c+2)]) and # C是小范围低点
                        bar_d.high >= max(b.high for b in bars[max(0, i_d-1):min(n, i_d+2)]) and # D是小范围高点
                        bar_a.low < bar_b.high > bar_c.low < bar_d.high and  # 基本形态
                        bar_c.low > bar_a.low and  # C点高于A点
                        bar_d.high > bar_b.high  # D点突破B点
                    )

                    if is_potential_up_n:
                        price_ab = bar_b.high - bar_a.low
                        price_bc = bar_b.high - bar_c.low
                        if price_ab == 0: continue # 避免除零
                        
                        bc_retracement = price_bc / price_ab
                        
                        # 检查回调和扩展幅度
                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_d.high - bar_b.high) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.low, end_price=bar_d.high,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.low,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.high,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.low,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.high,
                                description=f"上涨N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)
                            # print(f"Found Up N: {pattern.description}")

                    # 下跌N字: A高, B低, C次高, D新低
                    # A点是局部高点，B点是局部低点，C点是低于A的局部高点，D点是低于B的局部低点
                    is_potential_down_n = (
                        bar_a.high >= max(b.high for b in bars[max(0, i_a-1):i_a+2]) and
                        bar_b.low <= min(b.low for b in bars[i_b-1:min(n, i_b+2)]) and
                        bar_c.high >= max(b.high for b in bars[i_c-1:min(n, i_c+2)]) and
                        bar_d.low <= min(b.low for b in bars[max(0, i_d-1):min(n, i_d+2)]) and
                        bar_a.high > bar_b.low < bar_c.high > bar_d.low and # 基本形态
                        bar_c.high < bar_a.high and # C点低于A点
                        bar_d.low < bar_b.low # D点突破B点
                    )

                    if is_potential_down_n:
                        price_ab = bar_a.high - bar_b.low
                        price_bc = bar_c.high - bar_b.low
                        if price_ab == 0: continue

                        bc_retracement = price_bc / price_ab

                        if (min_bc_retracement <= bc_retracement <= max_bc_retracement and
                            (bar_b.low - bar_d.low) / price_ab >= (min_cd_extension -1) ): # D相对于B的突破幅度
                            # 确保日期对象有效，并安全地格式化日期
                            try:
                                a_date_str = bar_a.dt.strftime('%Y-%m-%d') if hasattr(bar_a.dt, 'strftime') else '无效日期'
                                b_date_str = bar_b.dt.strftime('%Y-%m-%d') if hasattr(bar_b.dt, 'strftime') else '无效日期'
                                c_date_str = bar_c.dt.strftime('%Y-%m-%d') if hasattr(bar_c.dt, 'strftime') else '无效日期'
                                d_date_str = bar_d.dt.strftime('%Y-%m-%d') if hasattr(bar_d.dt, 'strftime') else '无效日期'
                            except Exception as e:
                                print(f"日期格式化错误: {e}")
                                a_date_str = b_date_str = c_date_str = d_date_str = '无效日期'
                                
                            pattern = NPatternInfo(
                                start_dt=bar_a.dt, end_dt=bar_d.dt,
                                start_price=bar_a.high, end_price=bar_d.low,
                                point_a_dt=bar_a.dt, point_a_price=bar_a.high,
                                point_b_dt=bar_b.dt, point_b_price=bar_b.low,
                                point_c_dt=bar_c.dt, point_c_price=bar_c.high,
                                point_d_dt=bar_d.dt, point_d_price=bar_d.low,
                                description=f"下跌N: A({a_date_str})-B({b_date_str})-C({c_date_str})-D({d_date_str})"
                            )
                            identified_patterns.append(pattern)
                            # print(f"Found Down N: {pattern.description}")
    
    # 去重，如果多个N字共享相同的B,C,D点，保留A点 earliest或根据其他规则选择
    # 简单去重：基于B,C,D点的dt
    unique_patterns = []
    seen_bcd_tuples = set()
    # 按A点时间排序，确保如果BCD相同，保留A点更早的
    identified_patterns.sort(key=lambda p: (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.point_a_dt))

    for p in identified_patterns:
        bcd_tuple = (p.point_b_dt, p.point_c_dt, p.point_d_dt, p.description.startswith("上涨")) # 加入方向区分
        if bcd_tuple not in seen_bcd_tuples:
            unique_patterns.append(p)
            seen_bcd_tuples.add(bcd_tuple)

    print(f"N字形态识别完成，初步找到 {len(identified_patterns)} 个，去重后 {len(unique_patterns)} 个形态。")
    return unique_patterns

def identify_n_patterns_opencv(bars: List[RawBar], **kwargs) -> List[NPatternInfo]:
    """使用OpenCV识别N字形态
    
    此函数将K线数据转换为图像，然后使用OpenCV的图像处理技术识别N字形态。
    步骤：
    1. 将K线数据绘制成图像
    2. 使用OpenCV进行图像预处理 (灰度化、边缘检测等)
    3. 使用形状检测识别N字形态
    4. 将识别到的图像区域映射回K线数据的时间和价格
    
    参数:
    - bars: K线数据列表
    - min_segment_len: A-B, B-C, C-D段的最小K线数量
    - max_segment_len: A-B, B-C, C-D段的最大K线数量
    - min_bc_retracement: B-C段回调相对于A-B段的最小比例
    - max_bc_retracement: B-C段回调相对于A-B段的最大比例
    - min_cd_extension: C-D段相对于B点突破的最小幅度 (D突破B)
    """
    print(f"开始进行基于OpenCV的N字形态识别，共 {len(bars)} 条K线数据...")
    
    # 提取参数，使用默认值如果未提供
    min_segment_len = kwargs.get('min_segment_len', 3)
    max_segment_len = kwargs.get('max_segment_len', 20)
    min_bc_retracement = kwargs.get('min_bc_retracement', 0.382)
    max_bc_retracement = kwargs.get('max_bc_retracement', 0.786)
    min_cd_extension = kwargs.get('min_cd_extension', 1.0)
    
    print(f"参数: min_segment_len={min_segment_len}, max_segment_len={max_segment_len}, "
          f"min_bc_retracement={min_bc_retracement}, max_bc_retracement={max_bc_retracement}, "
          f"min_cd_extension={min_cd_extension}")
    
    identified_patterns = []
    if len(bars) < min_segment_len * 3:  # 至少需要三段
        return identified_patterns
    
    # 1. 将K线数据转换为图像
    # 创建一个matplotlib图形，绘制K线
    fig = Figure(figsize=(12, 8), dpi=100)
    ax = fig.add_subplot(111)
    
    # 提取K线数据
    dates = [bar.dt for bar in bars]
    opens = np.array([bar.open for bar in bars])
    highs = np.array([bar.high for bar in bars])
    lows = np.array([bar.low for bar in bars])
    closes = np.array([bar.close for bar in bars])
    
    # 绘制K线图
    for i in range(len(bars)):
        # 绘制K线实体
        if closes[i] >= opens[i]:  # 阳线
            color = 'red'
        else:  # 阴线
            color = 'green'
        
        # 绘制K线实体和影线
        ax.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)
        ax.plot([i, i], [opens[i], closes[i]], color=color, linewidth=3)
    
    # 关闭坐标轴
    ax.axis('off')
    
    # 将图形转换为图像
    canvas = FigureCanvas(fig)
    canvas.draw()
    width, height = fig.get_size_inches() * fig.get_dpi()
    buf = canvas.buffer_rgba()
    image_rgba = np.frombuffer(buf, dtype='uint8').reshape(int(height), int(width), 4)
    image = image_rgba[:, :, :3]  # Convert RGBA to RGB by slicing off the alpha channel
    
    # 2. 使用OpenCV进行图像预处理
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    # 高斯模糊以减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 边缘检测
    edges = cv2.Canny(blurred, 100, 200) # 调整Canny阈值，使其不那么敏感
    
    # 3. 使用OpenCV检测线段
    # 调整HoughLinesP参数，要求更强的线条特征
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=30, maxLineGap=5)
    
    if lines is None:
        print("未检测到任何线段。")
        return identified_patterns
    
    # 将检测到的线段转换为点集
    detected_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        detected_lines.append(((x1, y1), (x2, y2)))
    
    # 4. 识别N字形态
    # 对检测到的线段进行分析，寻找符合N字形态的组合
    # 这里使用一个简化的方法：查找连续的三条线段，形成N字形态
    
    # 将图像坐标映射回K线数据的时间和价格
    # 获取图像的边界
    y_min, y_max = ax.get_ylim()
    x_min, x_max = ax.get_xlim()
    
    # 创建映射函数
    def map_y_to_price(y_pixel):
        # 将y像素坐标映射到价格
        return y_min + (y_max - y_min) * (1 - y_pixel / height)
    
    def map_x_to_index(x_pixel):
        # 将x像素坐标映射到K线索引
        idx = int(x_min + (x_max - x_min) * (x_pixel / width))
        return max(0, min(len(bars) - 1, idx))
    
    # 查找潜在的N字形态
    print("OpenCV方法：图像处理完成。现在使用标准数值分析方法识别N字形态。")
    # Pass all relevant parameters to the numerical method
    numerical_patterns = identify_n_patterns_numerical(
        bars,
        min_segment_len=min_segment_len,
        max_segment_len=max_segment_len,
        min_bc_retracement=min_bc_retracement,
        max_bc_retracement=max_bc_retracement,
        min_cd_extension=min_cd_extension
    )
    # Here, you would map numerical_patterns (which are based on bar indices and prices)
    # back to image coordinates if needed, or simply return them as they are.
    # For now, we'll assume the numerical patterns are sufficient.
    print(f"OpenCV方法调用数值分析后，找到 {len(numerical_patterns)} 个形态。")
    return numerical_patterns

# --- 特定五点形态（三低两高）识别逻辑 ---
def identify_five_point_patterns(
    bars: List[RawBar],
    ma_period: int = 250, # 年线周期，可调整
    price_proximity_threshold: float = 0.05, # H1, H2 价格接近阈值 (5%)
    ma_proximity_threshold: float = 0.05,    # L2, L3 接近MA阈值 (5%)
    rally_threshold_from_ma: float = 0.30,   # L1拉升超过MA的幅度 (30%)
    l1_relative_low_threshold: float = 0.05, # L1比L2,L3低的幅度 (5%)
    local_extrema_window: int = 3            # 局部极值点判断窗口
) -> List[FivePointPatternInfo]:
    """识别特定的五点形态 (L1, H1, L2, H2, L3)

    条件:
    1. L1, L2, L3 是局部低点; H1, H2 是局部高点。
    2. 时间顺序: L1 -> H1 -> L2 -> H2 -> L3。
    3. H1价格与H2价格相近。
    4. L1价格显著低于L2和L3价格。
    5. 从L1拉升: H1价格 > MA在L1处的值 * (1 + rally_threshold_from_ma)。
    6. L2回踩MA: L2价格接近MA在L2处的值。
    7. H2反弹至H1附近 (已包含在条件3)。
    8. L3再次回踩MA: L3价格接近MA在L3处的值。
    """
    identified_patterns = []
    n = len(bars)
    if n < 5: # 至少需要5个点
        return identified_patterns

    print(f"开始进行特定五点形态识别，共 {n} 条K线数据...")
    print(f"参数: ma_period={ma_period}, price_proximity_threshold={price_proximity_threshold}, "
          f"ma_proximity_threshold={ma_proximity_threshold}, rally_threshold_from_ma={rally_threshold_from_ma}, "
          f"l1_relative_low_threshold={l1_relative_low_threshold}, local_extrema_window={local_extrema_window}")

    prices_close = np.array([bar.close for bar in bars])
    prices_low = np.array([bar.low for bar in bars])
    prices_high = np.array([bar.high for bar in bars])
    
    moving_avg = calculate_moving_average(prices_close, ma_period)

    for i_l1 in range(n - 4):
        if not is_local_low(bars, i_l1, window=local_extrema_window):
            continue
        bar_l1 = bars[i_l1]
        ma_at_l1 = moving_avg[i_l1]
        if np.isnan(ma_at_l1):
            continue

        for i_h1 in range(i_l1 + 1, n - 3):
            if not is_local_high(bars, i_h1, window=local_extrema_window):
                continue
            bar_h1 = bars[i_h1]
            # 条件5: 从L1拉升, H1价格 > MA在L1处的值 * (1 + rally_threshold_from_ma)
            if not (bar_h1.high > ma_at_l1 * (1 + rally_threshold_from_ma)):
                continue

            for i_l2 in range(i_h1 + 1, n - 2):
                if not is_local_low(bars, i_l2, window=local_extrema_window):
                    continue
                bar_l2 = bars[i_l2]
                ma_at_l2 = moving_avg[i_l2]
                if np.isnan(ma_at_l2):
                    continue
                # 条件6: L2回踩MA, L2价格接近MA在L2处的值
                if not (abs(bar_l2.low - ma_at_l2) / ma_at_l2 < ma_proximity_threshold):
                    continue
                # 条件4部分: L1价格显著低于L2价格
                if not (bar_l1.low < bar_l2.low * (1 - l1_relative_low_threshold)):
                    continue

                for i_h2 in range(i_l2 + 1, n - 1):
                    if not is_local_high(bars, i_h2, window=local_extrema_window):
                        continue
                    bar_h2 = bars[i_h2]
                    # 条件3: H1价格与H2价格相近
                    if not (abs(bar_h1.high - bar_h2.high) / bar_h1.high < price_proximity_threshold):
                        continue

                    for i_l3 in range(i_h2 + 1, n):
                        if not is_local_low(bars, i_l3, window=local_extrema_window):
                            continue
                        bar_l3 = bars[i_l3]
                        ma_at_l3 = moving_avg[i_l3]
                        if np.isnan(ma_at_l3):
                            continue
                        # 条件8: L3再次回踩MA, L3价格接近MA在L3处的值
                        if not (abs(bar_l3.low - ma_at_l3) / ma_at_l3 < ma_proximity_threshold):
                            continue
                        # 条件4部分: L1价格显著低于L3价格
                        if not (bar_l1.low < bar_l3.low * (1 - l1_relative_low_threshold)):
                            continue
                        
                        # 所有条件满足
                        desc = f"五点形态: L1({bar_l1.dt.strftime('%Y-%m-%d')}) H1({bar_h1.dt.strftime('%Y-%m-%d')}) " \
                               f"L2({bar_l2.dt.strftime('%Y-%m-%d')}) H2({bar_h2.dt.strftime('%Y-%m-%d')}) " \
                               f"L3({bar_l3.dt.strftime('%Y-%m-%d')})"
                        
                        pattern = FivePointPatternInfo(
                            l1_dt=bar_l1.dt, l1_price=bar_l1.low,
                            h1_dt=bar_h1.dt, h1_price=bar_h1.high,
                            l2_dt=bar_l2.dt, l2_price=bar_l2.low,
                            h2_dt=bar_h2.dt, h2_price=bar_h2.high,
                            l3_dt=bar_l3.dt, l3_price=bar_l3.low,
                            ma_at_l1=ma_at_l1,
                            ma_at_l2=ma_at_l2,
                            ma_at_l3=ma_at_l3,
                            description=desc
                        )
                        identified_patterns.append(pattern)
                        print(f"找到一个特定五点形态: {desc}")

    print(f"特定五点形态识别完成，找到 {len(identified_patterns)} 个形态。")
    return identified_patterns


# --- PyQt6 应用主窗口 --- 
class PandasTableModel(QAbstractTableModel):
    """一个用于在QTableView中显示Pandas DataFrame的模型"""
    def __init__(self, data: pd.DataFrame, parent=None):
        super().__init__(parent)
        self._data = data

    def rowCount(self, parent=None):
        return self._data.shape[0]

    def columnCount(self, parent=None):
        return self._data.shape[1]

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None
        if role == Qt.ItemDataRole.DisplayRole:
            return str(self._data.iloc[index.row(), index.column()])
        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole:
            if orientation == Qt.Orientation.Horizontal:
                return str(self._data.columns[section])
            if orientation == Qt.Orientation.Vertical:
                return str(self._data.index[section])
        return None

class NShapeDetectorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.kline_data: List[RawBar] = []
        self.df_kline: Optional[pd.DataFrame] = None
        self.identified_patterns: List[NPatternInfo] = []
        self.identified_five_point_patterns: List[FivePointPatternInfo] = []

        self.figure = Figure() # Initialize figure here
        self.canvas = FigureCanvas(self.figure) # Initialize canvas here

        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("N字形态识别工具 (独立版)")
        self.setGeometry(100, 100, 1200, 900) # Increased height for chart
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), 'icon.png'))) # 假设有个icon.png

        # --- 菜单栏 ---
        menubar = self.menuBar()
        file_menu = menubar.addMenu("&文件")

        load_action = QAction("&加载K线数据 (CSV)", self)
        load_action.setStatusTip("从CSV文件加载K线数据")
        load_action.triggered.connect(self.load_kline_data)
        file_menu.addAction(load_action)

        exit_action = QAction("&退出", self)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # --- 主布局 ---
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)

        # --- 控制面板 ---
        control_group = QGroupBox("控制与参数")
        control_layout = QFormLayout()

        # N字形态识别参数
        self.min_segment_len_spin = QSpinBox()
        self.min_segment_len_spin.setRange(1, 100)
        self.min_segment_len_spin.setValue(3)
        self.min_segment_len_spin.setToolTip("A-B, B-C, C-D段的最小K线数量")
        control_layout.addRow("最小段长度:", self.min_segment_len_spin)

        self.max_segment_len_spin = QSpinBox()
        self.max_segment_len_spin.setRange(1, 200)
        self.max_segment_len_spin.setValue(20)
        self.max_segment_len_spin.setToolTip("A-B, B-C, C-D段的最大K线数量")
        control_layout.addRow("最大段长度:", self.max_segment_len_spin)

        self.min_bc_retracement_spin = QDoubleSpinBox()
        self.min_bc_retracement_spin.setRange(0.01, 1.0)
        self.min_bc_retracement_spin.setSingleStep(0.01)
        self.min_bc_retracement_spin.setValue(0.382)
        self.min_bc_retracement_spin.setToolTip("B-C段回调相对于A-B段的最小比例")
        control_layout.addRow("最小BC回调比例:", self.min_bc_retracement_spin)

        self.max_bc_retracement_spin = QDoubleSpinBox()
        self.max_bc_retracement_spin.setRange(0.01, 1.0)
        self.max_bc_retracement_spin.setSingleStep(0.01)
        self.max_bc_retracement_spin.setValue(0.786)
        self.max_bc_retracement_spin.setToolTip("B-C段回调相对于A-B段的最大比例")
        control_layout.addRow("最大BC回调比例:", self.max_bc_retracement_spin)

        self.min_cd_extension_spinbox = QDoubleSpinBox()
        self.min_cd_extension_spinbox.setRange(1.0, 5.0)
        self.min_cd_extension_spinbox.setSingleStep(0.1)
        self.min_cd_extension_spinbox.setValue(1.0)
        control_layout.addRow("N字-最小CD扩展比例:", self.min_cd_extension_spinbox)

        # --- 自定义五点形态参数 ---
        control_layout.addRow(QLabel("<b>自定义五点形态参数:</b>"))
        self.ma_period_spinbox = QSpinBox()
        self.ma_period_spinbox.setRange(10, 500)
        self.ma_period_spinbox.setValue(250)
        self.ma_period_spinbox.setToolTip("用于L2, L3点位判断的移动平均线周期 (例如年线250日)")
        control_layout.addRow("MA周期 (年线):", self.ma_period_spinbox)

        self.ma_proximity_threshold_spinbox = QDoubleSpinBox()
        self.ma_proximity_threshold_spinbox.setRange(0.01, 0.20)
        self.ma_proximity_threshold_spinbox.setSingleStep(0.01)
        self.ma_proximity_threshold_spinbox.setValue(0.05)
        self.ma_proximity_threshold_spinbox.setToolTip("L2, L3价格在MA附近的百分比阈值")
        control_layout.addRow("MA接近阈值 (%):", self.ma_proximity_threshold_spinbox)

        self.h1_h2_similarity_threshold_spinbox = QDoubleSpinBox()
        self.h1_h2_similarity_threshold_spinbox.setRange(0.01, 0.20)
        self.h1_h2_similarity_threshold_spinbox.setSingleStep(0.01)
        self.h1_h2_similarity_threshold_spinbox.setValue(0.05)
        self.h1_h2_similarity_threshold_spinbox.setToolTip("H1和H2高点价格相似度的百分比阈值")
        control_layout.addRow("H1-H2相似阈值 (%):", self.h1_h2_similarity_threshold_spinbox)

        self.l1_h1_min_rise_ratio_spinbox = QDoubleSpinBox()
        self.l1_h1_min_rise_ratio_spinbox.setRange(0.10, 1.00)
        self.l1_h1_min_rise_ratio_spinbox.setSingleStep(0.05)
        self.l1_h1_min_rise_ratio_spinbox.setValue(0.30)
        self.l1_h1_min_rise_ratio_spinbox.setToolTip("H1高点至少高于L1处MA的百分比")
        control_layout.addRow("L1-H1最小涨幅/MA (%):", self.l1_h1_min_rise_ratio_spinbox)

        self.min_segment_len_5pt_spinbox = QSpinBox()
        self.min_segment_len_5pt_spinbox.setRange(1, 50)
        self.min_segment_len_5pt_spinbox.setValue(3)
        control_layout.addRow("五点-最小段K线数:", self.min_segment_len_5pt_spinbox)

        self.max_segment_len_5pt_spinbox = QSpinBox()
        self.max_segment_len_5pt_spinbox.setRange(5, 100)
        self.max_segment_len_5pt_spinbox.setValue(50)
        control_layout.addRow("五点-最大段K线数:", self.max_segment_len_5pt_spinbox)

        self.local_extremum_window_5pt_spinbox = QSpinBox()
        self.local_extremum_window_5pt_spinbox.setRange(1, 10)
        self.local_extremum_window_5pt_spinbox.setValue(1)
        self.local_extremum_window_5pt_spinbox.setToolTip("判断局部极值时参考的左右窗口大小")
        control_layout.addRow("五点-极值判断窗口:", self.local_extremum_window_5pt_spinbox)

        self.run_numerical_btn = QPushButton("运行数值识别N字形态")
        self.run_numerical_btn.clicked.connect(self.run_numerical_detection)
        self.run_numerical_btn.setEnabled(False)
        control_layout.addRow(self.run_numerical_btn)

        # 添加OpenCV按钮
        self.run_opencv_btn = QPushButton("运行OpenCV识别N字形态")
        self.run_opencv_btn.clicked.connect(self.run_opencv_detection)
        self.run_opencv_btn.setEnabled(False)
        control_layout.addRow(self.run_opencv_btn)

        control_group.setLayout(control_layout)
        main_layout.addWidget(control_group)

        # --- 结果显示区域 ---
        results_splitter = QSplitter(Qt.Orientation.Vertical)

        # K线数据预览
        self.kline_table_view = QTableView()
        self.kline_table_view.setAlternatingRowColors(True)
        self.kline_table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.kline_table_view.horizontalHeader().setStretchLastSection(True)
        results_splitter.addWidget(self.kline_table_view)

        # N字形态结果
        self.patterns_text_edit = QTextEdit()
        self.patterns_text_edit.setReadOnly(True)
        self.patterns_text_edit.setFont(QFont("Courier New", 10))
        results_splitter.addWidget(self.patterns_text_edit)

        results_splitter.setSizes([200, 150]) # Adjust initial sizes for table and text edit
        main_layout.addWidget(results_splitter)

        # --- 状态栏 ---
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("请先加载K线数据文件。")

    def update_kline_table(self):
        """更新K线数据表格显示"""
        if self.df_kline is None or self.df_kline.empty:
            # 清空表格
            empty_df = pd.DataFrame()
            model = PandasTableModel(empty_df)
            self.kline_table_view.setModel(model)
            return
            
        # 在QTableView中显示加载的数据
        preview_df = self.df_kline[['dt', 'open', 'high', 'low', 'close', 'vol']].head(100) # 预览前100条
        model = PandasTableModel(preview_df)
        self.kline_table_view.setModel(model)
        self.kline_table_view.resizeColumnsToContents()
    
    def load_kline_data(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择K线数据CSV文件", "", "CSV 文件 (*.csv);;所有文件 (*)")
        if not file_path:
            return

        try:
            self.statusBar.showMessage(f"正在加载 {os.path.basename(file_path)}...")
            print(f"开始加载文件: {file_path}")
            
            # 尝试多种编码格式，常见的有 utf-8, gbk, gb2312
            encodings_to_try = ['gbk', 'utf-8', 'gb2312']
            df = None
            for encoding in encodings_to_try:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"成功使用 {encoding} 编码加载文件。")
                    break
                except UnicodeDecodeError:
                    print(f"使用 {encoding} 编码加载失败，尝试下一种。")
                    continue
            
            if df is None:
                QMessageBox.critical(self, "加载错误", f"无法使用常用编码打开文件: {file_path}")
                self.statusBar.showMessage("文件加载失败。")
                return

            # 统一列名为标准格式（参考11.py的方法）
            column_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume',
                'amount': 'Amount',
                'date': 'Date',
                '日期': 'Date',
                '开盘': 'Open',
                '最高': 'High',
                '最低': 'Low',
                '收盘': 'Close',
                '成交量': 'Volume',
                '成交额': 'Amount',
                '时间': 'Date',
                'trade_date': 'Date',
                'dt': 'Date'
            }

            # 将所有列名转换为小写以进行匹配
            df.columns = df.columns.str.lower()

            # 重命名列
            df.rename(columns=column_mapping, inplace=True)

            # 确保必要的列存在
            required_columns = ['Open', 'High', 'Low', 'Close']
            missing = [col for col in required_columns if col not in df.columns]
            if missing:
                error_msg = f"缺少必要列: {', '.join(missing)}"
                QMessageBox.critical(self, "列名错误", error_msg)
                self.statusBar.showMessage("CSV文件列名不符合要求。")
                return

            # 如果没有Volume和Amount列，添加默认值
            if 'Volume' not in df.columns:
                df['Volume'] = 0
            if 'Amount' not in df.columns:
                df['Amount'] = 0

            # 转换日期列并设置索引
            if 'Date' in df.columns:
                try:
                    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
                    # 检查日期有效性
                    min_valid_date = pd.Timestamp('1990-01-01')
                    invalid_dates = df['Date'] < min_valid_date
                    if invalid_dates.any():
                        print(f"检测到{invalid_dates.sum()}条无效日期记录（早于1990年）")
                        # 显示警告消息
                        QMessageBox.warning(self, "数据错误", 
                                          f"加载数据后，经过日期有效性检查（例如，日期需晚于1990年），发现{invalid_dates.sum()}条无效数据。\n没有剩余有效数据。\n请检查CSV文件中的日期列。")
                        self.statusBar.showMessage("日期处理后无有效数据。")
                        return
                except Exception as e:
                    error_msg = f"日期转换失败: {str(e)}"
                    QMessageBox.critical(self, "日期错误", error_msg)
                    self.statusBar.showMessage("日期处理失败。")
                    return

            # 按日期排序
            df.sort_values('Date', inplace=True)
            
            # 准备RawBar所需的数据格式
            df_kline = pd.DataFrame()
            df_kline['dt'] = df['Date']
            df_kline['open'] = df['Open']
            df_kline['high'] = df['High']
            df_kline['low'] = df['Low']
            df_kline['close'] = df['Close']
            df_kline['vol'] = df['Volume']
            df_kline['amount'] = df['Amount']
            df_kline['symbol'] = os.path.basename(file_path).split('.')[0]
            df_kline['id'] = range(len(df_kline))
            df_kline['freq'] = Freq.D  # 默认为日线，可以后续添加选择

            self.df_kline = df_kline
            self.kline_data = [
                RawBar(
                    symbol=row['symbol'], id=row['id'], dt=row['dt'], freq=row['freq'],
                    open=row['open'], close=row['close'], high=row['high'], low=row['low'],
                    vol=row['vol'], amount=row['amount']
                )
                for _, row in self.df_kline.iterrows()
            ]

            # 更新UI
            self.update_kline_table()
            self.statusBar.showMessage(f"成功加载 {len(self.kline_data)} 条K线数据。")
            self.run_numerical_btn.setEnabled(True)
            self.run_opencv_btn.setEnabled(True)
            self.patterns_text_edit.clear()
            
            print(f"数据加载成功，共 {len(self.kline_data)} 条记录")
            print(f"数据列名: {', '.join(self.df_kline.columns)}")

        except Exception as e:
            import traceback
            error_msg = f"数据加载失败: {str(e)}\n文件路径: {file_path}"
            print(error_msg)
            print(traceback.format_exc())
            QMessageBox.critical(self, "加载错误", f"加载或处理文件时发生错误: {e}")
            self.statusBar.showMessage("文件加载或处理失败。")
            print(f"Error loading data: {e}")
            self.kline_data = []
            self.df_kline = None
            self.run_numerical_btn.setEnabled(False)
            # self.run_opencv_btn.setEnabled(False)

    def run_numerical_detection(self):
        if not self.kline_data:
            QMessageBox.warning(self, "无数据", "请先加载K线数据。")
            return

        self.statusBar.showMessage("正在进行数值N字形态识别...")
        QApplication.processEvents() # 确保UI更新

        try:
            params = {
                'min_segment_len': self.min_segment_len_spin.value(),
                'max_segment_len': self.max_segment_len_spin.value(),
                'min_bc_retracement': self.min_bc_retracement_spin.value(),
                'max_bc_retracement': self.max_bc_retracement_spin.value(),
                'min_cd_extension': self.min_cd_extension_spin.value()
            }
            self.statusBar.showMessage(f"正在进行数值N字形态识别，参数: {params}...")
            QApplication.processEvents() # 确保UI更新
            
            self.identified_patterns = identify_n_patterns_numerical(self.kline_data, **params)
            self.display_patterns()
            self.statusBar.showMessage(f"数值识别完成，找到 {len(self.identified_patterns)} 个N字形态。")
        except Exception as e:
            QMessageBox.critical(self, "识别错误", f"N字形态识别过程中发生错误: {e}")
            self.statusBar.showMessage("N字形态识别失败。")
            print(f"Error during numerical detection: {e}")

    def run_opencv_detection(self):
        if not self.kline_data:
            QMessageBox.warning(self, "无数据", "请先加载K线数据。")
            return

        self.statusBar.showMessage("正在进行OpenCV N字形态识别...")
        QApplication.processEvents() # 确保UI更新

        try:
            params = {
                'min_segment_len': self.min_segment_len_spin.value(),
                'max_segment_len': self.max_segment_len_spin.value(),
                'min_bc_retracement': self.min_bc_retracement_spin.value(),
                'max_bc_retracement': self.max_bc_retracement_spin.value(),
                'min_cd_extension': self.min_cd_extension_spin.value()
            }
            self.statusBar.showMessage(f"正在进行OpenCV N字形态识别，参数: {params}...")
            QApplication.processEvents() # 确保UI更新
            
            self.identified_patterns = identify_n_patterns_opencv(self.kline_data, **params)
            self.display_patterns()
            self.statusBar.showMessage(f"OpenCV识别完成，找到 {len(self.identified_patterns)} 个N字形态。")
        except Exception as e:
            QMessageBox.critical(self, "识别错误", f"OpenCV N字形态识别过程中发生错误: {e}")
            self.statusBar.showMessage("OpenCV N字形态识别失败。")
            print(f"Error during OpenCV detection: {e}")

    def display_patterns(self):
        self.patterns_text_edit.clear()
        if not self.identified_patterns:
            self.patterns_text_edit.append("未识别到任何形态。")
            return

        header = f"共识别到 {len(self.identified_patterns)} 个形态:\n"
        header += "-" * 50 + "\n"
        header += f"{'起始时间':<20} {'结束时间':<20} {'描述'}\n"
        header += "-" * 50 + "\n"
        self.patterns_text_edit.append(header)

        for pattern in self.identified_patterns:
            start_time_str = "日期错误" 
            end_time_str = "日期错误"   
            description_str = "无描述"  

            if isinstance(pattern, NPatternInfo):
                try:
                    start_time_str = pattern.start_dt.strftime('%Y-%m-%d %H:%M') if hasattr(pattern, 'start_dt') and pattern.start_dt else "无效日期"
                    end_time_str = pattern.end_dt.strftime('%Y-%m-%d %H:%M') if hasattr(pattern, 'end_dt') and pattern.end_dt else "无效日期"
                    description_str = pattern.description if hasattr(pattern, 'description') else "N/A"
                except (AttributeError, ValueError) as e:
                    print(f"NPatternInfo 日期/描述格式化错误: {e}, pattern: {pattern}")
                    description_str = pattern.description if hasattr(pattern, 'description') else "描述错误"
            
            elif isinstance(pattern, FivePointPatternInfo):
                try:
                    start_time_str = pattern.l1_dt.strftime('%Y-%m-%d %H:%M') if hasattr(pattern, 'l1_dt') and pattern.l1_dt else "无效日期"
                    end_time_str = pattern.l3_dt.strftime('%Y-%m-%d %H:%M') if hasattr(pattern, 'l3_dt') and pattern.l3_dt else "无效日期"
                    description_str = pattern.description if hasattr(pattern, 'description') else "N/A"
                except (AttributeError, ValueError) as e:
                    print(f"FivePointPatternInfo 日期/描述格式化错误: {e}, pattern: {pattern}")
                    description_str = pattern.description if hasattr(pattern, 'description') else "描述错误"
            else:
                print(f"警告: 在 display_patterns 中遇到未知形态类型 {type(pattern)}")
                if hasattr(pattern, 'description') and pattern.description:
                    description_str = pattern.description
                elif hasattr(pattern, 'name') and pattern.name:
                    description_str = str(pattern.name) 
                else:
                    description_str = f"未知类型: {type(pattern).__name__}"
                start_time_str = "N/A"
                end_time_str = "N/A"
                
            line = f"{start_time_str:<20} {end_time_str:<20} {description_str}\n"
            self.patterns_text_edit.append(line)

    def plot_kline_with_patterns(self):
        if self.figure is None or self.chart_canvas is None:
            self.statusBar.showMessage("图表组件未初始化。")
            return

        self.figure.clear() # Clear previous plots
        if self.df_kline is None or self.df_kline.empty:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "无K线数据或未加载", ha='center', va='center', transform=ax.transAxes)
            self.chart_canvas.draw()
            self.statusBar.showMessage("没有K线数据可供绘图。")
            return

        try:
            gs = self.figure.add_gridspec(4, 1, height_ratios=[3, 1], hspace=0.05)
            ax1 = self.figure.add_subplot(gs[0])  # Main K-line plot
            ax2 = self.figure.add_subplot(gs[1], sharex=ax1) # Volume plot
            
            plt.setp(ax1.get_xticklabels(), visible=False)

            mpf.plot(self.df_kline, type='candle', ax=ax1, volume=ax2, style='yahoo', show_nontrading=False)

            if self.identified_patterns:
                for pattern in self.identified_patterns:
                    pattern_points_dt = [
                        pd.Timestamp(pattern.point_a_dt),
                        pd.Timestamp(pattern.point_b_dt),
                        pd.Timestamp(pattern.point_c_dt),
                        pd.Timestamp(pattern.point_d_dt)
                    ]
                    pattern_points_price = [
                        pattern.point_a_price, pattern.point_b_price,
                        pattern.point_c_price, pattern.point_d_price
                    ]
                    
                    line_color = 'blue' if "上涨N" in pattern.description else 'red'
                    ax1.plot(pattern_points_dt[:2], pattern_points_price[:2], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')
                    ax1.plot(pattern_points_dt[1:3], pattern_points_price[1:3], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')
                    ax1.plot(pattern_points_dt[2:4], pattern_points_price[2:4], marker='o', linestyle='-', markersize=5, color=line_color, alpha=0.7, label='_nolegend_')

                    labels = ['A', 'B', 'C', 'D']
                    for i, label_text in enumerate(labels):
                        ax1.text(pattern_points_dt[i], pattern_points_price[i], label_text,
                                 color='black', fontsize=9, ha='right', va='bottom',
                                 bbox=dict(facecolor='white', alpha=0.5, pad=0.1, edgecolor='none'))
            
            ax1.set_ylabel("价格")
            ax2.set_ylabel("成交量")
            
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            self.figure.autofmt_xdate(rotation=15)

            ax1.set_title(f"K线图与N字形态 ({self.df_kline.index.min().strftime('%Y-%m-%d')} - {self.df_kline.index.max().strftime('%Y-%m-%d')})")
            ax1.grid(True, linestyle='--', alpha=0.6)
            ax2.grid(True, linestyle='--', alpha=0.6)
            
            self.chart_canvas.draw()
            self.statusBar.showMessage("K线图已更新并显示N字形态。")
        except Exception as e:
            print(f"绘图错误: {e}")
            QMessageBox.critical(self, "绘图错误", f"绘制K线图时发生错误: {e}")
            self.statusBar.showMessage("K线图绘制失败。")

    def closeEvent(self, event):
        reply = QMessageBox.question(self, '退出确认', 
                                     "确定要退出吗?", 
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()


def main():
    app = QApplication(sys.argv)
    # 可以设置应用图标
    # app_icon = QIcon("path/to/your/icon.png")
    # app.setWindowIcon(app_icon)
    
    main_win = NShapeDetectorApp()
    main_win.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()


def create_n_pattern_params_group() -> QGroupBox:
    group = QGroupBox("N字形态参数")
    group.setLayout(layout)
    return group

def create_five_point_params_group() -> QGroupBox:
    """创建特定五点形态的参数设置组"""
    group = QGroupBox("特定五点形态参数")
    layout = QFormLayout()

    self.five_point_ma_period_spin = QSpinBox()
    self.five_point_ma_period_spin.setRange(10, 500)
    self.five_point_ma_period_spin.setValue(250)
    self.five_point_ma_period_spin.setToolTip("用于判断回踩的移动平均线周期，例如年线通常是250")
    layout.addRow("MA周期:", self.five_point_ma_period_spin)

    self.five_point_price_prox_spin = QDoubleSpinBox()
    self.five_point_price_prox_spin.setRange(0.01, 0.20)
    self.five_point_price_prox_spin.setSingleStep(0.01)
    self.five_point_price_prox_spin.setValue(0.05)
    self.five_point_price_prox_spin.setToolTip("H1和H2高点价格相似的阈值（百分比）")
    layout.addRow("H1/H2价格接近阈值(%):", self.five_point_price_prox_spin)

    self.five_point_ma_prox_spin = QDoubleSpinBox()
    self.five_point_ma_prox_spin.setRange(0.01, 0.20)
    self.five_point_ma_prox_spin.setSingleStep(0.01)
    self.five_point_ma_prox_spin.setValue(0.05)
    self.five_point_ma_prox_spin.setToolTip("L2和L3低点回踩MA的价格接近阈值（百分比）")
    layout.addRow("L2/L3 MA接近阈值(%):", self.five_point_ma_prox_spin)

    self.five_point_rally_thresh_spin = QDoubleSpinBox()
    self.five_point_rally_thresh_spin.setRange(0.10, 1.00)
    self.five_point_rally_thresh_spin.setSingleStep(0.05)
    self.five_point_rally_thresh_spin.setValue(0.30)
    self.five_point_rally_thresh_spin.setToolTip("L1点后H1高点超过L1处MA的最小幅度（百分比）")
    layout.addRow("L1后拉升超MA幅度(%):", self.five_point_rally_thresh_spin)

    self.five_point_l1_low_spin = QDoubleSpinBox()
    self.five_point_l1_low_spin.setRange(0.01, 0.20)
    self.five_point_l1_low_spin.setSingleStep(0.01)
    self.five_point_l1_low_spin.setValue(0.05)
    self.five_point_l1_low_spin.setToolTip("L1低点相对于L2, L3低点的最小差异幅度（百分比）")
    layout.addRow("L1相对L2/L3低幅(%):", self.five_point_l1_low_spin)

    self.five_point_extrema_window_spin = QSpinBox()
    self.five_point_extrema_window_spin.setRange(1, 10)
    self.five_point_extrema_window_spin.setValue(3)
    self.five_point_extrema_window_spin.setToolTip("判断局部极值点时考虑的K线窗口大小（单边）")
    layout.addRow("局部极值窗口:", self.five_point_extrema_window_spin)
    
    group.setLayout(layout)
    return group

def plot_five_point_patterns(self, ax, df, patterns):
    """在图表上绘制识别出的特定五点形态"""
    if not patterns:
        return
    
    print(f"开始绘制 {len(patterns)} 个特定五点形态...")

    for i, p in enumerate(patterns):
        # 将datetime转换为matplotlib可识别的日期格式
        l1_dt_num = mdates.date2num(p.l1_dt)
        h1_dt_num = mdates.date2num(p.h1_dt)
        l2_dt_num = mdates.date2num(p.l2_dt)
        h2_dt_num = mdates.date2num(p.h2_dt)
        l3_dt_num = mdates.date2num(p.l3_dt)

        # 检查日期是否在当前显示的DataFrame的索引中
        # df.index 通常是 DatetimeIndex
        # 我们需要找到最接近这些dt的索引位置来获取正确的x轴坐标
        # 或者，如果df的索引就是matplotlib的数字日期，可以直接使用
        # 假设df的索引是DatetimeIndex
        try:
            idx_l1 = df.index.get_loc(p.l1_dt, method='nearest')
            idx_h1 = df.index.get_loc(p.h1_dt, method='nearest')
            idx_l2 = df.index.get_loc(p.l2_dt, method='nearest')
            idx_h2 = df.index.get_loc(p.h2_dt, method='nearest')
            idx_l3 = df.index.get_loc(p.l3_dt, method='nearest')
            
            # 使用df的索引（已经是matplotlib的数字日期）或者转换后的索引
            # 如果df.index已经是数字，可以直接用，否则用转换后的
            # 为简化，我们假设df的索引可以直接用于绘图，或者mpf.plot内部处理了转换
            # 这里我们用转换后的matplotlib数字日期
            x_coords = [l1_dt_num, h1_dt_num, l2_dt_num, h2_dt_num, l3_dt_num]
            y_coords = [p.l1_price, p.h1_price, p.l2_price, p.h2_price, p.l3_price]

            ax.plot(x_coords, y_coords, marker='o', linestyle='-', color='purple', markersize=8, linewidth=2, label=f'5-Point {i+1}')
            
            # 标注点位
            ax.text(l1_dt_num, p.l1_price, 'L1', color='purple', fontsize=10, ha='center', va='bottom')
            ax.text(h1_dt_num, p.h1_price, 'H1', color='purple', fontsize=10, ha='center', va='bottom')
            ax.text(l2_dt_num, p.l2_price, 'L2', color='purple', fontsize=10, ha='center', va='bottom')
            ax.text(h2_dt_num, p.h2_price, 'H2', color='purple', fontsize=10, ha='center', va='bottom')
            ax.text(l3_dt_num, p.l3_price, 'L3', color='purple', fontsize=10, ha='center', va='bottom')
            print(f"绘制形态 {i+1}: L1({p.l1_dt}), H1({p.h1_dt}), L2({p.l2_dt}), H2({p.h2_dt}), L3({p.l3_dt})")

        except KeyError as e:
            print(f"绘制五点形态时出错：日期 {e} 未在当前K线数据中找到。跳过此形态。")
        except Exception as e:
            print(f"绘制五点形态时发生未知错误: {e}")

    if patterns: # 添加图例，如果绘制了任何形态
        handles, labels = ax.get_legend_handles_labels()
        # 避免重复图例项
        by_label = dict(zip(labels, handles))
        ax.legend(by_label.values(), by_label.keys())

def update_results_text(self, patterns: List[Any], pattern_type: str = "Pattern"):
    """更新结果文本框的内容"""
    self.patterns_text_edit.clear()
    if not self.identified_patterns:
        self.patterns_text_edit.append("未识别到N字形态。")
        return

    header = f"共识别到 {len(self.identified_patterns)} 个N字形态:\n"
    header += "-" * 50 + "\n"
    header += f"{'起始时间':<20} {'结束时间':<20} {'描述'}\n"
    header += "-" * 50 + "\n"
    self.patterns_text_edit.append(header)

    for pattern in self.identified_patterns:
        # 确保日期是有效的，并使用安全的格式化方法
        try:
            start_time_str = pattern.start_dt.strftime('%Y-%m-%d %H:%M') if hasattr(pattern, 'start_dt') and pattern.start_dt else "无效日期"
        except Exception as e:
            start_time_str = "无效日期 (Error)"
            print(f"Error formatting start_dt for pattern. Description: {getattr(pattern, 'description', 'N/A')}. Error: {e}")