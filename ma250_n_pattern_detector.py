#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于250日均线的N字形态检测器
专门识别与250日均线相关的N字形态
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from typing import List, Dict, Tuple

class MA250NPatternDetector:
    """基于250日均线的N字形态检测器"""
    
    def __init__(self):
        pass
    
    def load_csv_data(self, csv_path: str) -> Tuple[np.array, pd.DataFrame]:
        """加载CSV数据并返回价格和完整数据框"""
        try:
            df = pd.read_csv(csv_path)
            
            # 尝试找到价格列
            price_columns = ['close', 'Close', '收盘价', '收盘', 'high', 'High', '最高价', 
                           'low', 'Low', '最低价', 'price', 'Price', '价格']
            
            price_col = None
            for col in price_columns:
                if col in df.columns:
                    price_col = col
                    break
            
            if price_col is None:
                # 使用第一个数值列
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    price_col = numeric_cols[0]
                else:
                    raise ValueError("未找到价格数据列")
            
            prices = df[price_col].values
            prices = prices[~np.isnan(prices)]  # 移除NaN
            
            print(f"加载CSV数据: {len(prices)} 个价格点，使用列: {price_col}")
            return prices, df
            
        except Exception as e:
            print(f"加载CSV失败 {csv_path}: {e}")
            return None, None
    
    def calculate_ma250(self, prices: np.array) -> np.array:
        """计算250日移动平均线"""
        if len(prices) < 250:
            print(f"数据长度 {len(prices)} 不足250天，无法计算250日均线")
            return None
        
        ma250 = np.full(len(prices), np.nan)
        
        # 从第250个数据点开始计算
        for i in range(249, len(prices)):
            ma250[i] = np.mean(prices[i-249:i+1])
        
        print(f"计算250日均线完成")
        return ma250
    
    def find_ma250_crossovers(self, prices: np.array, ma250: np.array) -> Dict:
        """找到价格与250日均线的交叉点"""
        crossovers = {
            'upward': [],   # 向上穿越
            'downward': []  # 向下穿越
        }
        
        # 从第250天开始检查交叉
        for i in range(250, len(prices) - 1):
            if not (np.isnan(ma250[i]) or np.isnan(ma250[i+1])):
                # 检查向上穿越
                if prices[i] <= ma250[i] and prices[i+1] > ma250[i+1]:
                    crossovers['upward'].append(i+1)
                
                # 检查向下穿越
                elif prices[i] >= ma250[i] and prices[i+1] < ma250[i+1]:
                    crossovers['downward'].append(i+1)
        
        print(f"找到向上穿越 {len(crossovers['upward'])} 次，向下穿越 {len(crossovers['downward'])} 次")
        return crossovers
    
    def find_ma250_support_points(self, prices: np.array, ma250: np.array, tolerance: float = 0.02) -> List[int]:
        """找到在250日均线附近的支撑点（低点）"""
        support_points = []
        
        # 寻找局部最小值
        troughs, _ = signal.find_peaks(-prices, prominence=np.std(prices) * 0.5, distance=10)
        
        for trough in troughs:
            if trough >= 250 and not np.isnan(ma250[trough]):
                # 检查是否在250日均线附近
                distance_to_ma = abs(prices[trough] - ma250[trough]) / ma250[trough]
                
                if distance_to_ma <= tolerance:  # 在2%范围内
                    support_points.append(trough)
        
        print(f"找到 {len(support_points)} 个250日均线附近的支撑点")
        return support_points
    
    def detect_ma250_n_patterns(self, prices: np.array, ma250: np.array) -> List[Dict]:
        """检测与250日均线相关的N字形态"""
        n_patterns = []
        
        # 找到关键点
        crossovers = self.find_ma250_crossovers(prices, ma250)
        support_points = self.find_ma250_support_points(prices, ma250)
        
        print(f"\n开始分析N字形态...")
        
        # 寻找N字形态：两个支撑点 + 中间的穿越
        for i, support1 in enumerate(support_points[:-1]):
            for support2 in support_points[i+1:]:
                # 检查两个支撑点之间的距离
                if support2 - support1 > 20 and support2 - support1 < 200:  # 20-200天之间
                    
                    # 寻找两个支撑点之间的向上穿越
                    upward_crosses = [cross for cross in crossovers['upward'] 
                                    if support1 < cross < support2]
                    
                    if upward_crosses:
                        # 找到最高点
                        segment_prices = prices[support1:support2+1]
                        peak_idx = np.argmax(segment_prices) + support1
                        
                        # 检查N字形态的质量
                        pattern_score = self._evaluate_n_pattern_quality(
                            prices, ma250, support1, peak_idx, support2, upward_crosses[0]
                        )
                        
                        if pattern_score > 0.5:
                            pattern = {
                                'start_index': support1,
                                'end_index': support2,
                                'peak_index': peak_idx,
                                'crossover_index': upward_crosses[0],
                                'support1_price': prices[support1],
                                'support2_price': prices[support2],
                                'peak_price': prices[peak_idx],
                                'ma250_at_support1': ma250[support1],
                                'ma250_at_support2': ma250[support2],
                                'pattern_score': pattern_score,
                                'duration': support2 - support1
                            }
                            n_patterns.append(pattern)
        
        # 按得分排序
        n_patterns.sort(key=lambda x: x['pattern_score'], reverse=True)
        
        print(f"找到 {len(n_patterns)} 个与250日均线相关的N字形态")
        return n_patterns
    
    def _evaluate_n_pattern_quality(self, prices: np.array, ma250: np.array, 
                                   support1: int, peak: int, support2: int, crossover: int) -> float:
        """评估N字形态的质量"""
        score = 0
        
        # 1. 检查两个支撑点是否都在250日均线附近
        support1_distance = abs(prices[support1] - ma250[support1]) / ma250[support1]
        support2_distance = abs(prices[support2] - ma250[support2]) / ma250[support2]
        
        if support1_distance <= 0.03 and support2_distance <= 0.03:  # 3%以内
            score += 0.3
        
        # 2. 检查峰值是否明显高于250日均线
        peak_above_ma = (prices[peak] - ma250[peak]) / ma250[peak]
        if peak_above_ma > 0.1:  # 高于10%
            score += 0.3
        
        # 3. 检查穿越点是否在合理位置
        if support1 < crossover < peak:
            score += 0.2
        
        # 4. 检查形态的对称性
        left_duration = peak - support1
        right_duration = support2 - peak
        symmetry = 1 - abs(left_duration - right_duration) / max(left_duration, right_duration)
        score += symmetry * 0.2
        
        return score
    
    def visualize_ma250_patterns(self, csv_path: str, prices: np.array, ma250: np.array, 
                                n_patterns: List[Dict], save_plot: bool = False):
        """可视化250日均线N字形态"""
        if not n_patterns:
            print("没有找到N字形态可视化")
            return
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 绘制价格线
        ax.plot(prices, 'b-', linewidth=1, alpha=0.8, label='Price')
        
        # 绘制250日均线
        valid_ma = ~np.isnan(ma250)
        ax.plot(np.where(valid_ma)[0], ma250[valid_ma], 'orange', linewidth=2, alpha=0.8, label='MA250')
        
        ax.set_title(f'MA250 N-Pattern Detection: {os.path.basename(csv_path)}')
        ax.set_ylabel('Price')
        ax.set_xlabel('Time (Days)')
        ax.grid(True, alpha=0.3)
        
        # 标记N字形态
        colors = ['red', 'green', 'purple', 'brown', 'pink']
        for i, pattern in enumerate(n_patterns[:5]):  # 最多显示5个
            color = colors[i % len(colors)]
            
            # 绘制N字形态的连线
            n_indices = [pattern['start_index'], pattern['peak_index'], pattern['end_index']]
            n_prices = [pattern['support1_price'], pattern['peak_price'], pattern['support2_price']]
            
            ax.plot(n_indices, n_prices, color=color, linewidth=3, alpha=0.8, marker='o', markersize=8,
                   label=f'N-Pattern {i+1} (score: {pattern["pattern_score"]:.2f})')
            
            # 标记关键点
            ax.scatter(pattern['start_index'], pattern['support1_price'], 
                      color=color, s=100, marker='v', alpha=0.8)
            ax.scatter(pattern['peak_index'], pattern['peak_price'], 
                      color=color, s=100, marker='^', alpha=0.8)
            ax.scatter(pattern['end_index'], pattern['support2_price'], 
                      color=color, s=100, marker='v', alpha=0.8)
            ax.scatter(pattern['crossover_index'], prices[pattern['crossover_index']], 
                      color=color, s=80, marker='x', alpha=0.8)
            
            # 添加文本标注
            ax.annotate(f'Support1', (pattern['start_index'], pattern['support1_price']), 
                       xytext=(5, 5), textcoords='offset points', fontsize=8, color=color)
            ax.annotate(f'Peak', (pattern['peak_index'], pattern['peak_price']), 
                       xytext=(5, 5), textcoords='offset points', fontsize=8, color=color)
            ax.annotate(f'Support2', (pattern['end_index'], pattern['support2_price']), 
                       xytext=(5, 5), textcoords='offset points', fontsize=8, color=color)
        
        ax.legend()
        plt.tight_layout()
        
        if save_plot:
            plot_name = f"{os.path.splitext(os.path.basename(csv_path))[0]}_ma250_n_patterns.png"
            plt.savefig(plot_name, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {plot_name}")
        
        plt.show()
    
    def analyze_csv(self, csv_path: str):
        """分析CSV文件"""
        print(f"\n=== 基于250日均线的N字形态分析: {os.path.basename(csv_path)} ===")
        
        # 加载数据
        prices, df = self.load_csv_data(csv_path)
        if prices is None:
            return []
        
        # 计算250日均线
        ma250 = self.calculate_ma250(prices)
        if ma250 is None:
            return []
        
        # 检测N字形态
        n_patterns = self.detect_ma250_n_patterns(prices, ma250)
        
        if n_patterns:
            print(f"\n找到 {len(n_patterns)} 个与250日均线相关的N字形态:")
            for i, pattern in enumerate(n_patterns):
                print(f"\nN字形态 {i+1}:")
                print(f"  时间跨度: {pattern['start_index']} - {pattern['end_index']} ({pattern['duration']} 天)")
                print(f"  支撑点1: 价格 {pattern['support1_price']:.2f}, MA250 {pattern['ma250_at_support1']:.2f}")
                print(f"  峰值点: 价格 {pattern['peak_price']:.2f}")
                print(f"  支撑点2: 价格 {pattern['support2_price']:.2f}, MA250 {pattern['ma250_at_support2']:.2f}")
                print(f"  穿越点: 第 {pattern['crossover_index']} 天")
                print(f"  形态得分: {pattern['pattern_score']:.3f}")
            
            # 可视化
            self.visualize_ma250_patterns(csv_path, prices, ma250, n_patterns, save_plot=True)
        else:
            print("未找到与250日均线相关的N字形态")
        
        return n_patterns

def main():
    """主函数"""
    print("=== 基于250日均线的N字形态检测器 ===\n")
    print("专门识别以下特征的N字形态:")
    print("1. 两个低点都在250日均线附近")
    print("2. 中间有向上穿越250日均线的过程")
    print("3. 形成明显的N字走势")
    
    # 创建检测器
    detector = MA250NPatternDetector()
    
    # 输入文件路径
    csv_path = input("\n请输入CSV文件路径: ").strip()
    if not os.path.exists(csv_path):
        print("文件不存在!")
        return
    
    # 分析文件
    n_patterns = detector.analyze_csv(csv_path)
    
    if n_patterns:
        print(f"\n检测完成! 找到 {len(n_patterns)} 个符合条件的N字形态")
        print("图表已显示并保存，橙色线为250日均线")
    else:
        print("\n未找到符合条件的N字形态")
        print("可能原因:")
        print("1. 数据长度不足250天")
        print("2. 没有明显的250日均线支撑")
        print("3. 缺少向上穿越250日均线的过程")

if __name__ == "__main__":
    main()
