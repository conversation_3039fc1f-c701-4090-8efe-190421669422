#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片文件诊断工具
检查图片文件的完整性和可读性
"""

import os
import glob
from PIL import Image
import cv2
import numpy as np

def diagnose_image_directory(directory: str):
    """诊断目录中的图片文件"""
    print(f"诊断目录: {directory}")
    
    if not os.path.exists(directory):
        print(f"目录不存在: {directory}")
        return
    
    # 收集所有图片文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']
    all_images = []
    
    for ext in image_extensions:
        all_images.extend(glob.glob(os.path.join(directory, ext)))
        all_images.extend(glob.glob(os.path.join(directory, ext.upper())))
    
    print(f"找到 {len(all_images)} 个图片文件")
    
    # 统计信息
    readable_count = 0
    pil_readable = 0
    cv2_readable = 0
    corrupted_files = []
    empty_files = []
    
    for i, img_path in enumerate(all_images):
        print(f"检查 {i+1}/{len(all_images)}: {os.path.basename(img_path)}")
        
        # 检查文件大小
        file_size = os.path.getsize(img_path)
        if file_size == 0:
            empty_files.append(img_path)
            print(f"  ❌ 文件为空")
            continue
        
        # 尝试用PIL读取
        pil_success = False
        try:
            with Image.open(img_path) as img:
                img.verify()
            pil_success = True
            pil_readable += 1
            print(f"  ✅ PIL可读")
        except Exception as e:
            print(f"  ❌ PIL失败: {str(e)[:50]}")
        
        # 尝试用OpenCV读取
        cv2_success = False
        try:
            img = cv2.imread(img_path)
            if img is not None:
                cv2_success = True
                cv2_readable += 1
                print(f"  ✅ OpenCV可读")
            else:
                # 尝试用不同编码
                img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                if img is not None:
                    cv2_success = True
                    cv2_readable += 1
                    print(f"  ✅ OpenCV可读(特殊编码)")
                else:
                    print(f"  ❌ OpenCV失败")
        except Exception as e:
            print(f"  ❌ OpenCV异常: {str(e)[:50]}")
        
        if pil_success or cv2_success:
            readable_count += 1
        else:
            corrupted_files.append(img_path)
    
    # 生成报告
    print(f"\n=== 诊断报告 ===")
    print(f"总文件数: {len(all_images)}")
    print(f"可读文件数: {readable_count}")
    print(f"PIL可读: {pil_readable}")
    print(f"OpenCV可读: {cv2_readable}")
    print(f"损坏文件数: {len(corrupted_files)}")
    print(f"空文件数: {len(empty_files)}")
    
    if corrupted_files:
        print(f"\n损坏的文件:")
        for file in corrupted_files[:10]:  # 只显示前10个
            print(f"  {os.path.basename(file)}")
        if len(corrupted_files) > 10:
            print(f"  ... 还有 {len(corrupted_files) - 10} 个")
    
    if empty_files:
        print(f"\n空文件:")
        for file in empty_files:
            print(f"  {os.path.basename(file)}")
    
    # 建议
    print(f"\n=== 建议 ===")
    if readable_count > 10:
        print(f"✅ 有 {readable_count} 个可读文件，足够训练模型")
    elif readable_count > 0:
        print(f"⚠️  只有 {readable_count} 个可读文件，建议增加更多图片")
    else:
        print(f"❌ 没有可读的图片文件")
    
    if len(corrupted_files) > 0:
        print(f"⚠️  有 {len(corrupted_files)} 个损坏文件，建议删除或修复")
    
    return {
        'total': len(all_images),
        'readable': readable_count,
        'pil_readable': pil_readable,
        'cv2_readable': cv2_readable,
        'corrupted': corrupted_files,
        'empty': empty_files
    }

def clean_directory(directory: str, remove_corrupted: bool = False):
    """清理目录中的问题文件"""
    print(f"\n=== 清理目录 ===")
    
    result = diagnose_image_directory(directory)
    
    if remove_corrupted and result['corrupted']:
        print(f"\n是否删除 {len(result['corrupted'])} 个损坏的文件？")
        choice = input("输入 'yes' 确认删除: ").strip().lower()
        
        if choice == 'yes':
            for file_path in result['corrupted']:
                try:
                    os.remove(file_path)
                    print(f"已删除: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"删除失败 {os.path.basename(file_path)}: {e}")
            print(f"清理完成")
        else:
            print("取消删除")

def main():
    """主函数"""
    print("=== 图片文件诊断工具 ===\n")
    
    # 诊断用户的图片目录
    user_dir = "D:\\双回撤"
    
    result = diagnose_image_directory(user_dir)
    
    if result['corrupted']:
        print(f"\n发现 {len(result['corrupted'])} 个损坏文件")
        choice = input("是否删除损坏的文件？(y/n): ").strip().lower()
        
        if choice == 'y':
            clean_directory(user_dir, remove_corrupted=True)

if __name__ == "__main__":
    main()
