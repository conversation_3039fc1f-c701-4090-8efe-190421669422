import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from mplfinance.original_flavor import candlestick_ohlc
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QFileDialog,
    QLineEdit, QLabel, QListWidget, QInputDialog
)
from PyQt6.QtCore import Qt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas


class KLineApp(QWidget):
    def __init__(self):
        super().__init__()
        self.csv_paths = []
        self.all_marks = []
        self.model = None
        self.initUI()

    def initUI(self):
        # 布局
        main_layout = QVBoxLayout()

        # 文件选择部分
        file_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        file_layout.addWidget(QLabel("选择 CSV 或 TXT 文件:"))
        file_layout.addWidget(self.file_input)
        select_button = QPushButton("选择文件")
        select_button.clicked.connect(self.select_file)
        file_layout.addWidget(select_button)
        main_layout.addLayout(file_layout)

        # 标注列表
        self.mark_list = QListWidget()
        main_layout.addWidget(QLabel("已标注文件列表:"))
        main_layout.addWidget(self.mark_list)

        # 标注按钮
        mark_button = QPushButton("标注点")
        mark_button.clicked.connect(self.mark_points)
        main_layout.addWidget(mark_button)

        # 生成 K 线图按钮
        plot_button = QPushButton("生成 K 线图")
        plot_button.clicked.connect(self.plot_candlestick)
        main_layout.addWidget(plot_button)

        # 构建模型按钮
        build_model_button = QPushButton("构建形态模型")
        build_model_button.clicked.connect(self.build_model)
        main_layout.addWidget(build_model_button)

        # 识别相似形态按钮
        recognize_button = QPushButton("识别相似形态")
        recognize_button.clicked.connect(self.recognize_pattern)
        main_layout.addWidget(recognize_button)

        # 图表显示部分
        self.figure = plt.figure()
        self.canvas = FigureCanvas(self.figure)
        main_layout.addWidget(self.canvas)

        self.setLayout(main_layout)
        self.setWindowTitle('K 线图形态识别')
        self.setGeometry(100, 100, 1200, 800)
        self.show()

    def select_file(self):
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if file_path:
            self.file_input.setText(file_path)

    def read_file(self, file_path):
        if file_path.endswith('.csv'):
            return pd.read_csv(file_path)
        elif file_path.endswith('.txt'):
            return pd.read_csv(file_path, sep=',')
        return None

    def mark_points(self):
        file_path = self.file_input.text()
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        marks = []
        for i in range(5):
            mark_type = "低点" if i % 2 == 0 else "高点"
            idx, ok = QInputDialog.getInt(self, f"标注第 {i + 1} 个点", f"请输入第 {i + 1} 个 {mark_type} 的索引:")
            if ok:
                marks.append(idx)
            else:
                return

        self.csv_paths.append(file_path)
        self.all_marks.append(marks)
        self.mark_list.addItem(os.path.basename(file_path))

    def plot_candlestick(self):
        if not self.csv_paths or not self.all_marks:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        for i, (csv_path, marks) in enumerate(zip(self.csv_paths, self.all_marks)):
            df = self.read_file(csv_path)
            if df is None:
                continue

            df['Date'] = pd.to_datetime(df['Date'])
            df['Date'] = df['Date'].map(mdates.date2num)
            ohlc = df[['Date', 'Open', 'High', 'Low', 'Close']]

            candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')

            for j, idx in enumerate(marks):
                x = df['Date'][idx]
                y = df['Low'][idx] if j % 2 == 0 else df['High'][idx]
                ax.annotate(f'{j + 1}', xy=(x, y), xytext=(x, y + 0.05),
                            arrowprops=dict(facecolor='black', shrink=0.05))

            start_idx = marks[0]
            end_idx = marks[4]
            ax.set_xlim(df['Date'][start_idx], df['Date'][end_idx])

        self.canvas.draw()

    def build_model(self):
        if not self.csv_paths or not self.all_marks:
            return

        X = []
        y = []
        for i, (csv_path, marks) in enumerate(zip(self.csv_paths, self.all_marks)):
            df = self.read_file(csv_path)
            if df is None:
                continue

            start_idx = marks[0]
            end_idx = marks[4]
            pattern = df['Close'][start_idx:end_idx + 1].values
            X.append(pattern)
            y.append(i)

        max_length = max(len(p) for p in X)
        X_padded = []
        for pattern in X:
            padded = np.pad(pattern, (0, max_length - len(pattern)), 'constant')
            X_padded.append(padded)

        X = np.array(X_padded)
        self.model = KNeighborsClassifier(n_neighbors=1)
        self.model.fit(X, y)

    def recognize_pattern(self):
        if not self.model:
            return

        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "选择要识别的 CSV 或 TXT 文件", "", "CSV Files (*.csv);;TXT Files (*.txt)")
        if not file_path or not os.path.exists(file_path):
            return

        df = self.read_file(file_path)
        if df is None:
            return

        max_length = self.model.n_features_in_
        similarities = []
        for i in range(len(df) - max_length + 1):
            pattern = df['Close'][i:i + max_length].values
            pattern = np.pad(pattern, (0, max_length - len(pattern)), 'constant')
            similarity = self.model.predict_proba([pattern])
            similarities.append(similarity.max())
        best_idx = np.argmax(similarities)

        print(f"相似形态起始索引: {best_idx}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = KLineApp()
    sys.exit(app.exec())
