#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
N字形态识别 - OpenCV版本

该脚本用于读取CSV或TXT格式的K线数据，生成日K线和周K线，
并使用OpenCV从K线图（由这些数据生成）中识别N字形态。
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
import math
import time
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import List, Dict, Any, Callable
from collections import OrderedDict
from enum import Enum
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import mplfinance as mpf
import cv2

# 设置中文字体 - 简单直接的方式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False

# 定义必要的枚举类型 (参考 simple_mat_fx_bi_analyzer.py)
class Mark(Enum):
    D = "底分型"
    G = "顶分型"

class Direction(Enum):
    Up = "向上"
    Down = "向下"

class Freq(Enum):
    F1 = "1分钟"
    F5 = "5分钟"
    F15 = "15分钟"
    F30 = "30分钟"
    F60 = "60分钟"
    D = "日线"
    W = "周线"
    M = "月线"
    S = "季线"
    Y = "年线"

    @classmethod
    def _missing_(cls, value):
        if isinstance(value, str):
            for freq in cls:
                if freq.value == value:
                    return freq
        return None

# 定义必要的数据类 (参考 simple_mat_fx_bi_analyzer.py)
@dataclass
class RawBar:
    """原始K线元素"""
    symbol: str
    id: int  # id 必须是升序
    dt: datetime
    freq: Freq
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float
    cache: dict = field(default_factory=dict)  # cache 用户缓存

# K线数据读取函数 (参考 simple_mat_fx_bi_analyzer.py)
def read_kline_data(file_path: str, symbol: str = "symbol", freq: Freq = Freq.D) -> List[RawBar]:
    """读取K线数据，支持CSV和TXT格式"""
    bars = []
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, encoding='utf-8')
        elif file_path.endswith('.txt'):
            df = pd.read_csv(file_path, encoding='utf-8', sep='\t') # 假设TXT文件是制表符分隔
        else:
            print(f"错误：不支持的文件格式 {file_path}")
            return bars

        # 常见列名映射，根据实际情况调整
        column_map = {
            'trade_date': 'dt', '日期': 'dt', 'date': 'dt', 'time': 'dt',
            'open': 'open', '开盘': 'open',
            'close': 'close', '收盘': 'close',
            'high': 'high', '最高': 'high',
            'low': 'low', '最低': 'low',
            'vol': 'vol', 'volume': 'vol', '成交量': 'vol',
            'amount': 'amount', '成交额': 'amount'
        }
        df.rename(columns=lambda x: column_map.get(x.lower(), x.lower()), inplace=True)

        required_cols = ['dt', 'open', 'high', 'low', 'close', 'vol']
        for col in required_cols:
            if col not in df.columns:
                print(f"错误：CSV/TXT文件缺少必需列: {col}")
                return bars

        # 尝试将 'dt' 列转换为 datetime 对象
        try:
            df['dt'] = pd.to_datetime(df['dt'])
        except Exception as e:
            print(f"错误：无法将日期时间列转换为datetime对象: {e}")
            return bars

        df = df.sort_values('dt')
        df.reset_index(drop=True, inplace=True)

        for i, row in df.iterrows():
            bars.append(RawBar(
                symbol=symbol,
                id=i,
                dt=row['dt'],
                freq=freq,
                open=float(row['open']),
                close=float(row['close']),
                high=float(row['high']),
                low=float(row['low']),
                vol=float(row['vol']),
                amount=float(row.get('amount', 0)) # amount列可能不存在
            ))
        print(f"成功读取 {len(bars)} 条K线数据从 {file_path}")
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {e}")
    return bars

# K线聚合函数
def aggregate_to_daily(raw_bars: List[RawBar]) -> List[RawBar]:
    """将任意周期的K线数据聚合成日K线"""
    if not raw_bars:
        return []

    df = pd.DataFrame([bar.__dict__ for bar in raw_bars])
    df['dt'] = pd.to_datetime(df['dt'])
    df.set_index('dt', inplace=True)

    # 按天聚合
    daily_df = df.resample('D').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'vol': 'sum',
        'amount': 'sum',
        'symbol': 'first', # 假设同一天内symbol不变
    })
    daily_df.dropna(subset=['open'], inplace=True) # 去除没有交易数据的日期
    daily_df.reset_index(inplace=True)

    daily_bars = []
    for i, row in daily_df.iterrows():
        daily_bars.append(RawBar(
            symbol=row['symbol'] if pd.notna(row['symbol']) else raw_bars[0].symbol,
            id=i,
            dt=row['dt'],
            freq=Freq.D,
            open=float(row['open']),
            close=float(row['close']),
            high=float(row['high']),
            low=float(row['low']),
            vol=float(row['vol']),
            amount=float(row['amount'])
        ))
    print(f"聚合成 {len(daily_bars)} 条日K线数据")
    return daily_bars

def aggregate_to_weekly(raw_bars: List[RawBar]) -> List[RawBar]:
    """将任意周期的K线数据聚合成周K线"""
    if not raw_bars:
        return []

    df = pd.DataFrame([bar.__dict__ for bar in raw_bars])
    df['dt'] = pd.to_datetime(df['dt'])
    df.set_index('dt', inplace=True)

    # 按周聚合 (每周一为开始)
    weekly_df = df.resample('W-MON').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'vol': 'sum',
        'amount': 'sum',
        'symbol': 'first',
    })
    weekly_df.dropna(subset=['open'], inplace=True)
    weekly_df.reset_index(inplace=True)

    weekly_bars = []
    for i, row in weekly_df.iterrows():
        weekly_bars.append(RawBar(
            symbol=row['symbol'] if pd.notna(row['symbol']) else raw_bars[0].symbol,
            id=i,
            dt=row['dt'], # 周K线的dt通常是该周的开始或结束日期
            freq=Freq.W,
            open=float(row['open']),
            close=float(row['close']),
            high=float(row['high']),
            low=float(row['low']),
            vol=float(row['vol']),
            amount=float(row['amount'])
        ))
    print(f"聚合成 {len(weekly_bars)} 条周K线数据")
    return weekly_bars

# OpenCV N字形态识别函数 (占位符)
def find_n_shape_cv(bars: List[RawBar], output_image_path: str = None):
    """
    使用OpenCV识别K线图中的N字形态
    :param bars: K线数据列表 (通常是日K或周K)
    :param output_image_path: 如果提供，则保存带有标记的K线图
    :return: 识别到的N字形态列表，每个元素包含起始和结束点等信息
    """
    print(f"开始使用OpenCV识别N字形态，共 {len(bars)} 条K线数据")
    if len(bars) < 10: # 至少需要一些K线才能形成有意义的形态
        print("K线数据过少，无法进行N字形态识别")
        return []

    # 1. 将K线数据转换为图像 (使用mplfinance或直接绘制)
    df = pd.DataFrame([bar.__dict__ for bar in bars])
    df['dt'] = pd.to_datetime(df['dt'])
    df.set_index('dt', inplace=True)
    df.rename(columns={'vol': 'volume'}, inplace=True) # mplfinance需要volume列名

    # 创建一个matplotlib图像，但不显示，直接获取图像数据
    fig, ax = plt.subplots(figsize=(10, 6)) # 调整图像大小以获得更好的分辨率
    mpf.plot(df, type='candle', ax=ax, style='yahoo', volume=False) # 暂时不画成交量
    ax.axis('off') # 关闭坐标轴，只保留K线
    fig.tight_layout(pad=0)
    fig.canvas.draw()

    # 将matplotlib图像转换为OpenCV图像格式
    img_data = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
    img_data = img_data.reshape(fig.canvas.get_width_height()[::-1] + (3,))
    img_bgr = cv2.cvtColor(img_data, cv2.COLOR_RGB2BGR)
    plt.close(fig) # 关闭图像，释放资源

    if output_image_path:
        cv2.imwrite(f"{output_image_path}_raw_kline.png", img_bgr)
        print(f"原始K线图已保存至 {output_image_path}_raw_kline.png")

    # 2. 图像预处理
    gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
    # 使用高斯模糊减少噪声，帮助边缘检测
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # 使用Canny进行边缘检测
    edges = cv2.Canny(blurred, 50, 150) # 参数可能需要调整

    if output_image_path:
        cv2.imwrite(f"{output_image_path}_edges.png", edges)
        print(f"边缘检测图像已保存至 {output_image_path}_edges.png")

    # 3. 轮廓检测
    # cv2.RETR_EXTERNAL 只检测最外层轮廓
    # cv2.CHAIN_APPROX_SIMPLE 压缩水平、垂直和对角线段，只留下它们的端点
    contours, hierarchy = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    img_contours = img_bgr.copy()
    cv2.drawContours(img_contours, contours, -1, (0, 255, 0), 1) # 用绿色绘制所有轮廓
    if output_image_path:
        cv2.imwrite(f"{output_image_path}_contours.png", img_contours)
        print(f"轮廓图像已保存至 {output_image_path}_contours.png")

    # 4. N字形态特征提取与匹配 (核心难点 - 初步简化实现)
    # 这是一个非常复杂的任务，以下仅为概念性演示，实际效果可能不佳
    # 真正的N字识别需要更高级的图像分析、模式识别技术或机器学习
    identified_n_shapes = []

    # 尝试基于轮廓的几何特征进行分析
    # 例如，我们可以尝试找到三个连续的、特定方向和相对位置的显著轮廓段
    # 但这在K线图中非常具有挑战性，因为K线本身形态多样

    # 简化思路：检测是否存在近似 'N' 形的轮廓组合
    # 筛选出足够大的轮廓
    min_contour_area = 100 # 最小轮廓面积，需要根据图像大小和K线尺寸调整
    significant_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area]

    # 对轮廓按x坐标排序，以便分析其左右关系
    if significant_contours:
        # 获取轮廓的包围框，并按x坐标排序
        bounding_boxes = [cv2.boundingRect(c) for c in significant_contours]
        # 将轮廓和其包围框一起排序
        sorted_contours_with_boxes = sorted(zip(significant_contours, bounding_boxes), key=lambda b: b[1][0])
        
        # 简化N字检测：查找三个在水平方向上大致连续，且高度有起伏的轮廓
        # (这远非精确的N字检测，仅为示例)
        for i in range(len(sorted_contours_with_boxes) - 2):
            c1, (x1, y1, w1, h1) = sorted_contours_with_boxes[i]
            c2, (x2, y2, w2, h2) = sorted_contours_with_boxes[i+1]
            c3, (x3, y3, w3, h3) = sorted_contours_with_boxes[i+2]

            # 简化的条件：
            # 1. 水平上大致连续 (x坐标递增，且不要离太远)
            # 2. 高度模式：中 < 左，中 < 右 (近似V型底部)
            #    或者： 中 > 左，中 > 右 (近似倒V型顶部)
            #    N字通常是 上升-下降-上升，关注的是价格的转折
            #    这里我们简化为寻找轮廓重心的y坐标变化
            m1 = cv2.moments(c1)
            m2 = cv2.moments(c2)
            m3 = cv2.moments(c3)

            if m1['m00'] == 0 or m2['m00'] == 0 or m3['m00'] == 0: continue # 避免除以0

            cy1 = int(m1['m01']/m1['m00'])
            cy2 = int(m2['m01']/m2['m00'])
            cy3 = int(m3['m01']/m3['m00'])

            # 假设N字是先跌后涨再跌（对应图像是先高后低再高，因为y轴向下为正）
            # 或者先涨后跌再涨 (对应图像是先低后高再低)
            # N字形态: 点1(高) -> 点2(低) -> 点3(更高) -> 点4(次低)
            # 图像上: y1(小) -> y2(大) -> y3(更小) -> y4(次大)
            # 简化为三个点: y1(高点) - y2(低点) - y3(次高点)
            # 对应图像: y_c1 (小) - y_c2 (大) - y_c3 (比y_c2小，但可能比y_c1小或大)
            
            # 条件1: 第二个轮廓的重心y值大于第一个和第三个 (形成一个“谷”)
            # 条件2: 第三个轮廓的重心y值小于第一个 (N字的右肩低于左肩，或类似情况)
            # 这些条件非常粗略，仅为演示
            is_potential_n = False
            if cy2 > cy1 and cy2 > cy3: # c2是局部低点 (y值大)
                if cy3 < cy1: # c3高于c1 (y值小)
                    # 这可能是一个上升N字的一部分 (涨-跌-涨)
                    is_potential_n = True
            
            # 另一种N字 (跌-涨-跌)
            elif cy2 < cy1 and cy2 < cy3: # c2是局部高点 (y值小)
                if cy3 > cy1: # c3低于c1 (y值大)
                    is_potential_n = True

            if is_potential_n:
                # 获取这三个轮廓覆盖的K线范围 (近似)
                # 需要一种方法将图像坐标映射回K线索引
                # 暂时用轮廓的x范围来粗略估计
                start_x = x1
                end_x = x3 + w3
                # 假设K线在图像中均匀分布
                img_width = img_bgr.shape[1]
                num_bars = len(bars)
                start_idx = int((start_x / img_width) * num_bars)
                end_idx = int((end_x / img_width) * num_bars)
                end_idx = min(end_idx, num_bars - 1)
                start_idx = max(0, start_idx)

                if end_idx > start_idx:
                    identified_n_shapes.append({
                        "start_idx": start_idx,
                        "end_idx": end_idx,
                        "start_dt": bars[start_idx].dt,
                        "end_dt": bars[end_idx].dt,
                        "details": f"Potential N-shape based on contours {i}-{i+2}"
                    })
                    # 在图上标记这三个轮廓
                    cv2.drawContours(img_contours, [c1, c2, c3], -1, (0,0,255), 2) # 用红色标记

    if identified_n_shapes:
        print(f"识别到 {len(identified_n_shapes)} 个潜在的N字形态 (基于简化轮廓分析)")
        for shape in identified_n_shapes:
            print(shape)
        if output_image_path:
             cv2.imwrite(f"{output_image_path}_identified_N.png", img_contours)
             print(f"标记潜在N形态的图像已保存至: {output_image_path}_identified_N.png")
    else:
        print("未能通过简化轮廓分析识别出N字形态。")

    print("N字形态识别 (OpenCV) 初步尝试完成。注意：此方法非常简化，准确性有限。")
    return identified_n_shapes


# 主函数/示例用法
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="N字形态识别 - OpenCV版本")
    parser.add_argument("file_path", type=str, help="K线数据文件路径 (CSV或TXT)")
    parser.add_argument("--symbol", type=str, default="示例股票", help="股票代码或名称")
    parser.add_argument("--freq", type=str, default="D", choices=[f.name for f in Freq],
                        help="K线周期 (例如 D, W, F30)")
    parser.add_argument("--output_dir", type=str, default="./n_shape_output", help="图像输出目录")

    args = parser.parse_args()

    if not os.path.exists(args.file_path):
        print(f"错误：文件 {args.file_path} 不存在")
        sys.exit(1)

    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    input_freq = Freq[args.freq]
    raw_bars = read_kline_data(args.file_path, symbol=args.symbol, freq=input_freq)

    if not raw_bars:
        print("未能读取K线数据，程序退出。")
        sys.exit(1)

    # 生成日K线
    daily_bars = aggregate_to_daily(raw_bars)
    if daily_bars:
        print(f"\n--- 日K线 N字形态识别 ---")
        daily_n_shapes_output_path = os.path.join(args.output_dir, f"{args.symbol}_daily_n_shapes.png")
        find_n_shape_cv(daily_bars, output_image_path=daily_n_shapes_output_path)

    # 生成周K线
    weekly_bars = aggregate_to_weekly(raw_bars)
    if weekly_bars:
        print(f"\n--- 周K线 N字形态识别 ---")
        weekly_n_shapes_output_path = os.path.join(args.output_dir, f"{args.symbol}_weekly_n_shapes.png")
        find_n_shape_cv(weekly_bars, output_image_path=weekly_n_shapes_output_path)

    print("\nN字形态识别处理完成。")