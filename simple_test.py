# -*- coding: utf-8 -*-
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# 直接导入需要的模块，避开__init__.py
sys.path.append("D:/czsc-master")
from czsc.enum import Freq, Direction, Mark
from czsc.objects import RawBar

# 打印一些基本信息
print("CZSC基本枚举类型:")
print(f"频率类型: {[f.value for f in Freq]}")
print(f"方向类型: {[d.value for d in Direction]}")
print(f"标记类型: {[m.value for m in Mark]}")

# 创建一些示例K线数据
print("\n创建示例K线数据:")
bar = RawBar(
    symbol="000001.SH", 
    id=1, 
    dt=datetime(2023, 1, 1), 
    freq=Freq.D, 
    open=100, 
    close=101, 
    high=102, 
    low=99, 
    vol=1000, 
    amount=100000
)

print(f"K线信息: 股票={bar.symbol}, 日期={bar.dt}, 开盘={bar.open}, 收盘={bar.close}, 最高={bar.high}, 最低={bar.low}")
print(f"上影线长度: {bar.upper}")
print(f"下影线长度: {bar.lower}")
print(f"实体长度: {bar.solid}")
print(f"K线颜色: {'红色' if bar.solid > 0 else '绿色'}")
